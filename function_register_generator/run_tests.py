#!/usr/bin/env python3
"""
Test runner script for the Function Register Generator platform.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path


class TestRunner:
    """Comprehensive test runner for the platform."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.tests_dir = self.project_root / "tests"
        
    def run_backend_unit_tests(self, verbose=False):
        """Run backend unit tests."""
        print("🧪 Running Backend Unit Tests...")
        
        os.chdir(self.backend_dir)
        
        cmd = ["python", "-m", "pytest", "tests/", "-v" if verbose else "-q"]
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Backend unit tests passed!")
        else:
            print("❌ Backend unit tests failed!")
            print(result.stdout)
            print(result.stderr)
        
        return result.returncode == 0
    
    def run_frontend_unit_tests(self, verbose=False):
        """Run frontend unit tests."""
        print("🧪 Running Frontend Unit Tests...")
        
        os.chdir(self.frontend_dir)
        
        cmd = ["flutter", "test"]
        if verbose:
            cmd.append("--verbose")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Frontend unit tests passed!")
        else:
            print("❌ Frontend unit tests failed!")
            print(result.stdout)
            print(result.stderr)
        
        return result.returncode == 0
    
    def run_integration_tests(self, verbose=False):
        """Run integration tests."""
        print("🔗 Running Integration Tests...")
        
        os.chdir(self.backend_dir)
        
        cmd = ["python", "-m", "pytest", "tests/integration/", "-v" if verbose else "-q"]
        cmd.extend(["--tb=short"])
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Integration tests passed!")
        else:
            print("❌ Integration tests failed!")
            print(result.stdout)
            print(result.stderr)
        
        return result.returncode == 0
    
    def run_e2e_tests(self, verbose=False, headless=True):
        """Run end-to-end tests."""
        print("🌐 Running End-to-End Tests...")
        
        # Check if services are running
        if not self.check_services_running():
            print("❌ Services not running. Please start the application first.")
            return False
        
        os.chdir(self.tests_dir)
        
        cmd = ["python", "-m", "pytest", "e2e/", "-v" if verbose else "-q"]
        if headless:
            cmd.extend(["--headless"])
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ End-to-end tests passed!")
        else:
            print("❌ End-to-end tests failed!")
            print(result.stdout)
            print(result.stderr)
        
        return result.returncode == 0
    
    def run_performance_tests(self, verbose=False):
        """Run performance tests."""
        print("⚡ Running Performance Tests...")
        
        # Check if services are running
        if not self.check_services_running():
            print("❌ Services not running. Please start the application first.")
            return False
        
        os.chdir(self.tests_dir)
        
        cmd = ["python", "-m", "pytest", "performance/", "-v" if verbose else "-q"]
        cmd.extend(["--tb=short", "-s"])  # Show print statements for performance metrics
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Performance tests passed!")
        else:
            print("❌ Performance tests failed!")
            print(result.stdout)
            print(result.stderr)
        
        return result.returncode == 0
    
    def run_security_tests(self, verbose=False):
        """Run security tests."""
        print("🔒 Running Security Tests...")
        
        os.chdir(self.tests_dir)
        
        cmd = ["python", "-m", "pytest", "security/", "-v" if verbose else "-q"]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Security tests passed!")
        else:
            print("❌ Security tests failed!")
            print(result.stdout)
            print(result.stderr)
        
        return result.returncode == 0
    
    def run_load_tests(self, users=10, duration=60):
        """Run load tests using Locust."""
        print(f"🚀 Running Load Tests ({users} users, {duration}s)...")
        
        # Check if services are running
        if not self.check_services_running():
            print("❌ Services not running. Please start the application first.")
            return False
        
        os.chdir(self.tests_dir)
        
        cmd = [
            "locust",
            "-f", "performance/test_api_performance.py",
            "--headless",
            "--users", str(users),
            "--spawn-rate", "2",
            "--run-time", f"{duration}s",
            "--host", "http://localhost:8000"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Load tests completed!")
            print(result.stdout)
        else:
            print("❌ Load tests failed!")
            print(result.stderr)
        
        return result.returncode == 0
    
    def check_services_running(self):
        """Check if backend and frontend services are running."""
        import requests
        
        try:
            # Check backend
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code != 200:
                return False
            
            # Check frontend (basic connectivity)
            response = requests.get("http://localhost:8080", timeout=5)
            if response.status_code != 200:
                return False
            
            return True
        except requests.RequestException:
            return False
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        print("📊 Generating Test Report...")
        
        report_dir = self.project_root / "test_reports"
        report_dir.mkdir(exist_ok=True)
        
        # Generate coverage report
        os.chdir(self.backend_dir)
        subprocess.run([
            "python", "-m", "pytest", "tests/",
            "--cov=app",
            "--cov-report=html:../test_reports/coverage_html",
            "--cov-report=xml:../test_reports/coverage.xml",
            "--junit-xml=../test_reports/junit.xml"
        ], capture_output=True)
        
        print(f"✅ Test report generated in {report_dir}")
        print(f"   - Coverage HTML: {report_dir}/coverage_html/index.html")
        print(f"   - Coverage XML: {report_dir}/coverage.xml")
        print(f"   - JUnit XML: {report_dir}/junit.xml")
    
    def run_all_tests(self, verbose=False, skip_e2e=False, skip_performance=False):
        """Run all test suites."""
        print("🚀 Running All Tests...")
        print("=" * 60)
        
        results = {}
        
        # Unit tests
        results['backend_unit'] = self.run_backend_unit_tests(verbose)
        results['frontend_unit'] = self.run_frontend_unit_tests(verbose)
        
        # Integration tests
        results['integration'] = self.run_integration_tests(verbose)
        
        # E2E tests (optional)
        if not skip_e2e:
            results['e2e'] = self.run_e2e_tests(verbose)
        
        # Performance tests (optional)
        if not skip_performance:
            results['performance'] = self.run_performance_tests(verbose)
        
        # Security tests
        results['security'] = self.run_security_tests(verbose)
        
        # Generate report
        self.generate_test_report()
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 Test Results Summary:")
        print("=" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_type, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"  {test_type.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall: {passed_tests}/{total_tests} test suites passed")
        
        if passed_tests == total_tests:
            print("🎉 All tests passed! The platform is ready for deployment.")
            return True
        else:
            print("⚠️  Some tests failed. Please review and fix issues before deployment.")
            return False
    
    def setup_test_environment(self):
        """Setup test environment."""
        print("🔧 Setting up test environment...")
        
        # Install backend dependencies
        os.chdir(self.backend_dir)
        subprocess.run(["pip", "install", "-r", "requirements.txt"], check=True)
        subprocess.run(["pip", "install", "pytest", "pytest-cov", "pytest-asyncio"], check=True)
        
        # Install frontend dependencies
        os.chdir(self.frontend_dir)
        subprocess.run(["flutter", "pub", "get"], check=True)
        
        # Install E2E test dependencies
        subprocess.run(["pip", "install", "selenium", "locust"], check=True)
        
        print("✅ Test environment setup complete!")
    
    def clean_test_artifacts(self):
        """Clean up test artifacts."""
        print("🧹 Cleaning test artifacts...")
        
        # Remove coverage files
        coverage_files = [
            self.backend_dir / ".coverage",
            self.backend_dir / "htmlcov",
            self.project_root / "test_reports"
        ]
        
        for file_path in coverage_files:
            if file_path.exists():
                if file_path.is_file():
                    file_path.unlink()
                else:
                    import shutil
                    shutil.rmtree(file_path)
        
        # Remove pytest cache
        pytest_cache = self.backend_dir / ".pytest_cache"
        if pytest_cache.exists():
            import shutil
            shutil.rmtree(pytest_cache)
        
        print("✅ Test artifacts cleaned!")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test runner for Function Register Generator")
    
    parser.add_argument("--type", choices=[
        "unit", "integration", "e2e", "performance", "security", "load", "all"
    ], default="all", help="Type of tests to run")
    
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--skip-e2e", action="store_true", help="Skip E2E tests")
    parser.add_argument("--skip-performance", action="store_true", help="Skip performance tests")
    parser.add_argument("--headless", action="store_true", default=True, help="Run E2E tests in headless mode")
    parser.add_argument("--setup", action="store_true", help="Setup test environment")
    parser.add_argument("--clean", action="store_true", help="Clean test artifacts")
    parser.add_argument("--users", type=int, default=10, help="Number of users for load tests")
    parser.add_argument("--duration", type=int, default=60, help="Duration for load tests (seconds)")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.setup:
        runner.setup_test_environment()
        return
    
    if args.clean:
        runner.clean_test_artifacts()
        return
    
    # Run tests based on type
    success = True
    
    if args.type == "unit":
        success = (runner.run_backend_unit_tests(args.verbose) and 
                  runner.run_frontend_unit_tests(args.verbose))
    elif args.type == "integration":
        success = runner.run_integration_tests(args.verbose)
    elif args.type == "e2e":
        success = runner.run_e2e_tests(args.verbose, args.headless)
    elif args.type == "performance":
        success = runner.run_performance_tests(args.verbose)
    elif args.type == "security":
        success = runner.run_security_tests(args.verbose)
    elif args.type == "load":
        success = runner.run_load_tests(args.users, args.duration)
    elif args.type == "all":
        success = runner.run_all_tests(args.verbose, args.skip_e2e, args.skip_performance)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
