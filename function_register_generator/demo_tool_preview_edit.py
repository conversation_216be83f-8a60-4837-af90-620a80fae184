#!/usr/bin/env python3
"""
工具预览和编辑功能演示脚本
"""
import asyncio
import json
from datetime import datetime

async def demo_tool_preview_edit():
    """演示工具预览和编辑功能"""
    print("=== 工具预览和编辑平台演示 ===")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 导入必要的模块
    try:
        from backend.routers.tools import (
            create_tool_draft, get_tool_draft, edit_tool_draft,
            publish_tool_draft, validate_tool_realtime, list_tool_drafts,
            delete_tool_draft, ToolDraftRequest, ToolEditRequest,
            ToolPublishRequest, ValidationRequest, _validate_single_field,
            _calculate_validation_score, _validate_tool_definition
        )
        print("✅ 成功导入工具预览编辑模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    print("\n" + "="*70)
    print("1. 工具草稿创建演示")
    print("="*70)
    
    # 1. 创建工具草稿
    sample_tool_definitions = [
        {
            "name": "天气查询工具",
            "definition": {
                "toolId": "http.weather_api.v1",
                "displayName": "天气查询API",
                "category": "weather",
                "transport": "http",
                "runtime": {
                    "transport": "http",
                    "endpoint": "https://api.openweathermap.org/data/2.5/weather"
                },
                "inputsDeveloperSchema": {
                    "type": "object",
                    "properties": {
                        "city": {"type": "string", "description": "城市名称"},
                        "appid": {"type": "string", "description": "API密钥"}
                    },
                    "required": ["city", "appid"]
                },
                "outputsSchema": {
                    "type": "object",
                    "properties": {
                        "temperature": {"type": "number", "description": "温度"},
                        "humidity": {"type": "number", "description": "湿度"},
                        "description": {"type": "string", "description": "天气描述"}
                    }
                },
                "descriptionUser": "获取指定城市的实时天气信息",
                "examples": []
            }
        },
        {
            "name": "密码生成器",
            "definition": {
                "toolId": "python.password_gen.v1",
                "displayName": "安全密码生成器",
                "category": "security",
                "transport": "python",
                "runtime": {
                    "transport": "python",
                    "code": """
def generate_password(length=12, include_symbols=True):
    import random
    import string
    
    chars = string.ascii_letters + string.digits
    if include_symbols:
        chars += "!@#$%^&*"
    
    return ''.join(random.choice(chars) for _ in range(length))
""",
                    "entry_function": "generate_password"
                },
                "inputsDeveloperSchema": {
                    "type": "object",
                    "properties": {
                        "length": {"type": "integer", "minimum": 4, "maximum": 128, "default": 12},
                        "include_symbols": {"type": "boolean", "default": True}
                    }
                },
                "outputsSchema": {
                    "type": "object",
                    "properties": {
                        "password": {"type": "string", "description": "生成的密码"},
                        "strength": {"type": "string", "description": "密码强度"}
                    }
                },
                "descriptionUser": "生成指定长度和复杂度的安全密码"
            }
        }
    ]
    
    created_drafts = []
    
    for tool_case in sample_tool_definitions:
        print(f"\n📝 创建 {tool_case['name']} 草稿:")
        try:
            draft_request = ToolDraftRequest(
                tool_definition=tool_case["definition"],
                draft_name=f"{tool_case['name']}草稿",
                description=f"用于测试的{tool_case['name']}草稿",
                tags=["demo", tool_case["definition"]["category"]],
                auto_save=True,
                expiry_hours=24
            )
            
            result = await create_tool_draft(draft_request)
            
            if result.success:
                print(f"   ✅ 草稿创建成功")
                print(f"   草稿ID: {result.draft_id}")
                print(f"   编辑URL: {result.edit_url}")
                print(f"   过期时间: {result.expiry_time}")
                print(f"   版本: {result.draft_data['version']}")
                
                created_drafts.append(result.draft_id)
            else:
                print(f"   ❌ 草稿创建失败: {result.message}")
                
        except Exception as e:
            print(f"   ❌ 草稿创建异常: {e}")
    
    print("\n" + "="*70)
    print("2. 草稿列表和查询演示")
    print("="*70)
    
    # 2. 草稿列表查询
    print("\n📋 获取草稿列表:")
    try:
        result = await list_tool_drafts(skip=0, limit=10)
        
        if result["success"]:
            print(f"   ✅ 获取成功，共 {result['total']} 个草稿")
            for draft in result["drafts"][:5]:  # 显示前5个
                print(f"   - {draft['draft_id']}: {draft['draft_name']} ({draft['tool_type']})")
                print(f"     标签: {', '.join(draft['tags'])}")
                print(f"     创建时间: {draft['created_time'][:19]}")
        else:
            print(f"   ❌ 获取失败")
            
    except Exception as e:
        print(f"   ❌ 获取异常: {e}")
    
    # 带过滤条件的查询
    print("\n🔍 带过滤条件的草稿查询:")
    try:
        result = await list_tool_drafts(
            skip=0, 
            limit=5, 
            status="draft", 
            tags="demo,test"
        )
        
        if result["success"]:
            print(f"   ✅ 过滤查询成功，找到 {len(result['drafts'])} 个匹配草稿")
            for draft in result["drafts"]:
                print(f"   - {draft['draft_name']}: {draft['tool_type']}")
        else:
            print(f"   ❌ 过滤查询失败")
            
    except Exception as e:
        print(f"   ❌ 过滤查询异常: {e}")
    
    print("\n" + "="*70)
    print("3. 草稿编辑演示")
    print("="*70)
    
    # 3. 草稿编辑
    if created_drafts:
        draft_id = created_drafts[0]
        print(f"\n✏️ 编辑草稿: {draft_id}")
        
        # 获取原始草稿
        try:
            original_draft = await get_tool_draft(draft_id)
            if original_draft["success"]:
                print(f"   📖 原始草稿获取成功")
                original_tool = original_draft["draft_data"]["tool_definition"]
                print(f"   原始名称: {original_tool.get('displayName')}")
                print(f"   原始描述: {original_tool.get('descriptionUser', '')[:50]}...")
            else:
                print(f"   ❌ 原始草稿获取失败")
                
        except Exception as e:
            print(f"   ❌ 原始草稿获取异常: {e}")
        
        # 执行编辑
        edit_scenarios = [
            {
                "name": "基础信息更新",
                "modifications": {
                    "displayName": "高级天气查询API",
                    "descriptionUser": "获取指定城市的详细天气信息，包括温度、湿度、风速、气压等完整数据",
                    "category": "weather_advanced"
                }
            },
            {
                "name": "输入参数扩展",
                "modifications": {
                    "inputsDeveloperSchema": {
                        "type": "object",
                        "properties": {
                            "city": {"type": "string", "description": "城市名称"},
                            "country": {"type": "string", "description": "国家代码"},
                            "units": {"type": "string", "enum": ["metric", "imperial"], "description": "单位制"},
                            "lang": {"type": "string", "description": "语言代码", "default": "zh"}
                        },
                        "required": ["city"]
                    }
                }
            },
            {
                "name": "运行时配置优化",
                "modifications": {
                    "runtime": {
                        "transport": "http",
                        "endpoint": "https://api.openweathermap.org/data/2.5/weather",
                        "method": "GET",
                        "timeout": 10,
                        "retry_count": 3
                    }
                }
            }
        ]
        
        for scenario in edit_scenarios:
            print(f"\n   🔧 {scenario['name']}:")
            try:
                edit_request = ToolEditRequest(
                    modifications=scenario["modifications"],
                    validation_level="full",
                    auto_save=True,
                    create_backup=True
                )
                
                result = await edit_tool_draft(draft_id, edit_request)
                
                if result.success:
                    print(f"      ✅ 编辑成功")
                    print(f"      备份ID: {result.backup_id}")
                    
                    if result.validation_result:
                        validation = result.validation_result
                        print(f"      验证结果: {'✅ 通过' if validation.get('success') else '❌ 失败'}")
                        if validation.get("warnings"):
                            print(f"      警告: {len(validation['warnings'])} 个")
                        if validation.get("suggestions"):
                            print(f"      建议: {len(validation['suggestions'])} 个")
                    
                    # 显示关键修改
                    updated_def = result.updated_definition
                    if "displayName" in scenario["modifications"]:
                        print(f"      新名称: {updated_def.get('displayName')}")
                    if "inputsDeveloperSchema" in scenario["modifications"]:
                        props = updated_def.get("inputsDeveloperSchema", {}).get("properties", {})
                        print(f"      输入参数: {len(props)} 个")
                else:
                    print(f"      ❌ 编辑失败: {result.message}")
                    if result.errors:
                        for error in result.errors[:2]:
                            print(f"        错误: {error}")
                            
            except Exception as e:
                print(f"      ❌ 编辑异常: {e}")
    
    print("\n" + "="*70)
    print("4. 实时验证演示")
    print("="*70)
    
    # 4. 实时验证
    validation_test_cases = [
        {
            "name": "完整工具验证",
            "tool_definition": {
                "toolId": "test.complete.tool",
                "displayName": "完整测试工具",
                "transport": "http",
                "runtime": {"transport": "http", "endpoint": "https://api.test.com"},
                "inputsDeveloperSchema": {"type": "object", "properties": {"param1": {"type": "string"}}},
                "outputsSchema": {"type": "object", "properties": {"result": {"type": "string"}}},
                "descriptionUser": "这是一个完整的测试工具，用于演示验证功能"
            },
            "validation_type": "full"
        },
        {
            "name": "缺少字段的工具",
            "tool_definition": {
                "displayName": "不完整工具",
                "transport": "http"
                # 缺少 toolId, runtime 等
            },
            "validation_type": "basic"
        },
        {
            "name": "无效传输方式",
            "tool_definition": {
                "toolId": "test.invalid.transport",
                "displayName": "无效传输工具",
                "transport": "invalid_transport",
                "descriptionUser": "测试无效传输方式"
            },
            "validation_type": "schema"
        }
    ]
    
    for test_case in validation_test_cases:
        print(f"\n🔍 {test_case['name']}:")
        try:
            validation_request = ValidationRequest(
                tool_definition=test_case["tool_definition"],
                validation_type=test_case["validation_type"]
            )
            
            result = await validate_tool_realtime(validation_request)
            
            print(f"   验证结果: {'✅ 通过' if result.success else '❌ 失败'}")
            print(f"   验证分数: {result.validation_score:.2f}")
            
            if result.field_errors:
                print(f"   字段错误: {len(result.field_errors)} 个")
                for field, errors in list(result.field_errors.items())[:2]:
                    print(f"     - {field}: {errors[0]}")
            
            if result.global_errors:
                print(f"   全局错误: {len(result.global_errors)} 个")
                for error in result.global_errors[:2]:
                    print(f"     - {error}")
            
            if result.warnings:
                print(f"   警告: {len(result.warnings)} 个")
                for warning in result.warnings[:2]:
                    print(f"     - {warning}")
            
            if result.suggestions:
                print(f"   建议: {len(result.suggestions)} 个")
                for suggestion in result.suggestions[:2]:
                    print(f"     - {suggestion}")
                    
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")
    
    print("\n" + "="*70)
    print("5. 单字段验证演示")
    print("="*70)
    
    # 5. 单字段验证
    single_field_tests = [
        {
            "field": "toolId",
            "values": ["valid.tool.id", "", "ab", "very.long.tool.id.with.many.parts"]
        },
        {
            "field": "displayName", 
            "values": ["正常工具名称", "", "x" * 150, "简短名"]
        },
        {
            "field": "transport",
            "values": ["http", "python", "stdio", "invalid", ""]
        },
        {
            "field": "descriptionUser",
            "values": ["详细的工具描述，包含足够的信息", "短", "", "x" * 500]
        }
    ]
    
    for field_test in single_field_tests:
        print(f"\n🎯 {field_test['field']} 字段验证:")
        
        for value in field_test["values"]:
            try:
                tool_def = {field_test["field"]: value}
                field_errors, warnings, suggestions = await _validate_single_field(
                    tool_def, field_test["field"]
                )
                
                value_display = f"'{value}'" if value else "'空值'"
                if len(str(value)) > 20:
                    value_display = f"'{str(value)[:20]}...'"
                
                status = "✅" if not field_errors else "❌"
                print(f"   {status} {value_display}")
                
                if field_errors:
                    for error in field_errors.get(field_test["field"], []):
                        print(f"      错误: {error}")
                
                if warnings:
                    for warning in warnings[:1]:
                        print(f"      警告: {warning}")
                        
            except Exception as e:
                print(f"   ❌ 验证异常: {e}")
    
    print("\n" + "="*70)
    print("6. 工具发布演示")
    print("="*70)
    
    # 6. 工具发布
    if created_drafts:
        draft_id = created_drafts[0]
        print(f"\n🚀 发布草稿: {draft_id}")
        
        try:
            publish_request = ToolPublishRequest(
                publish_options={
                    "visibility": "public",
                    "category": "weather",
                    "tags": ["weather", "api", "demo"]
                },
                final_validation=True,
                cleanup_draft=True
            )
            
            result = await publish_tool_draft(draft_id, publish_request)
            
            if result.success:
                print(f"   ✅ 发布成功")
                print(f"   工具ID: {result.tool_id}")
                
                if result.published_tool:
                    published = result.published_tool
                    print(f"   发布名称: {published.get('displayName')}")
                    print(f"   发布时间: {published.get('publishedTime', '')[:19]}")
                    print(f"   状态: {published.get('status')}")
                
                if result.validation_result:
                    validation = result.validation_result
                    print(f"   最终验证: {'✅ 通过' if validation.get('success') else '❌ 失败'}")
            else:
                print(f"   ❌ 发布失败: {result.message}")
                if result.warnings:
                    for warning in result.warnings[:2]:
                        print(f"     警告: {warning}")
                        
        except Exception as e:
            print(f"   ❌ 发布异常: {e}")
    
    print("\n" + "="*70)
    print("7. 验证分数计算演示")
    print("="*70)
    
    # 7. 验证分数计算
    score_test_cases = [
        {
            "name": "完美工具",
            "field_errors": {},
            "global_errors": [],
            "warnings": []
        },
        {
            "name": "少量问题",
            "field_errors": {"field1": ["error1"]},
            "global_errors": [],
            "warnings": ["warning1"]
        },
        {
            "name": "中等问题",
            "field_errors": {"field1": ["error1"], "field2": ["error2"]},
            "global_errors": ["global_error"],
            "warnings": ["warning1", "warning2"]
        },
        {
            "name": "严重问题",
            "field_errors": {
                "field1": ["error1", "error2"],
                "field2": ["error3"],
                "field3": ["error4"]
            },
            "global_errors": ["global_error1", "global_error2"],
            "warnings": ["warning1", "warning2", "warning3", "warning4"]
        }
    ]
    
    print("\n📊 验证分数计算:")
    for test_case in score_test_cases:
        score = _calculate_validation_score(
            test_case["field_errors"],
            test_case["global_errors"],
            test_case["warnings"]
        )
        
        total_issues = (
            len(test_case["global_errors"]) + 
            sum(len(errors) for errors in test_case["field_errors"].values()) +
            len(test_case["warnings"])
        )
        
        print(f"   {test_case['name']}: {score:.1f} (问题数: {total_issues})")
    
    print("\n" + "="*70)
    print("8. 草稿清理演示")
    print("="*70)
    
    # 8. 草稿清理
    print("\n🗑️ 清理演示草稿:")
    for draft_id in created_drafts[1:]:  # 保留第一个，删除其他的
        try:
            result = await delete_tool_draft(draft_id)
            
            if result["success"]:
                print(f"   ✅ 删除草稿 {draft_id} 成功")
            else:
                print(f"   ❌ 删除草稿 {draft_id} 失败")
                
        except Exception as e:
            print(f"   ❌ 删除草稿 {draft_id} 异常: {e}")
    
    print("\n" + "="*70)
    print("演示完成")
    print("="*70)
    print("✅ 工具预览和编辑功能演示完成")
    print("🎯 主要特性:")
    print("   - 工具草稿创建和管理")
    print("   - 实时编辑和版本控制")
    print("   - 多级验证和错误提示")
    print("   - 单字段和全量验证")
    print("   - 自动备份和恢复")
    print("   - 简化的发布流程")
    print("   - 草稿过期和清理")
    print("   - 验证分数和质量评估")
    print("📝 注意: 实际使用时需要配置持久化存储")

async def demo_advanced_editing_features():
    """演示高级编辑功能"""
    print("\n" + "="*70)
    print("高级编辑功能演示")
    print("="*70)
    
    # 协作编辑功能
    print("\n👥 协作编辑功能:")
    collaboration_features = [
        "多用户同时编辑同一草稿",
        "实时同步编辑状态",
        "冲突检测和解决",
        "编辑历史和版本对比",
        "评论和审核系统"
    ]
    
    for feature in collaboration_features:
        print(f"   🔧 {feature}")
    
    # 高级验证功能
    print("\n🔍 高级验证功能:")
    advanced_validations = [
        "语义一致性检查",
        "性能影响评估",
        "安全风险扫描",
        "最佳实践建议",
        "兼容性检查"
    ]
    
    for validation in advanced_validations:
        print(f"   ✅ {validation}")
    
    # 智能编辑助手
    print("\n🤖 智能编辑助手:")
    ai_features = [
        "自动补全工具定义",
        "智能错误修复建议",
        "代码质量优化",
        "文档自动生成",
        "测试用例推荐"
    ]
    
    for feature in ai_features:
        print(f"   🧠 {feature}")

if __name__ == "__main__":
    print("启动工具预览和编辑演示...")
    asyncio.run(demo_tool_preview_edit())
    asyncio.run(demo_advanced_editing_features())