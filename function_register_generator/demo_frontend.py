#!/usr/bin/env python3
"""
前端架构演示脚本
"""
import os
import subprocess
import sys
from pathlib import Path

def main():
    """演示前端架构"""
    print("🚀 Flutter Web 前端架构演示")
    print("=" * 50)
    
    # 检查Flutter是否安装
    try:
        result = subprocess.run(['flutter', '--version'], 
                              capture_output=True, text=True, check=True)
        print("✅ Flutter 已安装")
        print(f"版本信息: {result.stdout.split()[1]}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Flutter 未安装或不在PATH中")
        print("请安装Flutter: https://flutter.dev/docs/get-started/install")
        return
    
    # 检查项目结构
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend 目录不存在")
        return
    
    print("\n📁 项目结构:")
    print_directory_tree(frontend_dir, max_depth=3)
    
    # 检查pubspec.yaml
    pubspec_file = frontend_dir / "pubspec.yaml"
    if pubspec_file.exists():
        print("\n📦 依赖包:")
        with open(pubspec_file, 'r', encoding='utf-8') as f:
            content = f.read()
            dependencies_section = False
            for line in content.split('\n'):
                if line.strip() == 'dependencies:':
                    dependencies_section = True
                    continue
                elif line.strip() == 'dev_dependencies:':
                    dependencies_section = False
                    continue
                elif dependencies_section and line.strip() and not line.startswith(' '):
                    dependencies_section = False
                
                if dependencies_section and line.strip() and line.startswith('  '):
                    package = line.strip().split(':')[0]
                    if package != 'flutter':
                        print(f"  ✅ {package}")
    
    # 检查核心文件
    print("\n🔧 核心架构文件:")
    core_files = [
        "lib/main.dart",
        "lib/core/app.dart",
        "lib/core/config/app_config.dart",
        "lib/core/router/app_router.dart",
        "lib/core/theme/app_theme.dart",
        "lib/core/network/dio_client.dart",
        "lib/core/providers/theme_provider.dart",
        "lib/core/responsive/responsive_layout.dart",
        "lib/shared/presentation/widgets/app_shell.dart",
    ]
    
    for file_path in core_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
    
    # 检查功能模块
    print("\n🎯 功能模块:")
    feature_modules = [
        "lib/features/home/<USER>/pages/home_page.dart",
        "lib/features/tools/presentation/pages/tools_page.dart",
        "lib/features/registration/presentation/pages/registration_page.dart",
        "lib/features/search/presentation/pages/search_page.dart",
        "lib/features/drafts/presentation/pages/drafts_page.dart",
    ]
    
    for file_path in feature_modules:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
    
    print("\n🎨 主题和响应式设计:")
    print("  ✅ Material 3 设计系统")
    print("  ✅ 深色/浅色主题切换")
    print("  ✅ 响应式布局 (Mobile/Tablet/Desktop)")
    print("  ✅ 自定义颜色系统")
    print("  ✅ Typography 系统")
    
    print("\n🌐 网络和状态管理:")
    print("  ✅ Dio HTTP 客户端")
    print("  ✅ 请求/响应拦截器")
    print("  ✅ 错误处理")
    print("  ✅ Riverpod 状态管理")
    print("  ✅ Go Router 路由管理")
    
    print("\n🚀 准备运行:")
    print("1. cd frontend")
    print("2. flutter pub get")
    print("3. flutter run -d chrome")
    
    # 尝试获取依赖
    print("\n📥 正在获取依赖...")
    try:
        os.chdir(frontend_dir)
        result = subprocess.run(['flutter', 'pub', 'get'], 
                              capture_output=True, text=True, check=True)
        print("✅ 依赖获取成功")
        
        # 检查是否可以构建
        print("\n🔨 检查构建状态...")
        result = subprocess.run(['flutter', 'analyze'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 代码分析通过")
        else:
            print("⚠️  代码分析发现问题:")
            print(result.stdout)
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖获取失败: {e}")
        print(e.stdout)
        print(e.stderr)

def print_directory_tree(path, prefix="", max_depth=3, current_depth=0):
    """打印目录树"""
    if current_depth >= max_depth:
        return
    
    items = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name))
    
    for i, item in enumerate(items):
        is_last = i == len(items) - 1
        current_prefix = "└── " if is_last else "├── "
        print(f"{prefix}{current_prefix}{item.name}")
        
        if item.is_dir() and not item.name.startswith('.'):
            extension = "    " if is_last else "│   "
            print_directory_tree(item, prefix + extension, max_depth, current_depth + 1)

if __name__ == "__main__":
    main()