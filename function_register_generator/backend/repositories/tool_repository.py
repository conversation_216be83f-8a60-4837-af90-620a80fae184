from typing import Dict, List, Optional, Any, Union
from elasticsearch import AsyncElasticsearch
import redis.asyncio as redis
import json
import logging
from datetime import datetime

from .base_repository import BaseRepository
from config import settings

logger = logging.getLogger(__name__)

class ToolRepository(BaseRepository):
    """工具文档仓库"""
    
    def get_index_name(self) -> str:
        return settings.es_index
    
    async def search_tools(
        self,
        query: str = "",
        category: Optional[str] = None,
        transport: Optional[str] = None,
        visibility: Optional[str] = None,
        user_facing: Optional[bool] = None,
        size: int = 20,
        from_: int = 0,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """搜索工具"""
        
        # 构建查询
        es_query = self._build_search_query(
            query, category, transport, visibility, user_facing
        )
        
        # 排序
        sort = [
            "_score",
            {"updatedTime": {"order": "desc"}}
        ]
        
        # 高亮
        highlight = {
            "fields": {
                "displayName": {},
                "descriptionUser": {},
                "content": {}
            }
        }
        
        # 执行搜索
        body = {
            "query": es_query,
            "highlight": highlight,
            "size": size,
            "from": from_,
            "sort": sort
        }
        
        try:
            response = await self.es_client.search(
                index=self.index_name,
                body=body
            )
            
            # 处理结果
            hits = []
            for hit in response["hits"]["hits"]:
                tool = hit["_source"]
                tool["_score"] = hit["_score"]
                
                # 添加高亮信息
                if "highlight" in hit:
                    tool["_highlight"] = hit["highlight"]
                
                hits.append(tool)
            
            return {
                "hits": hits,
                "total": response["hits"]["total"]["value"],
                "took": response["took"],
                "max_score": response["hits"]["max_score"]
            }
            
        except Exception as e:
            logger.error(f"Tool search failed: {e}")
            raise
    
    async def get_by_category(self, category: str, size: int = 50) -> List[Dict[str, Any]]:
        """按分类获取工具"""
        query = {
            "bool": {
                "must": [
                    {"term": {"category": category}},
                    {"term": {"deleteFlag": 0}}
                ]
            }
        }
        
        result = await self.search(query, size=size, use_cache=True)
        return result["hits"]
    
    async def get_by_transport(self, transport: str, size: int = 50) -> List[Dict[str, Any]]:
        """按传输方式获取工具"""
        query = {
            "bool": {
                "must": [
                    {"term": {"capabilities": transport}},
                    {"term": {"deleteFlag": 0}}
                ]
            }
        }
        
        result = await self.search(query, size=size, use_cache=True)
        return result["hits"]
    
    async def get_user_facing_tools(self, size: int = 50) -> List[Dict[str, Any]]:
        """获取面向用户的工具"""
        query = {
            "bool": {
                "must": [
                    {"term": {"metadata.user_facing": True}},
                    {"term": {"deleteFlag": 0}}
                ]
            }
        }
        
        result = await self.search(query, size=size, use_cache=True)
        return result["hits"]
    
    async def get_recent_tools(self, days: int = 7, size: int = 20) -> List[Dict[str, Any]]:
        """获取最近创建的工具"""
        from datetime import datetime, timedelta
        
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        query = {
            "bool": {
                "must": [
                    {"range": {"createdTime": {"gte": since_date}}},
                    {"term": {"deleteFlag": 0}}
                ]
            }
        }
        
        sort = [{"createdTime": {"order": "desc"}}]
        
        result = await self.search(query, size=size, sort=sort, use_cache=True)
        return result["hits"]
    
    async def get_popular_tools(self, size: int = 20) -> List[Dict[str, Any]]:
        """获取热门工具（按更新时间排序）"""
        query = {
            "bool": {
                "must": [
                    {"term": {"deleteFlag": 0}},
                    {"term": {"visibility": "public"}}
                ]
            }
        }
        
        sort = [{"updatedTime": {"order": "desc"}}]
        
        result = await self.search(query, size=size, sort=sort, use_cache=True)
        return result["hits"]
    
    async def get_categories(self) -> List[Dict[str, Any]]:
        """获取所有分类及其统计"""
        try:
            # 尝试从缓存获取
            cache_key = f"{self.index_name}:categories"
            if self.redis_client:
                cached = await self.redis_client.get(cache_key)
                if cached:
                    return json.loads(cached)
            
            # 聚合查询
            body = {
                "size": 0,
                "query": {
                    "term": {"deleteFlag": 0}
                },
                "aggs": {
                    "categories": {
                        "terms": {
                            "field": "category",
                            "size": 100
                        }
                    }
                }
            }
            
            response = await self.es_client.search(
                index=self.index_name,
                body=body
            )
            
            categories = []
            for bucket in response["aggregations"]["categories"]["buckets"]:
                categories.append({
                    "name": bucket["key"],
                    "count": bucket["doc_count"]
                })
            
            # 缓存结果
            if self.redis_client:
                await self.redis_client.setex(
                    cache_key,
                    300,  # 5分钟缓存
                    json.dumps(categories)
                )
            
            return categories
            
        except Exception as e:
            logger.error(f"Failed to get categories: {e}")
            return []
    
    async def get_transports(self) -> List[Dict[str, Any]]:
        """获取所有传输方式及其统计"""
        try:
            body = {
                "size": 0,
                "query": {
                    "term": {"deleteFlag": 0}
                },
                "aggs": {
                    "transports": {
                        "terms": {
                            "field": "capabilities",
                            "size": 50
                        }
                    }
                }
            }
            
            response = await self.es_client.search(
                index=self.index_name,
                body=body
            )
            
            transports = []
            for bucket in response["aggregations"]["transports"]["buckets"]:
                transports.append({
                    "name": bucket["key"],
                    "count": bucket["doc_count"]
                })
            
            return transports
            
        except Exception as e:
            logger.error(f"Failed to get transports: {e}")
            return []
    
    async def get_suggestions(self, prefix: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取搜索建议"""
        try:
            # 搜索工具名称、别名、分类
            query = {
                "bool": {
                    "should": [
                        {"prefix": {"displayName": prefix}},
                        {"prefix": {"aliases": prefix}},
                        {"prefix": {"category": prefix}}
                    ],
                    "must": [
                        {"term": {"deleteFlag": 0}}
                    ]
                }
            }
            
            result = await self.search(
                query, 
                size=limit, 
                source=["displayName", "toolId", "category", "aliases"],
                use_cache=True
            )
            
            suggestions = []
            for hit in result["hits"]:
                suggestions.append({
                    "text": hit["displayName"],
                    "type": "tool_name",
                    "toolId": hit["toolId"],
                    "category": hit.get("category")
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to get suggestions: {e}")
            return []
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取工具统计信息"""
        try:
            # 尝试从缓存获取
            cache_key = f"{self.index_name}:stats"
            if self.redis_client:
                cached = await self.redis_client.get(cache_key)
                if cached:
                    return json.loads(cached)
            
            # 聚合查询
            body = {
                "size": 0,
                "query": {
                    "term": {"deleteFlag": 0}
                },
                "aggs": {
                    "total_tools": {
                        "value_count": {"field": "toolId"}
                    },
                    "categories": {
                        "cardinality": {"field": "category"}
                    },
                    "visibility": {
                        "terms": {"field": "visibility"}
                    },
                    "transports": {
                        "terms": {"field": "capabilities"}
                    },
                    "user_facing": {
                        "terms": {"field": "metadata.user_facing"}
                    }
                }
            }
            
            response = await self.es_client.search(
                index=self.index_name,
                body=body
            )
            
            aggs = response["aggregations"]
            
            stats = {
                "total_tools": aggs["total_tools"]["value"],
                "categories_count": aggs["categories"]["value"],
                "visibility": {
                    bucket["key"]: bucket["doc_count"] 
                    for bucket in aggs["visibility"]["buckets"]
                },
                "transports": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggs["transports"]["buckets"]
                },
                "user_facing": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggs["user_facing"]["buckets"]
                }
            }
            
            # 缓存结果
            if self.redis_client:
                await self.redis_client.setex(
                    cache_key,
                    300,  # 5分钟缓存
                    json.dumps(stats, default=str)
                )
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {}
    
    async def find_similar_tools(self, tool_id: str, size: int = 5) -> List[Dict[str, Any]]:
        """查找相似工具"""
        try:
            # 获取原工具
            tool = await self.get_by_id(tool_id)
            if not tool:
                return []
            
            # 构建相似性查询
            query = {
                "bool": {
                    "should": [
                        {"match": {"category": tool.get("category", "")}},
                        {"match": {"capabilities": tool.get("capabilities", [])}},
                        {"match": {"content": tool.get("content", "")}}
                    ],
                    "must_not": [
                        {"term": {"toolId": tool_id}}
                    ],
                    "must": [
                        {"term": {"deleteFlag": 0}}
                    ]
                }
            }
            
            result = await self.search(query, size=size, use_cache=True)
            return result["hits"]
            
        except Exception as e:
            logger.error(f"Failed to find similar tools: {e}")
            return []
    
    def _build_search_query(
        self,
        query: str = "",
        category: Optional[str] = None,
        transport: Optional[str] = None,
        visibility: Optional[str] = None,
        user_facing: Optional[bool] = None
    ) -> Dict[str, Any]:
        """构建搜索查询"""
        
        must_queries = []
        filter_queries = []
        
        # 基础过滤：排除已删除的工具
        filter_queries.append({"term": {"deleteFlag": 0}})
        
        # 文本搜索
        if query:
            must_queries.append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "displayName^3",
                        "descriptionUser^2",
                        "content",
                        "aliases^2",
                        "category"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        else:
            must_queries.append({"match_all": {}})
        
        # 分类筛选
        if category:
            filter_queries.append({"term": {"category": category}})
        
        # 传输方式筛选
        if transport:
            filter_queries.append({"term": {"capabilities": transport}})
        
        # 可见性筛选
        if visibility:
            filter_queries.append({"term": {"visibility": visibility}})
        
        # 用户面向筛选
        if user_facing is not None:
            filter_queries.append({"term": {"metadata.user_facing": user_facing}})
        
        # 构建完整查询
        es_query = {
            "bool": {
                "must": must_queries,
                "filter": filter_queries
            }
        }
        
        return es_query

# 全局工具仓库实例
_tool_repository: Optional[ToolRepository] = None

def get_tool_repository(
    es_client: AsyncElasticsearch, 
    redis_client: Optional[redis.Redis] = None
) -> ToolRepository:
    """获取工具仓库实例"""
    global _tool_repository
    if _tool_repository is None:
        _tool_repository = ToolRepository(es_client, redis_client)
    return _tool_repository