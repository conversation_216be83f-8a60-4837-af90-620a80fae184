from typing import Dict, List, Optional, Any, Union
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class QueryType(str, Enum):
    """查询类型枚举"""
    MATCH = "match"
    MATCH_ALL = "match_all"
    MULTI_MATCH = "multi_match"
    TERM = "term"
    TERMS = "terms"
    RANGE = "range"
    PREFIX = "prefix"
    WILDCARD = "wildcard"
    FUZZY = "fuzzy"
    BOOL = "bool"

class SortOrder(str, Enum):
    """排序顺序枚举"""
    ASC = "asc"
    DESC = "desc"

class QueryBuilder:
    """Elasticsearch 查询构建器"""
    
    def __init__(self):
        self.query = {"match_all": {}}
        self.filters = []
        self.must_not = []
        self.should = []
        self.sort_fields = []
        self.highlight_fields = {}
        self.aggregations = {}
        self.size = 20
        self.from_ = 0
        self.source_fields = None
        self.min_score = None
    
    def match(self, field: str, value: str, boost: Optional[float] = None) -> 'QueryBuilder':
        """添加 match 查询"""
        match_query = {"match": {field: {"query": value}}}
        if boost:
            match_query["match"][field]["boost"] = boost
        
        self.query = match_query
        return self
    
    def multi_match(
        self, 
        query: str, 
        fields: List[str], 
        type_: str = "best_fields",
        fuzziness: str = "AUTO"
    ) -> 'QueryBuilder':
        """添加 multi_match 查询"""
        self.query = {
            "multi_match": {
                "query": query,
                "fields": fields,
                "type": type_,
                "fuzziness": fuzziness
            }
        }
        return self
    
    def term(self, field: str, value: Any) -> 'QueryBuilder':
        """添加 term 查询"""
        self.query = {"term": {field: value}}
        return self
    
    def terms(self, field: str, values: List[Any]) -> 'QueryBuilder':
        """添加 terms 查询"""
        self.query = {"terms": {field: values}}
        return self
    
    def range_query(
        self, 
        field: str, 
        gte: Optional[Any] = None,
        lte: Optional[Any] = None,
        gt: Optional[Any] = None,
        lt: Optional[Any] = None
    ) -> 'QueryBuilder':
        """添加 range 查询"""
        range_params = {}
        if gte is not None:
            range_params["gte"] = gte
        if lte is not None:
            range_params["lte"] = lte
        if gt is not None:
            range_params["gt"] = gt
        if lt is not None:
            range_params["lt"] = lt
        
        self.query = {"range": {field: range_params}}
        return self
    
    def prefix(self, field: str, value: str) -> 'QueryBuilder':
        """添加 prefix 查询"""
        self.query = {"prefix": {field: value}}
        return self
    
    def wildcard(self, field: str, value: str) -> 'QueryBuilder':
        """添加 wildcard 查询"""
        self.query = {"wildcard": {field: value}}
        return self
    
    def fuzzy(self, field: str, value: str, fuzziness: str = "AUTO") -> 'QueryBuilder':
        """添加 fuzzy 查询"""
        self.query = {
            "fuzzy": {
                field: {
                    "value": value,
                    "fuzziness": fuzziness
                }
            }
        }
        return self
    
    def filter(self, field: str, value: Any) -> 'QueryBuilder':
        """添加过滤条件"""
        self.filters.append({"term": {field: value}})
        return self
    
    def filter_terms(self, field: str, values: List[Any]) -> 'QueryBuilder':
        """添加多值过滤条件"""
        self.filters.append({"terms": {field: values}})
        return self
    
    def filter_range(
        self, 
        field: str, 
        gte: Optional[Any] = None,
        lte: Optional[Any] = None
    ) -> 'QueryBuilder':
        """添加范围过滤条件"""
        range_params = {}
        if gte is not None:
            range_params["gte"] = gte
        if lte is not None:
            range_params["lte"] = lte
        
        self.filters.append({"range": {field: range_params}})
        return self
    
    def must_not_term(self, field: str, value: Any) -> 'QueryBuilder':
        """添加 must_not 条件"""
        self.must_not.append({"term": {field: value}})
        return self
    
    def should_match(self, field: str, value: str, boost: Optional[float] = None) -> 'QueryBuilder':
        """添加 should 条件"""
        should_query = {"match": {field: {"query": value}}}
        if boost:
            should_query["match"][field]["boost"] = boost
        
        self.should.append(should_query)
        return self
    
    def sort(self, field: str, order: SortOrder = SortOrder.ASC) -> 'QueryBuilder':
        """添加排序字段"""
        if field == "_score":
            self.sort_fields.append("_score")
        else:
            self.sort_fields.append({field: {"order": order.value}})
        return self
    
    def sort_by_score(self) -> 'QueryBuilder':
        """按相关性排序"""
        self.sort_fields.append("_score")
        return self
    
    def sort_by_time(self, field: str = "updatedTime", order: SortOrder = SortOrder.DESC) -> 'QueryBuilder':
        """按时间排序"""
        self.sort_fields.append({field: {"order": order.value}})
        return self
    
    def highlight(self, fields: Union[str, List[str]], **options) -> 'QueryBuilder':
        """添加高亮字段"""
        if isinstance(fields, str):
            fields = [fields]
        
        for field in fields:
            self.highlight_fields[field] = options
        
        return self
    
    def aggregate(self, name: str, agg_type: str, field: str, **options) -> 'QueryBuilder':
        """添加聚合"""
        self.aggregations[name] = {
            agg_type: {
                "field": field,
                **options
            }
        }
        return self
    
    def terms_agg(self, name: str, field: str, size: int = 10) -> 'QueryBuilder':
        """添加 terms 聚合"""
        return self.aggregate(name, "terms", field, size=size)
    
    def date_histogram_agg(
        self, 
        name: str, 
        field: str, 
        interval: str = "1d"
    ) -> 'QueryBuilder':
        """添加日期直方图聚合"""
        return self.aggregate(name, "date_histogram", field, calendar_interval=interval)
    
    def cardinality_agg(self, name: str, field: str) -> 'QueryBuilder':
        """添加基数聚合"""
        return self.aggregate(name, "cardinality", field)
    
    def avg_agg(self, name: str, field: str) -> 'QueryBuilder':
        """添加平均值聚合"""
        return self.aggregate(name, "avg", field)
    
    def pagination(self, page: int = 1, page_size: int = 20) -> 'QueryBuilder':
        """设置分页"""
        self.size = page_size
        self.from_ = (page - 1) * page_size
        return self
    
    def limit(self, size: int) -> 'QueryBuilder':
        """设置返回数量限制"""
        self.size = size
        return self
    
    def offset(self, from_: int) -> 'QueryBuilder':
        """设置偏移量"""
        self.from_ = from_
        return self
    
    def source(self, fields: Union[bool, List[str]]) -> 'QueryBuilder':
        """设置返回字段"""
        self.source_fields = fields
        return self
    
    def minimum_score(self, score: float) -> 'QueryBuilder':
        """设置最小相关性分数"""
        self.min_score = score
        return self
    
    def build(self) -> Dict[str, Any]:
        """构建最终查询"""
        body = {}
        
        # 构建查询部分
        if self.filters or self.must_not or self.should:
            # 使用 bool 查询
            bool_query = {"must": [self.query]}
            
            if self.filters:
                bool_query["filter"] = self.filters
            
            if self.must_not:
                bool_query["must_not"] = self.must_not
            
            if self.should:
                bool_query["should"] = self.should
                bool_query["minimum_should_match"] = 1
            
            body["query"] = {"bool": bool_query}
        else:
            body["query"] = self.query
        
        # 添加其他参数
        body["size"] = self.size
        body["from"] = self.from_
        
        if self.sort_fields:
            body["sort"] = self.sort_fields
        
        if self.highlight_fields:
            body["highlight"] = {"fields": self.highlight_fields}
        
        if self.aggregations:
            body["aggs"] = self.aggregations
        
        if self.source_fields is not None:
            body["_source"] = self.source_fields
        
        if self.min_score is not None:
            body["min_score"] = self.min_score
        
        return body
    
    def reset(self) -> 'QueryBuilder':
        """重置查询构建器"""
        self.__init__()
        return self

class ToolQueryBuilder(QueryBuilder):
    """工具专用查询构建器"""
    
    def __init__(self):
        super().__init__()
        # 默认过滤已删除的工具
        self.filter("deleteFlag", 0)
    
    def search_text(
        self, 
        query: str, 
        boost_name: float = 3.0,
        boost_description: float = 2.0,
        boost_aliases: float = 2.5
    ) -> 'ToolQueryBuilder':
        """文本搜索"""
        fields = [
            f"displayName^{boost_name}",
            f"descriptionUser^{boost_description}",
            f"aliases^{boost_aliases}",
            "content",
            "category"
        ]
        
        self.multi_match(query, fields)
        return self
    
    def by_category(self, category: str) -> 'ToolQueryBuilder':
        """按分类筛选"""
        self.filter("category", category)
        return self
    
    def by_transport(self, transport: str) -> 'ToolQueryBuilder':
        """按传输方式筛选"""
        self.filter("capabilities", transport)
        return self
    
    def by_visibility(self, visibility: str) -> 'ToolQueryBuilder':
        """按可见性筛选"""
        self.filter("visibility", visibility)
        return self
    
    def user_facing_only(self, user_facing: bool = True) -> 'ToolQueryBuilder':
        """只显示面向用户的工具"""
        self.filter("metadata.user_facing", user_facing)
        return self
    
    def public_only(self) -> 'ToolQueryBuilder':
        """只显示公开工具"""
        self.by_visibility("public")
        return self
    
    def recent_tools(self, days: int = 7) -> 'ToolQueryBuilder':
        """最近创建的工具"""
        from datetime import datetime, timedelta
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        self.filter_range("createdTime", gte=since_date)
        return self
    
    def exclude_tool(self, tool_id: str) -> 'ToolQueryBuilder':
        """排除特定工具"""
        self.must_not_term("toolId", tool_id)
        return self
    
    def similar_to(self, tool: Dict[str, Any]) -> 'ToolQueryBuilder':
        """查找相似工具"""
        # 按分类相似
        if tool.get("category"):
            self.should_match("category", tool["category"], boost=2.0)
        
        # 按功能能力相似
        if tool.get("capabilities"):
            for capability in tool["capabilities"]:
                self.should_match("capabilities", capability, boost=1.5)
        
        # 按内容相似
        if tool.get("content"):
            self.should_match("content", tool["content"], boost=1.0)
        
        # 排除自己
        if tool.get("toolId"):
            self.exclude_tool(tool["toolId"])
        
        return self
    
    def with_examples(self) -> 'ToolQueryBuilder':
        """有使用示例的工具"""
        self.filter_range("examples", gte=1)
        return self
    
    def with_quickstarts(self) -> 'ToolQueryBuilder':
        """有快速启动的工具"""
        self.filter_range("quickstarts", gte=1)
        return self
    
    def sort_by_relevance(self) -> 'ToolQueryBuilder':
        """按相关性排序"""
        self.sort_by_score()
        return self
    
    def sort_by_updated(self, order: SortOrder = SortOrder.DESC) -> 'ToolQueryBuilder':
        """按更新时间排序"""
        self.sort_by_time("updatedTime", order)
        return self
    
    def sort_by_created(self, order: SortOrder = SortOrder.DESC) -> 'ToolQueryBuilder':
        """按创建时间排序"""
        self.sort_by_time("createdTime", order)
        return self
    
    def with_highlights(self) -> 'ToolQueryBuilder':
        """添加搜索高亮"""
        self.highlight(["displayName", "descriptionUser", "content"])
        return self
    
    def with_category_agg(self, size: int = 20) -> 'ToolQueryBuilder':
        """添加分类聚合"""
        self.terms_agg("categories", "category", size)
        return self
    
    def with_transport_agg(self, size: int = 10) -> 'ToolQueryBuilder':
        """添加传输方式聚合"""
        self.terms_agg("transports", "capabilities", size)
        return self
    
    def with_stats_agg(self) -> 'ToolQueryBuilder':
        """添加统计聚合"""
        self.with_category_agg()
        self.with_transport_agg()
        self.terms_agg("visibility", "visibility", 5)
        self.cardinality_agg("unique_owners", "owner.userId")
        return self

def create_tool_query() -> ToolQueryBuilder:
    """创建工具查询构建器"""
    return ToolQueryBuilder()

def create_search_query(
    text: str = "",
    category: Optional[str] = None,
    transport: Optional[str] = None,
    visibility: Optional[str] = None,
    user_facing: Optional[bool] = None,
    page: int = 1,
    page_size: int = 20
) -> Dict[str, Any]:
    """快速创建搜索查询"""
    builder = create_tool_query()
    
    if text:
        builder.search_text(text)
    else:
        builder.query = {"match_all": {}}
    
    if category:
        builder.by_category(category)
    
    if transport:
        builder.by_transport(transport)
    
    if visibility:
        builder.by_visibility(visibility)
    
    if user_facing is not None:
        builder.user_facing_only(user_facing)
    
    builder.pagination(page, page_size)
    builder.sort_by_relevance()
    builder.sort_by_updated()
    builder.with_highlights()
    
    return builder.build()