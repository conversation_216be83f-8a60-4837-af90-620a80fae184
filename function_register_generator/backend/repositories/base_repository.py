from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from elasticsearch import AsyncElasticsearch
import redis.asyncio as redis
import json
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class BaseRepository(ABC):
    """基础仓库抽象类"""
    
    def __init__(self, es_client: AsyncElasticsearch, redis_client: Optional[redis.Redis] = None):
        self.es_client = es_client
        self.redis_client = redis_client
        self.index_name = self.get_index_name()
    
    @abstractmethod
    def get_index_name(self) -> str:
        """获取索引名称"""
        pass
    
    async def create(self, doc_id: str, document: Dict[str, Any]) -> Dict[str, Any]:
        """创建文档"""
        try:
            # 设置时间戳
            now = datetime.now().isoformat()
            document['createdTime'] = now
            document['updatedTime'] = now
            document['deleteFlag'] = 0
            
            response = await self.es_client.index(
                index=self.index_name,
                id=doc_id,
                body=document
            )
            
            # 清除相关缓存
            await self._clear_cache_pattern(f"*{doc_id}*")
            
            logger.info(f"Created document {doc_id} in {self.index_name}")
            return {
                "success": True,
                "id": doc_id,
                "result": response["result"]
            }
            
        except Exception as e:
            logger.error(f"Failed to create document {doc_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_by_id(self, doc_id: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """根据ID获取文档"""
        try:
            # 尝试从缓存获取
            if use_cache and self.redis_client:
                cache_key = f"{self.index_name}:doc:{doc_id}"
                cached = await self.redis_client.get(cache_key)
                if cached:
                    logger.debug(f"Cache hit for document {doc_id}")
                    return json.loads(cached)
            
            # 从 Elasticsearch 获取
            response = await self.es_client.get(
                index=self.index_name,
                id=doc_id
            )
            
            document = response["_source"]
            
            # 缓存结果
            if use_cache and self.redis_client:
                cache_key = f"{self.index_name}:doc:{doc_id}"
                await self.redis_client.setex(
                    cache_key, 
                    300,  # 5分钟缓存
                    json.dumps(document, default=str)
                )
            
            return document
            
        except Exception as e:
            if "not_found" in str(e).lower():
                return None
            logger.error(f"Failed to get document {doc_id}: {e}")
            raise
    
    async def update(self, doc_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新文档"""
        try:
            # 设置更新时间
            updates['updatedTime'] = datetime.now().isoformat()
            
            response = await self.es_client.update(
                index=self.index_name,
                id=doc_id,
                body={"doc": updates}
            )
            
            # 清除缓存
            await self._clear_cache_pattern(f"*{doc_id}*")
            
            logger.info(f"Updated document {doc_id} in {self.index_name}")
            return {
                "success": True,
                "id": doc_id,
                "result": response["result"]
            }
            
        except Exception as e:
            logger.error(f"Failed to update document {doc_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def delete(self, doc_id: str, soft_delete: bool = True) -> Dict[str, Any]:
        """删除文档"""
        try:
            if soft_delete:
                # 软删除：设置删除标记
                updates = {
                    'deleteFlag': 1,
                    'updatedTime': datetime.now().isoformat()
                }
                return await self.update(doc_id, updates)
            else:
                # 硬删除：物理删除
                response = await self.es_client.delete(
                    index=self.index_name,
                    id=doc_id
                )
                
                # 清除缓存
                await self._clear_cache_pattern(f"*{doc_id}*")
                
                logger.info(f"Deleted document {doc_id} from {self.index_name}")
                return {
                    "success": True,
                    "id": doc_id,
                    "result": response["result"]
                }
                
        except Exception as e:
            logger.error(f"Failed to delete document {doc_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def exists(self, doc_id: str) -> bool:
        """检查文档是否存在"""
        try:
            response = await self.es_client.exists(
                index=self.index_name,
                id=doc_id
            )
            return response
        except Exception as e:
            logger.error(f"Failed to check existence of document {doc_id}: {e}")
            return False
    
    async def count(self, query: Optional[Dict[str, Any]] = None) -> int:
        """统计文档数量"""
        try:
            body = {}
            if query:
                body["query"] = query
            
            response = await self.es_client.count(
                index=self.index_name,
                body=body
            )
            return response["count"]
        except Exception as e:
            logger.error(f"Failed to count documents: {e}")
            return 0
    
    async def search(
        self,
        query: Dict[str, Any],
        size: int = 20,
        from_: int = 0,
        sort: Optional[List[Dict[str, Any]]] = None,
        source: Optional[Union[bool, List[str]]] = None,
        use_cache: bool = False
    ) -> Dict[str, Any]:
        """搜索文档"""
        try:
            # 构建查询体
            body = {
                "query": query,
                "size": size,
                "from": from_
            }
            
            if sort:
                body["sort"] = sort
            
            if source is not None:
                body["_source"] = source
            
            # 尝试从缓存获取
            cache_key = None
            if use_cache and self.redis_client:
                cache_key = f"{self.index_name}:search:{hash(json.dumps(body, sort_keys=True))}"
                cached = await self.redis_client.get(cache_key)
                if cached:
                    logger.debug("Cache hit for search query")
                    return json.loads(cached)
            
            # 执行搜索
            response = await self.es_client.search(
                index=self.index_name,
                body=body
            )
            
            # 处理结果
            result = {
                "hits": [hit["_source"] for hit in response["hits"]["hits"]],
                "total": response["hits"]["total"]["value"],
                "took": response["took"],
                "max_score": response["hits"]["max_score"]
            }
            
            # 缓存结果
            if use_cache and self.redis_client and cache_key:
                await self.redis_client.setex(
                    cache_key,
                    60,  # 1分钟缓存
                    json.dumps(result, default=str)
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    async def bulk_create(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量创建文档"""
        try:
            actions = []
            now = datetime.now().isoformat()
            
            for doc in documents:
                doc_id = doc.get('id') or doc.get('toolId')
                if not doc_id:
                    continue
                
                # 设置时间戳
                doc['createdTime'] = now
                doc['updatedTime'] = now
                doc['deleteFlag'] = 0
                
                actions.extend([
                    {"index": {"_index": self.index_name, "_id": doc_id}},
                    doc
                ])
            
            if not actions:
                return {"success": False, "error": "No valid documents to create"}
            
            response = await self.es_client.bulk(body=actions)
            
            # 清除缓存
            await self._clear_cache_pattern("*")
            
            # 统计结果
            created = sum(1 for item in response["items"] if item["index"]["result"] == "created")
            errors = [item for item in response["items"] if "error" in item["index"]]
            
            logger.info(f"Bulk created {created} documents in {self.index_name}")
            
            return {
                "success": len(errors) == 0,
                "created": created,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Bulk create failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _clear_cache_pattern(self, pattern: str):
        """清除匹配模式的缓存"""
        if not self.redis_client:
            return
        
        try:
            keys = await self.redis_client.keys(f"{self.index_name}:{pattern}")
            if keys:
                await self.redis_client.delete(*keys)
                logger.debug(f"Cleared {len(keys)} cache keys")
        except Exception as e:
            logger.warning(f"Failed to clear cache: {e}")
    
    async def refresh_index(self):
        """刷新索引"""
        try:
            await self.es_client.indices.refresh(index=self.index_name)
            logger.info(f"Refreshed index {self.index_name}")
        except Exception as e:
            logger.error(f"Failed to refresh index: {e}")
    
    async def get_mapping(self) -> Dict[str, Any]:
        """获取索引映射"""
        try:
            response = await self.es_client.indices.get_mapping(index=self.index_name)
            return response[self.index_name]["mappings"]
        except Exception as e:
            logger.error(f"Failed to get mapping: {e}")
            return {}
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            response = await self.es_client.indices.stats(index=self.index_name)
            return response["indices"][self.index_name]
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {}