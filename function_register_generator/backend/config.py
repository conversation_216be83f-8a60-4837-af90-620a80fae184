from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # Elasticsearch settings
    es_host: str = os.getenv("ES_HOST", "localhost:9200")
    es_user: str = os.getenv("ES_USER", "elastic")
    es_pass: str = os.getenv("ES_PASS", "changeme")
    es_index: str = "mcp_tools_guangfan"
    index: str = os.getenv("INDEX", "mcp_tools_guangfan")  # 兼容 .env 文件中的 INDEX
    
    # Redis settings
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # OpenAI settings
    openai_api_key: Optional[str] = os.getenv("OPENAI_API_KEY")
    
    # LM Studio settings
    lm_studio_url: str = os.getenv("LM_STUDIO_URL", "http://localhost:1234")
    lm_studio_model: str = os.getenv("LM_STUDIO_MODEL", "qwen/qwen3-30b-a3b-2507")
    
    # Sandbox settings
    sandbox_timeout: int = 30
    sandbox_memory_limit: str = "128m"
    sandbox_cpu_limit: str = "0.5"
    
    # Security settings
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    class Config:
        env_file = ".env"

settings = Settings()