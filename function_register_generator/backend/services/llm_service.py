import httpx
import json
import logging
from typing import Dict, Any, Optional, List
from config import settings

logger = logging.getLogger(__name__)

class LMStudioService:
    """LM Studio 本地模型服务"""
    
    def __init__(self, base_url: str = "http://localhost:1234", model_name: str = "qwen/qwen3-30b-a3b-2507"):
        self.base_url = base_url.rstrip('/')
        self.model_name = model_name
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def complete_tool_metadata(self, tool_info: Dict[str, Any]) -> Dict[str, Any]:
        """使用 LLM 补全工具元数据"""
        try:
            prompt = self._build_completion_prompt(tool_info)
            
            response = await self._call_llm(prompt, max_tokens=1000)
            
            # 解析 LLM 响应
            completion_data = self._parse_completion_response(response)
            
            return {
                "success": True,
                "completions": completion_data,
                "original_response": response
            }
            
        except Exception as e:
            logger.error(f"LLM completion failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "completions": {}
            }
    
    async def generate_tool_from_description(self, description: str, examples: Optional[Dict] = None) -> Dict[str, Any]:
        """从自然语言描述生成工具定义"""
        try:
            prompt = self._build_generation_prompt(description, examples)
            
            response = await self._call_llm(prompt, max_tokens=1500)
            
            # 解析生成的工具定义
            tool_definition = self._parse_generation_response(response)
            
            return {
                "success": True,
                "tool_definition": tool_definition,
                "original_response": response
            }
            
        except Exception as e:
            logger.error(f"Tool generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool_definition": {}
            }
    
    async def infer_schema_from_curl(self, curl_command: str) -> Dict[str, Any]:
        """从 cURL 命令推断输入输出 schema"""
        try:
            prompt = self._build_curl_analysis_prompt(curl_command)
            
            response = await self._call_llm(prompt, max_tokens=800)
            
            # 解析 schema 推断结果
            schema_data = self._parse_schema_response(response)
            
            return {
                "success": True,
                "schemas": schema_data,
                "original_response": response
            }
            
        except Exception as e:
            logger.error(f"cURL schema inference failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "schemas": {}
            }
    
    async def generate_examples(self, tool_name: str, input_schema: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成工具使用示例"""
        try:
            prompt = self._build_examples_prompt(tool_name, input_schema)
            
            response = await self._call_llm(prompt, max_tokens=600)
            
            # 解析示例
            examples = self._parse_examples_response(response)
            
            return examples
            
        except Exception as e:
            logger.error(f"Example generation failed: {e}")
            return []
    
    async def _call_llm(self, prompt: str, max_tokens: int = 500, temperature: float = 0.3) -> str:
        """调用 LM Studio API"""
        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的API工具分析专家，擅长从各种输入中提取和生成结构化的工具定义。请用中文回答，并确保输出格式正确。"
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": False
        }
        
        response = await self.client.post(
            f"{self.base_url}/v1/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            raise Exception(f"LM Studio API error: {response.status_code} - {response.text}")
        
        result = response.json()
        return result["choices"][0]["message"]["content"]
    
    def _build_completion_prompt(self, tool_info: Dict[str, Any]) -> str:
        """构建元数据补全提示"""
        return f"""
请分析以下工具信息，并补全缺失的元数据：

工具信息：
- 名称: {tool_info.get('displayName', '未知')}
- 类型: {tool_info.get('transport', '未知')}
- 端点: {tool_info.get('endpoint', '未知')}
- 描述: {tool_info.get('description', '无')}

请以JSON格式返回补全的元数据，包括：
1. category (工具分类，如: weather, utility, nlp, database 等)
2. descriptionUser (面向用户的友好描述)
3. descriptionDev (面向开发者的技术描述)
4. capabilities (功能能力列表，如: ["http", "sync", "public"])
5. aliases (别名列表)

示例输出：
```json
{{
  "category": "weather",
  "descriptionUser": "获取指定位置的天气预报信息",
  "descriptionDev": "调用第三方天气API获取气象数据",
  "capabilities": ["http", "sync", "public"],
  "aliases": ["天气", "weather", "forecast"]
}}
```
"""
    
    def _build_generation_prompt(self, description: str, examples: Optional[Dict] = None) -> str:
        """构建工具生成提示"""
        examples_text = ""
        if examples:
            examples_text = f"\n测试数据示例：\n{json.dumps(examples, ensure_ascii=False, indent=2)}"
        
        return f"""
请根据以下描述生成一个完整的工具定义：

用户描述：{description}{examples_text}

请以JSON格式返回工具定义，包括：
1. toolId (点分格式，如: category.service.action)
2. displayName (显示名称)
3. category (分类)
4. transport (传输方式: http/python/stdio)
5. runtime (运行时配置)
6. inputsDeveloperSchema (输入参数JSON Schema)
7. outputsSchema (输出JSON Schema)
8. descriptionUser (用户描述)
9. examples (使用示例)

示例输出：
```json
{{
  "toolId": "text.processor.count",
  "displayName": "文本字符统计",
  "category": "utility",
  "transport": "python",
  "runtime": {{
    "transport": "python",
    "entry_function": "count_characters"
  }},
  "inputsDeveloperSchema": {{
    "type": "object",
    "required": ["text"],
    "properties": {{
      "text": {{"type": "string", "description": "要统计的文本"}}
    }}
  }},
  "outputsSchema": {{
    "type": "object",
    "properties": {{
      "character_count": {{"type": "integer"}},
      "word_count": {{"type": "integer"}}
    }}
  }},
  "descriptionUser": "统计文本中的字符和单词数量",
  "examples": [{{
    "userQuery": "统计这段文本的字符数",
    "parsedInputs": {{"text": "示例文本"}}
  }}]
}}
```
"""
    
    def _build_curl_analysis_prompt(self, curl_command: str) -> str:
        """构建 cURL 分析提示"""
        return f"""
请分析以下 cURL 命令，并推断输入输出 schema：

cURL 命令：
{curl_command}

请以JSON格式返回分析结果，包括：
1. endpoint (提取的URL)
2. method (HTTP方法)
3. headers (请求头)
4. inputSchema (根据参数推断的输入schema)
5. expectedOutputSchema (预期的输出schema)
6. description (API功能描述)

示例输出：
```json
{{
  "endpoint": "https://api.example.com/data",
  "method": "GET",
  "headers": {{"Content-Type": "application/json"}},
  "inputSchema": {{
    "type": "object",
    "properties": {{
      "query": {{"type": "string", "description": "查询参数"}}
    }}
  }},
  "expectedOutputSchema": {{
    "type": "object",
    "properties": {{
      "data": {{"type": "array"}},
      "status": {{"type": "string"}}
    }}
  }},
  "description": "获取数据的API接口"
}}
```
"""
    
    def _build_examples_prompt(self, tool_name: str, input_schema: Dict[str, Any]) -> str:
        """构建示例生成提示"""
        return f"""
请为工具 "{tool_name}" 生成3个使用示例。

输入参数schema：
{json.dumps(input_schema, ensure_ascii=False, indent=2)}

请以JSON数组格式返回示例，每个示例包括：
1. userQuery (用户查询描述)
2. parsedInputs (解析后的输入参数)

示例输出：
```json
[
  {{
    "userQuery": "查询北京的天气",
    "parsedInputs": {{
      "city": "北京",
      "days": 3
    }}
  }},
  {{
    "userQuery": "获取上海未来一周天气",
    "parsedInputs": {{
      "city": "上海", 
      "days": 7
    }}
  }}
]
```
"""
    
    def _parse_completion_response(self, response: str) -> Dict[str, Any]:
        """解析补全响应"""
        try:
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，返回默认值
                return {
                    "category": "utility",
                    "descriptionUser": "用户工具",
                    "descriptionDev": "User tool",
                    "capabilities": ["sync"],
                    "aliases": []
                }
        except Exception as e:
            logger.error(f"Failed to parse completion response: {e}")
            return {}
    
    def _parse_generation_response(self, response: str) -> Dict[str, Any]:
        """解析生成响应"""
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                return {}
        except Exception as e:
            logger.error(f"Failed to parse generation response: {e}")
            return {}
    
    def _parse_schema_response(self, response: str) -> Dict[str, Any]:
        """解析schema响应"""
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                return {}
        except Exception as e:
            logger.error(f"Failed to parse schema response: {e}")
            return {}
    
    def _parse_examples_response(self, response: str) -> List[Dict[str, Any]]:
        """解析示例响应"""
        try:
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                return []
        except Exception as e:
            logger.error(f"Failed to parse examples response: {e}")
            return []
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()

# 全局LLM服务实例
llm_service = LMStudioService()

async def get_llm_service() -> LMStudioService:
    """获取LLM服务实例"""
    return llm_service