"""
Python 脚本沙盒服务

提供安全的 Python 代码执行环境，包括 Docker 容器隔离、资源限制、
代码静态分析、执行结果捕获等功能。
"""

import asyncio
import json
import logging
import tempfile
import os
import ast
import time
import re
import subprocess
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from pathlib import Path
from pydantic import BaseModel, field_validator

logger = logging.getLogger(__name__)


class PythonSandboxConfig:
    """Python 沙盒配置"""
    
    # 默认超时时间（秒）
    DEFAULT_TIMEOUT = 30
    MAX_TIMEOUT = 300
    
    # 资源限制
    MEMORY_LIMIT = "128m"
    CPU_LIMIT = "0.5"
    
    # 允许的 Python 模块
    ALLOWED_MODULES = {
        # 标准库
        "json", "re", "math", "datetime", "time", "random", "uuid",
        "base64", "hashlib", "urllib", "collections", "itertools",
        "functools", "operator", "string", "textwrap", "unicodedata",
        # 数据处理
        "csv", "xml", "html", "email", "mimetypes",
        # 网络（受限）
        "requests", "httpx",
        # 数据科学（基础）
        "statistics", "decimal", "fractions"
    }
    
    # 禁止的函数和模块
    FORBIDDEN_PATTERNS = [
        r"__import__", r"eval", r"exec", r"compile",
        r"open\s*\(", r"file\s*\(", r"input\s*\(",
        r"os\.", r"sys\.", r"subprocess\.", r"shutil\.",
        r"socket\.", r"threading\.", r"multiprocessing\.",
        r"ctypes\.", r"importlib\.", r"pkgutil\.",
        r"globals\s*\(", r"locals\s*\(", r"vars\s*\(",
        r"dir\s*\(", r"getattr", r"setattr", r"delattr",
        r"hasattr", r"callable"
    ]
    
    # Docker 配置
    DOCKER_IMAGE = "python:3.11-slim"
    DOCKER_WORK_DIR = "/sandbox"
    DOCKER_USER = "nobody"


@dataclass
class PythonExecutionResult:
    """Python 执行结果"""
    success: bool
    exit_code: Optional[int] = None
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    execution_time_ms: Optional[int] = None
    result_data: Optional[Any] = None
    error: Optional[str] = None
    security_warnings: List[str] = None
    resource_usage: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.security_warnings is None:
            self.security_warnings = []


class PythonSandboxRequest(BaseModel):
    """Python 沙盒请求"""
    script_content: str
    entry_function: str
    function_args: Optional[Dict[str, Any]] = None
    timeout: int = PythonSandboxConfig.DEFAULT_TIMEOUT
    allow_network: bool = False
    memory_limit: str = PythonSandboxConfig.MEMORY_LIMIT
    cpu_limit: str = PythonSandboxConfig.CPU_LIMIT
    
    @field_validator('timeout')
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0 or v > PythonSandboxConfig.MAX_TIMEOUT:
            raise ValueError(f"超时时间必须在 1-{PythonSandboxConfig.MAX_TIMEOUT} 秒之间")
        return v
    
    @field_validator('script_content')
    @classmethod
    def validate_script_content(cls, v):
        if not v or not v.strip():
            raise ValueError("脚本内容不能为空")
        if len(v) > 50000:  # 50KB 限制
            raise ValueError("脚本内容过大，最大支持 50KB")
        return v
    
    @field_validator('entry_function')
    @classmethod
    def validate_entry_function(cls, v):
        if not v or not v.strip():
            raise ValueError("入口函数名不能为空")
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', v):
            raise ValueError("入口函数名格式无效")
        return v


class PythonSandboxService:
    """Python 脚本沙盒服务"""
    
    def __init__(self, config: Optional[PythonSandboxConfig] = None):
        self.config = config or PythonSandboxConfig()
    
    async def execute_python_script(self, request: PythonSandboxRequest) -> PythonExecutionResult:
        """执行 Python 脚本"""
        try:
            # 1. 静态安全检查
            security_warnings = self._static_security_check(request.script_content)
            
            # 2. 准备执行环境
            script_path = await self._prepare_script(request)
            
            try:
                # 3. 执行脚本
                start_time = time.time()
                result = await self._execute_in_container(script_path, request)
                end_time = time.time()
                
                execution_time_ms = int((end_time - start_time) * 1000)
                
                # 4. 解析执行结果
                result_data = self._parse_execution_output(result.stdout)
                
                return PythonExecutionResult(
                    success=result.exit_code == 0,
                    exit_code=result.exit_code,
                    stdout=result.stdout,
                    stderr=result.stderr,
                    execution_time_ms=execution_time_ms,
                    result_data=result_data,
                    security_warnings=security_warnings,
                    resource_usage=self._get_resource_usage(result.stdout)
                )
                
            finally:
                # 清理临时文件
                if os.path.exists(script_path):
                    os.unlink(script_path)
                    
        except Exception as e:
            logger.error(f"Python 脚本执行失败: {e}")
            return PythonExecutionResult(
                success=False,
                error=str(e),
                security_warnings=security_warnings if 'security_warnings' in locals() else []
            )
    
    def _static_security_check(self, script_content: str) -> List[str]:
        """静态安全检查"""
        warnings = []
        
        try:
            # 1. AST 解析检查
            try:
                tree = ast.parse(script_content)
                warnings.extend(self._check_ast_security(tree))
            except SyntaxError as e:
                warnings.append(f"语法错误: {e}")
            
            # 2. 正则表达式检查
            for pattern in self.config.FORBIDDEN_PATTERNS:
                if re.search(pattern, script_content, re.IGNORECASE):
                    warnings.append(f"检测到禁止的模式: {pattern}")
            
            # 3. 导入检查
            import_warnings = self._check_imports(script_content)
            warnings.extend(import_warnings)
            
            # 4. 字符串检查
            string_warnings = self._check_dangerous_strings(script_content)
            warnings.extend(string_warnings)
            
        except Exception as e:
            warnings.append(f"安全检查失败: {e}")
        
        return warnings
    
    def _check_ast_security(self, tree: ast.AST) -> List[str]:
        """AST 安全检查"""
        warnings = []
        
        class SecurityVisitor(ast.NodeVisitor):
            def __init__(self):
                self.warnings = []
            
            def visit_Import(self, node):
                for alias in node.names:
                    if alias.name not in PythonSandboxConfig.ALLOWED_MODULES:
                        self.warnings.append(f"不允许导入模块: {alias.name}")
                self.generic_visit(node)
            
            def visit_ImportFrom(self, node):
                if node.module and node.module not in PythonSandboxConfig.ALLOWED_MODULES:
                    self.warnings.append(f"不允许从模块导入: {node.module}")
                self.generic_visit(node)
            
            def visit_Call(self, node):
                # 检查危险函数调用
                if isinstance(node.func, ast.Name):
                    if node.func.id in ['eval', 'exec', 'compile', '__import__']:
                        self.warnings.append(f"不允许调用函数: {node.func.id}")
                elif isinstance(node.func, ast.Attribute):
                    if node.func.attr in ['system', 'popen', 'spawn']:
                        self.warnings.append(f"不允许调用方法: {node.func.attr}")
                self.generic_visit(node)
            
            def visit_Attribute(self, node):
                # 检查危险属性访问
                if isinstance(node.value, ast.Name):
                    if node.value.id in ['os', 'sys', 'subprocess']:
                        self.warnings.append(f"不允许访问模块: {node.value.id}")
                self.generic_visit(node)
        
        visitor = SecurityVisitor()
        visitor.visit(tree)
        return visitor.warnings
    
    def _check_imports(self, script_content: str) -> List[str]:
        """检查导入语句"""
        warnings = []
        
        # 提取所有导入语句
        import_patterns = [
            r'import\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)',
            r'from\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s+import'
        ]
        
        for pattern in import_patterns:
            matches = re.findall(pattern, script_content)
            for match in matches:
                module_name = match.split('.')[0]  # 获取顶级模块名
                if module_name not in self.config.ALLOWED_MODULES:
                    warnings.append(f"不允许导入模块: {module_name}")
        
        return warnings
    
    def _check_dangerous_strings(self, script_content: str) -> List[str]:
        """检查危险字符串"""
        warnings = []
        
        dangerous_strings = [
            '/etc/passwd', '/etc/shadow', '/proc/', '/sys/',
            'rm -rf', 'sudo', 'chmod', 'chown',
            'curl', 'wget', 'nc ', 'netcat',
            'python -c', 'python3 -c'
        ]
        
        for dangerous in dangerous_strings:
            if dangerous in script_content.lower():
                warnings.append(f"检测到可能危险的字符串: {dangerous}")
        
        return warnings
    
    async def _prepare_script(self, request: PythonSandboxRequest) -> str:
        """准备执行脚本"""
        # 创建临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            # 写入原始脚本内容
            f.write(request.script_content)
            f.write('\n\n')
            
            # 添加执行代码
            f.write('# === 沙盒执行代码 ===\n')
            f.write('import json\n')
            f.write('import sys\n')
            f.write('import traceback\n')
            f.write('\n')
            
            # 构建函数调用
            if request.function_args:
                args_str = ', '.join([
                    f"{k}={repr(v)}" for k, v in request.function_args.items()
                ])
                call_str = f"{request.entry_function}({args_str})"
            else:
                call_str = f"{request.entry_function}()"
            
            # 添加执行和结果捕获代码
            f.write('try:\n')
            f.write(f'    result = {call_str}\n')
            f.write('    print("SANDBOX_RESULT_START")\n')
            f.write('    print(json.dumps({"success": True, "result": result}, default=str))\n')
            f.write('    print("SANDBOX_RESULT_END")\n')
            f.write('except Exception as e:\n')
            f.write('    print("SANDBOX_RESULT_START")\n')
            f.write('    print(json.dumps({"success": False, "error": str(e), "traceback": traceback.format_exc()}, default=str))\n')
            f.write('    print("SANDBOX_RESULT_END")\n')
            f.write('    sys.exit(1)\n')
            
            return f.name
    
    async def _execute_in_container(self, script_path: str, request: PythonSandboxRequest) -> asyncio.subprocess.Process:
        """在 Docker 容器中执行脚本"""
        # 构建 Docker 命令
        docker_cmd = [
            'docker', 'run',
            '--rm',
            '--user', self.config.DOCKER_USER,
            '--memory', request.memory_limit,
            '--cpus', request.cpu_limit,
            '--network', 'none' if not request.allow_network else 'bridge',
            '--read-only',
            '--tmpfs', '/tmp:rw,noexec,nosuid,size=100m',
            '--volume', f'{script_path}:{self.config.DOCKER_WORK_DIR}/script.py:ro',
            '--workdir', self.config.DOCKER_WORK_DIR,
            self.config.DOCKER_IMAGE,
            'python', 'script.py'
        ]
        
        # 执行命令
        process = await asyncio.create_subprocess_exec(
            *docker_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=request.timeout
            )
            
            # 创建结果对象
            class ExecutionResult:
                def __init__(self, exit_code, stdout, stderr):
                    self.exit_code = exit_code
                    self.stdout = stdout.decode('utf-8') if stdout else ""
                    self.stderr = stderr.decode('utf-8') if stderr else ""
            
            return ExecutionResult(process.returncode, stdout, stderr)
            
        except asyncio.TimeoutError:
            # 超时，终止进程
            process.terminate()
            await process.wait()
            raise Exception(f"脚本执行超时 ({request.timeout}秒)")
    
    def _parse_execution_output(self, stdout: str) -> Any:
        """解析执行输出"""
        try:
            # 查找结果标记
            start_marker = "SANDBOX_RESULT_START"
            end_marker = "SANDBOX_RESULT_END"
            
            start_idx = stdout.find(start_marker)
            end_idx = stdout.find(end_marker)
            
            if start_idx >= 0 and end_idx >= 0:
                result_json = stdout[start_idx + len(start_marker):end_idx].strip()
                return json.loads(result_json)
            else:
                # 没有找到标记，返回原始输出
                return {"raw_output": stdout}
                
        except Exception as e:
            logger.warning(f"解析执行输出失败: {e}")
            return {"parse_error": str(e), "raw_output": stdout}
    
    def _get_resource_usage(self, stdout: str) -> Dict[str, Any]:
        """获取资源使用情况"""
        # 这里可以解析 Docker 的资源使用统计
        # 目前返回基本信息
        return {
            "output_size": len(stdout),
            "container_used": True
        }
    
    async def validate_python_function(self, script_content: str, function_name: str) -> Dict[str, Any]:
        """验证 Python 函数（不执行）"""
        try:
            # 静态分析
            security_warnings = self._static_security_check(script_content)
            
            # AST 解析
            tree = ast.parse(script_content)
            
            # 查找目标函数
            function_found = False
            function_info = None
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and node.name == function_name:
                    function_found = True
                    function_info = {
                        "name": node.name,
                        "args": [arg.arg for arg in node.args.args],
                        "defaults": len(node.args.defaults),
                        "docstring": ast.get_docstring(node),
                        "line_number": node.lineno
                    }
                    break
            
            return {
                "success": True,
                "function_found": function_found,
                "function_info": function_info,
                "security_warnings": security_warnings,
                "syntax_valid": True
            }
            
        except SyntaxError as e:
            return {
                "success": False,
                "syntax_valid": False,
                "syntax_error": str(e),
                "line_number": e.lineno
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def generate_test_script(self, function_info: Dict[str, Any]) -> str:
        """生成测试脚本"""
        function_name = function_info.get("name", "test_function")
        args = function_info.get("args", [])
        
        lines = [
            f"# 测试脚本 - {function_name}",
            "",
            "# 导入必要的模块",
            "import json",
            "import datetime",
            "",
            "# 测试用例",
            f"def test_{function_name}():",
            f'    """测试 {function_name} 函数"""'
        ]
        
        if args:
            # 生成示例参数
            example_args = []
            for arg in args:
                if 'name' in arg.lower():
                    example_args.append(f'{arg}="测试用户"')
                elif 'age' in arg.lower() or 'count' in arg.lower():
                    example_args.append(f'{arg}=25')
                elif 'active' in arg.lower() or 'enabled' in arg.lower():
                    example_args.append(f'{arg}=True')
                else:
                    example_args.append(f'{arg}="示例值"')
            
            args_str = ", ".join(example_args)
            lines.append(f"    result = {function_name}({args_str})")
        else:
            lines.append(f"    result = {function_name}()")
        
        lines.extend([
            "    print(f'测试结果: {result}')",
            "    return result",
            "",
            "# 运行测试",
            "if __name__ == '__main__':",
            f"    test_{function_name}()"
        ])
        
        return "\n".join(lines)


# 全局 Python 沙盒服务实例
_python_sandbox_service = None

async def get_python_sandbox_service() -> PythonSandboxService:
    """获取 Python 沙盒服务实例"""
    global _python_sandbox_service
    if _python_sandbox_service is None:
        _python_sandbox_service = PythonSandboxService()
    return _python_sandbox_service