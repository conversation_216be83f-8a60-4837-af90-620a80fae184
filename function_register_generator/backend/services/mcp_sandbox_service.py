"""
MCP 连接沙盒服务

提供安全的 MCP (Model Context Protocol) 服务器连接和验证环境，
支持 stdio 和 http 两种传输方式，包括工具列表获取、schema 解析等功能。
"""

import asyncio
import json
import logging
import time
import subprocess
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from pathlib import Path
from pydantic import BaseModel, field_validator
import httpx

logger = logging.getLogger(__name__)


class MCPSandboxConfig:
    """MCP 沙盒配置"""
    
    # 默认超时时间（秒）
    DEFAULT_TIMEOUT = 30
    MAX_TIMEOUT = 120
    
    # 连接超时
    CONNECT_TIMEOUT = 10
    
    # 支持的传输方式
    SUPPORTED_TRANSPORTS = {"stdio", "http"}
    
    # MCP 协议版本
    PROTOCOL_VERSION = "2024-11-05"
    
    # 客户端信息
    CLIENT_INFO = {
        "name": "tool-registry-sandbox",
        "version": "1.0.0"
    }
    
    # 允许的命令前缀（安全白名单）
    ALLOWED_COMMAND_PREFIXES = [
        "uvx", "npx", "node", "python", "python3",
        "/usr/bin/", "/usr/local/bin/", "./", "../"
    ]
    
    # 禁止的命令模式
    FORBIDDEN_COMMAND_PATTERNS = [
        "rm", "del", "format", "fdisk", "dd",
        "sudo", "su", "chmod", "chown",
        "curl", "wget", "nc", "netcat",
        "ssh", "scp", "rsync"
    ]


@dataclass
class MCPToolInfo:
    """MCP 工具信息"""
    name: str
    description: Optional[str] = None
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None


@dataclass
class MCPServerInfo:
    """MCP 服务器信息"""
    name: Optional[str] = None
    version: Optional[str] = None
    protocol_version: Optional[str] = None
    capabilities: Optional[Dict[str, Any]] = None
    server_info: Optional[Dict[str, Any]] = None


@dataclass
class MCPValidationResult:
    """MCP 验证结果"""
    success: bool
    transport: str
    server_info: Optional[MCPServerInfo] = None
    available_tools: List[MCPToolInfo] = None
    connection_time_ms: Optional[int] = None
    error: Optional[str] = None
    security_warnings: List[str] = None
    quickstart: Optional[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.available_tools is None:
            self.available_tools = []
        if self.security_warnings is None:
            self.security_warnings = []


class MCPSandboxRequest(BaseModel):
    """MCP 沙盒请求"""
    transport: str
    command: Optional[str] = None
    args: Optional[List[str]] = None
    url: Optional[str] = None
    timeout: int = MCPSandboxConfig.DEFAULT_TIMEOUT
    
    @field_validator('transport')
    @classmethod
    def validate_transport(cls, v):
        if v not in MCPSandboxConfig.SUPPORTED_TRANSPORTS:
            raise ValueError(f"不支持的传输方式: {v}")
        return v
    
    @field_validator('timeout')
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0 or v > MCPSandboxConfig.MAX_TIMEOUT:
            raise ValueError(f"超时时间必须在 1-{MCPSandboxConfig.MAX_TIMEOUT} 秒之间")
        return v
    
    @field_validator('command')
    @classmethod
    def validate_command(cls, v):
        if v is not None:
            # 检查命令安全性
            if not any(v.startswith(prefix) for prefix in MCPSandboxConfig.ALLOWED_COMMAND_PREFIXES):
                raise ValueError(f"不允许的命令前缀: {v}")
            
            for pattern in MCPSandboxConfig.FORBIDDEN_COMMAND_PATTERNS:
                if pattern in v.lower():
                    raise ValueError(f"命令包含禁止的模式: {pattern}")
        return v
    
    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        if v is not None:
            from urllib.parse import urlparse
            try:
                parsed = urlparse(v)
                if not parsed.scheme or not parsed.netloc:
                    raise ValueError("无效的 URL 格式")
                if parsed.scheme not in ['http', 'https']:
                    raise ValueError(f"不支持的协议: {parsed.scheme}")
            except Exception as e:
                raise ValueError(f"URL 验证失败: {e}")
        return v


class MCPSandboxService:
    """MCP 连接沙盒服务"""
    
    def __init__(self, config: Optional[MCPSandboxConfig] = None):
        self.config = config or MCPSandboxConfig()
    
    async def validate_mcp_server(self, request: MCPSandboxRequest) -> MCPValidationResult:
        """验证 MCP 服务器"""
        try:
            # 1. 安全检查
            security_warnings = self._security_check(request)
            
            # 2. 根据传输方式选择验证方法
            start_time = time.time()
            
            if request.transport == "stdio":
                result = await self._validate_stdio_server(request)
            elif request.transport == "http":
                result = await self._validate_http_server(request)
            else:
                raise ValueError(f"不支持的传输方式: {request.transport}")
            
            end_time = time.time()
            connection_time_ms = int((end_time - start_time) * 1000)
            
            # 3. 设置结果属性
            result.connection_time_ms = connection_time_ms
            result.security_warnings = security_warnings
            result.quickstart = self._generate_quickstart(request, result)
            
            return result
            
        except Exception as e:
            logger.error(f"MCP 服务器验证失败: {e}")
            return MCPValidationResult(
                success=False,
                transport=request.transport,
                error=str(e),
                security_warnings=security_warnings if 'security_warnings' in locals() else []
            )
    
    def _security_check(self, request: MCPSandboxRequest) -> List[str]:
        """安全检查"""
        warnings = []
        
        if request.transport == "stdio":
            if not request.command:
                warnings.append("stdio 传输需要指定命令")
                return warnings
            
            # 检查命令安全性
            command_parts = request.command.split()
            if command_parts:
                base_command = command_parts[0]
                
                # 检查是否在允许列表中
                allowed = any(base_command.startswith(prefix) for prefix in self.config.ALLOWED_COMMAND_PREFIXES)
                if not allowed:
                    warnings.append(f"命令不在安全白名单中: {base_command}")
                
                # 检查禁止的模式
                for pattern in self.config.FORBIDDEN_COMMAND_PATTERNS:
                    if pattern in request.command.lower():
                        warnings.append(f"命令包含禁止的模式: {pattern}")
            
            # 检查参数
            if request.args:
                args_str = " ".join(request.args)
                for pattern in self.config.FORBIDDEN_COMMAND_PATTERNS:
                    if pattern in args_str.lower():
                        warnings.append(f"参数包含禁止的模式: {pattern}")
        
        elif request.transport == "http":
            if not request.url:
                warnings.append("http 传输需要指定 URL")
                return warnings
            
            # URL 安全检查
            from urllib.parse import urlparse
            try:
                parsed = urlparse(request.url)
                
                # 检查是否为本地地址
                if parsed.hostname in ['localhost', '127.0.0.1', '0.0.0.0']:
                    warnings.append("连接到本地地址可能存在安全风险")
                
                # 检查端口
                if parsed.port and parsed.port in [22, 23, 25, 53, 135, 139, 445]:
                    warnings.append(f"连接到敏感端口: {parsed.port}")
                    
            except Exception as e:
                warnings.append(f"URL 解析失败: {e}")
        
        return warnings
    
    async def _validate_stdio_server(self, request: MCPSandboxRequest) -> MCPValidationResult:
        """验证 stdio MCP 服务器"""
        try:
            # 构建命令
            cmd_args = [request.command] + (request.args or [])
            
            # 启动进程
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env={"PATH": "/usr/bin:/bin:/usr/local/bin"}  # 限制 PATH
            )
            
            try:
                # 发送初始化消息
                init_message = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": self.config.PROTOCOL_VERSION,
                        "capabilities": {},
                        "clientInfo": self.config.CLIENT_INFO
                    }
                }
                
                message_bytes = (json.dumps(init_message) + '\n').encode('utf-8')
                process.stdin.write(message_bytes)
                await process.stdin.drain()
                
                # 读取初始化响应
                response_line = await asyncio.wait_for(
                    process.stdout.readline(),
                    timeout=request.timeout
                )
                
                if not response_line:
                    raise Exception("服务器没有响应初始化消息")
                
                init_response = json.loads(response_line.decode('utf-8'))
                
                # 解析服务器信息
                server_info = self._parse_server_info(init_response)
                
                # 获取工具列表
                tools_message = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/list",
                    "params": {}
                }
                
                message_bytes = (json.dumps(tools_message) + '\n').encode('utf-8')
                process.stdin.write(message_bytes)
                await process.stdin.drain()
                
                # 读取工具列表响应
                tools_response_line = await asyncio.wait_for(
                    process.stdout.readline(),
                    timeout=5
                )
                
                available_tools = []
                if tools_response_line:
                    tools_response = json.loads(tools_response_line.decode('utf-8'))
                    available_tools = self._parse_tools_list(tools_response)
                
                return MCPValidationResult(
                    success=True,
                    transport="stdio",
                    server_info=server_info,
                    available_tools=available_tools
                )
                
            finally:
                # 清理进程
                try:
                    process.terminate()
                    await asyncio.wait_for(process.wait(), timeout=5)
                except:
                    process.kill()
                    await process.wait()
                    
        except asyncio.TimeoutError:
            raise Exception("MCP 服务器连接超时")
        except json.JSONDecodeError as e:
            raise Exception(f"MCP 响应格式错误: {e}")
        except Exception as e:
            raise Exception(f"stdio MCP 验证失败: {e}")
    
    async def _validate_http_server(self, request: MCPSandboxRequest) -> MCPValidationResult:
        """验证 HTTP MCP 服务器"""
        try:
            async with httpx.AsyncClient(timeout=request.timeout) as client:
                # 发送初始化请求
                init_message = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": self.config.PROTOCOL_VERSION,
                        "capabilities": {},
                        "clientInfo": self.config.CLIENT_INFO
                    }
                }
                
                init_response = await client.post(
                    request.url,
                    json=init_message,
                    headers={"Content-Type": "application/json"}
                )
                
                init_response.raise_for_status()
                init_data = init_response.json()
                
                # 解析服务器信息
                server_info = self._parse_server_info(init_data)
                
                # 获取工具列表
                tools_message = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/list",
                    "params": {}
                }
                
                tools_response = await client.post(
                    request.url,
                    json=tools_message,
                    headers={"Content-Type": "application/json"}
                )
                
                available_tools = []
                if tools_response.status_code == 200:
                    tools_data = tools_response.json()
                    available_tools = self._parse_tools_list(tools_data)
                
                return MCPValidationResult(
                    success=True,
                    transport="http",
                    server_info=server_info,
                    available_tools=available_tools
                )
                
        except httpx.TimeoutException:
            raise Exception("HTTP MCP 服务器连接超时")
        except httpx.HTTPStatusError as e:
            raise Exception(f"HTTP 错误: {e.response.status_code}")
        except json.JSONDecodeError as e:
            raise Exception(f"MCP 响应格式错误: {e}")
        except Exception as e:
            raise Exception(f"HTTP MCP 验证失败: {e}")
    
    def _parse_server_info(self, response: Dict[str, Any]) -> MCPServerInfo:
        """解析服务器信息"""
        result = response.get("result", {})
        
        return MCPServerInfo(
            protocol_version=result.get("protocolVersion"),
            capabilities=result.get("capabilities", {}),
            server_info=result.get("serverInfo", {})
        )
    
    def _parse_tools_list(self, response: Dict[str, Any]) -> List[MCPToolInfo]:
        """解析工具列表"""
        tools = []
        
        result = response.get("result", {})
        tools_data = result.get("tools", [])
        
        for tool_data in tools_data:
            tool = MCPToolInfo(
                name=tool_data.get("name", ""),
                description=tool_data.get("description"),
                input_schema=tool_data.get("inputSchema"),
                output_schema=tool_data.get("outputSchema")
            )
            tools.append(tool)
        
        return tools
    
    def _generate_quickstart(self, request: MCPSandboxRequest, result: MCPValidationResult) -> Dict[str, str]:
        """生成快速开始代码"""
        quickstart = {}
        
        if request.transport == "stdio":
            # 生成命令行调用
            cmd_parts = [request.command] + (request.args or [])
            quickstart["command"] = " ".join(cmd_parts)
            
            # 生成 MCP 消息示例
            quickstart["mcp_init"] = json.dumps({
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": self.config.PROTOCOL_VERSION,
                    "capabilities": {},
                    "clientInfo": self.config.CLIENT_INFO
                }
            }, indent=2)
            
            quickstart["mcp_tools"] = json.dumps({
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }, indent=2)
            
        elif request.transport == "http":
            # 生成 cURL 命令
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": self.config.PROTOCOL_VERSION,
                    "capabilities": {},
                    "clientInfo": self.config.CLIENT_INFO
                }
            }
            
            quickstart["curl"] = f"""curl -X POST {request.url} \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(init_message)}'"""
            
            # 生成 Python 代码
            quickstart["python"] = f"""import requests
import json

url = "{request.url}"

# 初始化连接
init_message = {json.dumps(init_message, indent=4)}

response = requests.post(url, json=init_message)
print("初始化响应:", response.json())

# 获取工具列表
tools_message = {json.dumps({"jsonrpc": "2.0", "id": 2, "method": "tools/list", "params": {}}, indent=4)}

tools_response = requests.post(url, json=tools_message)
print("工具列表:", tools_response.json())"""
        
        return quickstart
    
    async def test_mcp_tool(self, request: MCPSandboxRequest, tool_name: str, 
                           tool_args: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """测试 MCP 工具调用"""
        try:
            if request.transport == "stdio":
                return await self._test_stdio_tool(request, tool_name, tool_args)
            elif request.transport == "http":
                return await self._test_http_tool(request, tool_name, tool_args)
            else:
                raise ValueError(f"不支持的传输方式: {request.transport}")
                
        except Exception as e:
            logger.error(f"MCP 工具测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_stdio_tool(self, request: MCPSandboxRequest, tool_name: str, 
                              tool_args: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """测试 stdio MCP 工具"""
        cmd_args = [request.command] + (request.args or [])
        
        process = await asyncio.create_subprocess_exec(
            *cmd_args,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            # 初始化
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": self.config.PROTOCOL_VERSION,
                    "capabilities": {},
                    "clientInfo": self.config.CLIENT_INFO
                }
            }
            
            message_bytes = (json.dumps(init_message) + '\n').encode('utf-8')
            process.stdin.write(message_bytes)
            await process.stdin.drain()
            
            # 读取初始化响应
            await process.stdout.readline()
            
            # 调用工具
            tool_message = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": tool_args or {}
                }
            }
            
            message_bytes = (json.dumps(tool_message) + '\n').encode('utf-8')
            process.stdin.write(message_bytes)
            await process.stdin.drain()
            
            # 读取工具调用响应
            response_line = await asyncio.wait_for(
                process.stdout.readline(),
                timeout=request.timeout
            )
            
            if response_line:
                response = json.loads(response_line.decode('utf-8'))
                return {
                    "success": True,
                    "response": response
                }
            else:
                return {
                    "success": False,
                    "error": "工具调用无响应"
                }
                
        finally:
            process.terminate()
            await process.wait()
    
    async def _test_http_tool(self, request: MCPSandboxRequest, tool_name: str, 
                             tool_args: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """测试 HTTP MCP 工具"""
        async with httpx.AsyncClient(timeout=request.timeout) as client:
            # 初始化
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": self.config.PROTOCOL_VERSION,
                    "capabilities": {},
                    "clientInfo": self.config.CLIENT_INFO
                }
            }
            
            await client.post(request.url, json=init_message)
            
            # 调用工具
            tool_message = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": tool_args or {}
                }
            }
            
            response = await client.post(request.url, json=tool_message)
            
            return {
                "success": response.status_code == 200,
                "response": response.json() if response.status_code == 200 else None,
                "status_code": response.status_code
            }


# 全局 MCP 沙盒服务实例
_mcp_sandbox_service = None

async def get_mcp_sandbox_service() -> MCPSandboxService:
    """获取 MCP 沙盒服务实例"""
    global _mcp_sandbox_service
    if _mcp_sandbox_service is None:
        _mcp_sandbox_service = MCPSandboxService()
    return _mcp_sandbox_service