"""
HTTP 工具沙盒服务

提供安全的 HTTP 工具验证和执行环境，包括网络白名单、超时限制、
响应解析和 Schema 推断等功能。
"""

import asyncio
import json
import logging
import time
import re
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from urllib.parse import urlparse, urlencode
import httpx
from pydantic import BaseModel, field_validator

logger = logging.getLogger(__name__)


class HTTPSandboxConfig:
    """HTTP 沙盒配置"""
    
    # 默认超时时间（秒）
    DEFAULT_TIMEOUT = 30
    MAX_TIMEOUT = 120
    
    # 响应大小限制（字节）
    MAX_RESPONSE_SIZE = 10 * 1024 * 1024  # 10MB
    
    # 重定向限制
    MAX_REDIRECTS = 5
    
    # 允许的协议
    ALLOWED_SCHEMES = {"http", "https"}
    
    # 禁止的域名和IP（安全黑名单）
    BLOCKED_DOMAINS = {
        "localhost", "127.0.0.1", "0.0.0.0",
        "10.0.0.0/8", "**********/12", "***********/16",
        "***********/16", "::1", "fc00::/7", "fe80::/10"
    }
    
    # 允许的域名白名单（如果设置，只允许这些域名）
    ALLOWED_DOMAINS: Optional[List[str]] = None
    
    # 默认请求头
    DEFAULT_HEADERS = {
        "User-Agent": "ToolRegistry-Sandbox/1.0",
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate"
    }


@dataclass
class HTTPValidationResult:
    """HTTP 验证结果"""
    success: bool
    status_code: Optional[int] = None
    latency_ms: Optional[int] = None
    response_size: Optional[int] = None
    content_type: Optional[str] = None
    sample_response: Optional[Any] = None
    inferred_output_schema: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    security_warnings: List[str] = None
    quickstart: Optional[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.security_warnings is None:
            self.security_warnings = []


class HTTPSandboxRequest(BaseModel):
    """HTTP 沙盒请求"""
    url: str
    method: str = "GET"
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    data: Optional[Union[Dict[str, Any], str]] = None
    timeout: int = HTTPSandboxConfig.DEFAULT_TIMEOUT
    follow_redirects: bool = True
    verify_ssl: bool = True
    
    @field_validator('method')
    @classmethod
    def validate_method(cls, v):
        allowed_methods = {"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
        if v.upper() not in allowed_methods:
            raise ValueError(f"不支持的 HTTP 方法: {v}")
        return v.upper()
    
    @field_validator('timeout')
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0 or v > HTTPSandboxConfig.MAX_TIMEOUT:
            raise ValueError(f"超时时间必须在 1-{HTTPSandboxConfig.MAX_TIMEOUT} 秒之间")
        return v
    
    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        try:
            parsed = urlparse(v)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("无效的 URL 格式")
            if parsed.scheme not in HTTPSandboxConfig.ALLOWED_SCHEMES:
                raise ValueError(f"不支持的协议: {parsed.scheme}")
            return v
        except Exception as e:
            raise ValueError(f"URL 验证失败: {e}")


class HTTPSandboxService:
    """HTTP 工具沙盒服务"""
    
    def __init__(self, config: Optional[HTTPSandboxConfig] = None):
        self.config = config or HTTPSandboxConfig()
        self.client = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.config.MAX_TIMEOUT),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5),
            follow_redirects=False  # 手动处理重定向以进行安全检查
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.client:
            await self.client.aclose()
    
    async def validate_http_tool(self, request: HTTPSandboxRequest) -> HTTPValidationResult:
        """验证 HTTP 工具"""
        try:
            # 1. 安全检查
            security_warnings = await self._security_check(request.url)
            
            # 2. 准备请求参数
            request_kwargs = await self._prepare_request(request)
            
            # 3. 执行请求
            start_time = time.time()
            response = await self._execute_request(request_kwargs)
            end_time = time.time()
            
            latency_ms = int((end_time - start_time) * 1000)
            
            # 4. 处理响应
            response_data, content_type = await self._process_response(response)
            
            # 5. 推断输出 Schema
            output_schema = self._infer_output_schema(response_data)
            
            # 6. 生成快速开始代码
            quickstart = self._generate_quickstart(request)
            
            return HTTPValidationResult(
                success=True,
                status_code=response.status_code,
                latency_ms=latency_ms,
                response_size=len(response.content),
                content_type=content_type,
                sample_response=response_data,
                inferred_output_schema=output_schema,
                security_warnings=security_warnings,
                quickstart=quickstart
            )
            
        except httpx.TimeoutException:
            return HTTPValidationResult(
                success=False,
                error="请求超时"
            )
        except httpx.ConnectError as e:
            return HTTPValidationResult(
                success=False,
                error=f"连接失败: {str(e)}"
            )
        except httpx.HTTPStatusError as e:
            return HTTPValidationResult(
                success=False,
                status_code=e.response.status_code,
                error=f"HTTP 错误: {e.response.status_code} {e.response.reason_phrase}"
            )
        except Exception as e:
            logger.error(f"HTTP 工具验证失败: {e}")
            return HTTPValidationResult(
                success=False,
                error=f"验证失败: {str(e)}"
            )
    
    async def _security_check(self, url: str) -> List[str]:
        """安全检查"""
        warnings = []
        
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.split(':')[0]  # 移除端口号
            
            # 检查协议
            if parsed.scheme not in self.config.ALLOWED_SCHEMES:
                warnings.append(f"不安全的协议: {parsed.scheme}")
            
            # 检查域名黑名单
            if domain in self.config.BLOCKED_DOMAINS:
                warnings.append(f"禁止访问的域名: {domain}")
            
            # 检查私有IP地址
            if self._is_private_ip(domain):
                warnings.append(f"禁止访问私有IP地址: {domain}")
            
            # 检查域名白名单
            if (self.config.ALLOWED_DOMAINS and 
                domain not in self.config.ALLOWED_DOMAINS):
                warnings.append(f"域名不在白名单中: {domain}")
            
            # 检查端口
            if parsed.port:
                if parsed.port in [22, 23, 25, 53, 135, 139, 445]:  # 常见的敏感端口
                    warnings.append(f"可能不安全的端口: {parsed.port}")
            
        except Exception as e:
            warnings.append(f"URL 安全检查失败: {e}")
        
        return warnings
    
    def _is_private_ip(self, ip_or_domain: str) -> bool:
        """检查是否为私有IP地址"""
        import ipaddress
        
        try:
            ip = ipaddress.ip_address(ip_or_domain)
            return ip.is_private or ip.is_loopback or ip.is_link_local
        except ValueError:
            # 不是IP地址，可能是域名
            return False
    
    async def _prepare_request(self, request: HTTPSandboxRequest) -> Dict[str, Any]:
        """准备请求参数"""
        # 合并请求头
        headers = dict(self.config.DEFAULT_HEADERS)
        if request.headers:
            headers.update(request.headers)
        
        # 构建请求参数
        request_kwargs = {
            "method": request.method,
            "url": request.url,
            "headers": headers,
            "timeout": request.timeout,
            "follow_redirects": request.follow_redirects
        }
        
        # 添加查询参数
        if request.params:
            request_kwargs["params"] = request.params
        
        # 添加请求体
        if request.data and request.method in ["POST", "PUT", "PATCH"]:
            if isinstance(request.data, dict):
                request_kwargs["json"] = request.data
            else:
                request_kwargs["content"] = request.data
                if "Content-Type" not in headers:
                    headers["Content-Type"] = "text/plain"
        
        return request_kwargs
    
    async def _execute_request(self, request_kwargs: Dict[str, Any]) -> httpx.Response:
        """执行 HTTP 请求"""
        if not self.client:
            raise RuntimeError("HTTP 客户端未初始化")
        
        # 执行请求
        response = await self.client.request(**request_kwargs)
        
        # 检查响应大小
        if len(response.content) > self.config.MAX_RESPONSE_SIZE:
            raise ValueError(f"响应大小超过限制: {len(response.content)} > {self.config.MAX_RESPONSE_SIZE}")
        
        # 检查状态码
        response.raise_for_status()
        
        return response
    
    async def _process_response(self, response: httpx.Response) -> Tuple[Any, str]:
        """处理响应数据"""
        content_type = response.headers.get("content-type", "").lower()
        
        try:
            if "application/json" in content_type:
                return response.json(), content_type
            elif "text/" in content_type or "application/xml" in content_type:
                return response.text, content_type
            else:
                # 二进制数据，返回基本信息
                return {
                    "content_type": content_type,
                    "size": len(response.content),
                    "data": "binary_data"
                }, content_type
        except Exception as e:
            logger.warning(f"响应解析失败: {e}")
            return response.text, content_type
    
    def _infer_output_schema(self, data: Any) -> Dict[str, Any]:
        """推断输出 Schema"""
        return self._infer_schema_recursive(data)
    
    def _infer_schema_recursive(self, data: Any) -> Dict[str, Any]:
        """递归推断 Schema"""
        if data is None:
            return {"type": "null"}
        elif isinstance(data, bool):
            return {"type": "boolean"}
        elif isinstance(data, int):
            return {"type": "integer"}
        elif isinstance(data, float):
            return {"type": "number"}
        elif isinstance(data, str):
            # 尝试识别特殊格式
            if self._is_datetime_string(data):
                return {"type": "string", "format": "date-time"}
            elif self._is_email_string(data):
                return {"type": "string", "format": "email"}
            elif self._is_url_string(data):
                return {"type": "string", "format": "uri"}
            else:
                return {"type": "string"}
        elif isinstance(data, list):
            if not data:
                return {"type": "array", "items": {}}
            
            # 分析数组项的类型
            item_schemas = [self._infer_schema_recursive(item) for item in data[:5]]  # 只分析前5项
            
            # 如果所有项类型相同，使用统一类型
            if len(set(json.dumps(schema, sort_keys=True) for schema in item_schemas)) == 1:
                return {"type": "array", "items": item_schemas[0]}
            else:
                return {"type": "array", "items": {"anyOf": item_schemas}}
        
        elif isinstance(data, dict):
            properties = {}
            required = []
            
            for key, value in data.items():
                properties[key] = self._infer_schema_recursive(value)
                if value is not None:  # 非空值视为必需
                    required.append(key)
            
            schema = {
                "type": "object",
                "properties": properties
            }
            
            if required:
                schema["required"] = required
            
            return schema
        
        else:
            return {"type": "string", "description": f"Unknown type: {type(data).__name__}"}
    
    def _is_datetime_string(self, s: str) -> bool:
        """检查是否为日期时间字符串"""
        datetime_patterns = [
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',  # ISO 8601
            r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',  # SQL datetime
            r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
        ]
        return any(re.match(pattern, s) for pattern in datetime_patterns)
    
    def _is_email_string(self, s: str) -> bool:
        """检查是否为邮箱字符串"""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, s) is not None
    
    def _is_url_string(self, s: str) -> bool:
        """检查是否为URL字符串"""
        try:
            parsed = urlparse(s)
            return parsed.scheme in ['http', 'https'] and parsed.netloc
        except:
            return False
    
    def _generate_quickstart(self, request: HTTPSandboxRequest) -> Dict[str, str]:
        """生成快速开始代码"""
        return {
            "curl": self._generate_curl_command(request),
            "python": self._generate_python_code(request),
            "javascript": self._generate_javascript_code(request)
        }
    
    def _generate_curl_command(self, request: HTTPSandboxRequest) -> str:
        """生成 cURL 命令"""
        cmd_parts = ["curl"]
        
        # HTTP 方法
        if request.method != "GET":
            cmd_parts.extend(["-X", request.method])
        
        # 请求头
        headers = dict(self.config.DEFAULT_HEADERS)
        if request.headers:
            headers.update(request.headers)
        
        for key, value in headers.items():
            cmd_parts.extend(["-H", f"'{key}: {value}'"])
        
        # 请求体
        if request.data and request.method in ["POST", "PUT", "PATCH"]:
            if isinstance(request.data, dict):
                cmd_parts.extend(["-H", "'Content-Type: application/json'"])
                cmd_parts.extend(["-d", f"'{json.dumps(request.data)}'"])
            else:
                cmd_parts.extend(["-d", f"'{request.data}'"])
        
        # URL 和查询参数
        url = request.url
        if request.params:
            query_string = urlencode(request.params)
            url = f"{url}?{query_string}"
        
        cmd_parts.append(f"'{url}'")
        
        return " ".join(cmd_parts)
    
    def _generate_python_code(self, request: HTTPSandboxRequest) -> str:
        """生成 Python 代码"""
        lines = [
            "import requests",
            "",
            f"response = requests.{request.method.lower()}("
        ]
        
        # URL
        url = request.url
        if request.params:
            query_string = urlencode(request.params)
            url = f"{url}?{query_string}"
        
        lines.append(f"    '{url}',")
        
        # 请求头
        headers = dict(self.config.DEFAULT_HEADERS)
        if request.headers:
            headers.update(request.headers)
        
        if headers:
            lines.append(f"    headers={json.dumps(headers, indent=4)},")
        
        # 请求体
        if request.data and request.method in ["POST", "PUT", "PATCH"]:
            if isinstance(request.data, dict):
                lines.append(f"    json={json.dumps(request.data, indent=4)},")
            else:
                lines.append(f"    data='{request.data}',")
        
        lines.append(f"    timeout={request.timeout}")
        lines.append(")")
        lines.append("")
        lines.append("# 处理响应")
        lines.append("if response.status_code == 200:")
        lines.append("    data = response.json()")
        lines.append("    print(data)")
        lines.append("else:")
        lines.append("    print(f'错误: {response.status_code} - {response.text}')")
        
        return "\n".join(lines)
    
    def _generate_javascript_code(self, request: HTTPSandboxRequest) -> str:
        """生成 JavaScript 代码"""
        lines = [
            "// 使用 fetch API",
            "const fetchData = async () => {"
        ]
        
        # 构建选项
        options = {
            "method": request.method,
            "headers": dict(self.config.DEFAULT_HEADERS)
        }
        
        if request.headers:
            options["headers"].update(request.headers)
        
        if request.data and request.method in ["POST", "PUT", "PATCH"]:
            if isinstance(request.data, dict):
                options["headers"]["Content-Type"] = "application/json"
                options["body"] = json.dumps(request.data)
            else:
                options["body"] = request.data
        
        # URL
        url = request.url
        if request.params:
            query_string = urlencode(request.params)
            url = f"{url}?{query_string}"
        
        lines.append(f"  try {{")
        lines.append(f"    const response = await fetch('{url}', {json.dumps(options, indent=4)});")
        lines.append(f"    ")
        lines.append(f"    if (!response.ok) {{")
        lines.append(f"      throw new Error(`HTTP error! status: ${{response.status}}`);")
        lines.append(f"    }}")
        lines.append(f"    ")
        lines.append(f"    const data = await response.json();")
        lines.append(f"    console.log(data);")
        lines.append(f"    return data;")
        lines.append(f"  }} catch (error) {{")
        lines.append(f"    console.error('请求失败:', error);")
        lines.append(f"  }}")
        lines.append("};")
        lines.append("")
        lines.append("// 调用函数")
        lines.append("fetchData();")
        
        return "\n".join(lines)


# 全局 HTTP 沙盒服务实例
_http_sandbox_service = None

async def get_http_sandbox_service() -> HTTPSandboxService:
    """获取 HTTP 沙盒服务实例"""
    global _http_sandbox_service
    if _http_sandbox_service is None:
        _http_sandbox_service = HTTPSandboxService()
    return _http_sandbox_service