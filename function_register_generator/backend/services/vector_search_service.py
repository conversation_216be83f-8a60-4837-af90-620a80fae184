"""
向量搜索服务 - 支持语义搜索
"""
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json
import hashlib

class VectorSearchService:
    """向量搜索服务"""
    
    def __init__(self):
        self.embeddings_cache = {}
        self.vector_index = {}
        self.embedding_model = None
        self.dimension = 384  # 默认向量维度
        
    async def initialize(self):
        """初始化向量搜索服务"""
        try:
            # 这里应该初始化嵌入模型
            # 例如：sentence-transformers, OpenAI embeddings 等
            print("初始化向量搜索服务...")
            self.embedding_model = "mock_model"  # 模拟模型
            return True
        except Exception as e:
            print(f"向量搜索服务初始化失败: {e}")
            return False
    
    async def generate_embedding(self, text: str) -> np.ndarray:
        """生成文本嵌入向量"""
        try:
            # 检查缓存
            cache_key = hashlib.md5(text.encode()).hexdigest()
            if cache_key in self.embeddings_cache:
                return self.embeddings_cache[cache_key]
            
            # 生成嵌入向量
            if self.embedding_model == "mock_model":
                # 模拟嵌入向量生成
                embedding = await self._generate_mock_embedding(text)
            else:
                # 这里应该调用真实的嵌入模型
                embedding = await self._generate_real_embedding(text)
            
            # 缓存结果
            self.embeddings_cache[cache_key] = embedding
            
            return embedding
            
        except Exception as e:
            print(f"生成嵌入向量失败: {e}")
            # 返回随机向量作为降级
            return np.random.rand(self.dimension).astype(np.float32)
    
    async def _generate_mock_embedding(self, text: str) -> np.ndarray:
        """生成模拟嵌入向量"""
        # 基于文本内容生成确定性的向量
        # 这样相似的文本会有相似的向量
        
        # 简单的文本特征提取
        words = text.lower().split()
        
        # 创建基础向量
        embedding = np.zeros(self.dimension, dtype=np.float32)
        
        # 基于词汇生成向量特征
        for i, word in enumerate(words[:50]):  # 限制词数
            # 为每个词生成一个简单的哈希值
            word_hash = hash(word) % self.dimension
            embedding[word_hash] += 1.0 / (i + 1)  # 位置权重
        
        # 添加一些基于文本长度和内容的特征
        text_length_feature = min(len(text) / 100.0, 1.0)
        embedding[0] = text_length_feature
        
        # 检测关键词并增强相关维度
        keywords = {
            "api": [10, 50, 100],
            "数据": [20, 60, 120],
            "工具": [30, 70, 140],
            "文件": [40, 80, 160],
            "用户": [50, 90, 180],
            "系统": [60, 100, 200],
            "网络": [70, 110, 220],
            "安全": [80, 120, 240]
        }
        
        for keyword, indices in keywords.items():
            if keyword in text.lower():
                for idx in indices:
                    if idx < self.dimension:
                        embedding[idx] += 2.0
        
        # 归一化向量
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
        
        return embedding
    
    async def _generate_real_embedding(self, text: str) -> np.ndarray:
        """生成真实嵌入向量（使用实际模型）"""
        # 这里应该调用真实的嵌入模型
        # 例如：
        # - sentence-transformers
        # - OpenAI embeddings API
        # - 本地部署的嵌入模型
        
        # 示例代码（需要安装相应的库）:
        # from sentence_transformers import SentenceTransformer
        # model = SentenceTransformer('all-MiniLM-L6-v2')
        # embedding = model.encode(text)
        # return embedding.astype(np.float32)
        
        # 暂时返回模拟向量
        return await self._generate_mock_embedding(text)
    
    async def index_tool(self, tool_id: str, tool_data: Dict[str, Any]):
        """为工具创建向量索引"""
        try:
            # 构建工具的文本表示
            text_content = self._build_tool_text(tool_data)
            
            # 生成嵌入向量
            embedding = await self.generate_embedding(text_content)
            
            # 存储到向量索引
            self.vector_index[tool_id] = {
                "embedding": embedding,
                "tool_data": tool_data,
                "text_content": text_content,
                "indexed_time": datetime.now().isoformat()
            }
            
            return True
            
        except Exception as e:
            print(f"工具向量索引失败 {tool_id}: {e}")
            return False
    
    def _build_tool_text(self, tool_data: Dict[str, Any]) -> str:
        """构建工具的文本表示"""
        text_parts = []
        
        # 工具名称（权重最高）
        if tool_data.get("displayName"):
            text_parts.append(tool_data["displayName"])
            text_parts.append(tool_data["displayName"])  # 重复以增加权重
        
        # 描述
        if tool_data.get("descriptionUser"):
            text_parts.append(tool_data["descriptionUser"])
        
        if tool_data.get("descriptionDev"):
            text_parts.append(tool_data["descriptionDev"])
        
        # 分类
        if tool_data.get("category"):
            text_parts.append(f"分类:{tool_data['category']}")
        
        # 别名
        if tool_data.get("aliases"):
            text_parts.extend(tool_data["aliases"])
        
        # 标签
        if tool_data.get("tags"):
            text_parts.extend(tool_data["tags"])
        
        # 传输方式
        if tool_data.get("transport"):
            text_parts.append(f"传输方式:{tool_data['transport']}")
        
        # 功能描述
        if tool_data.get("capabilities"):
            text_parts.extend([f"功能:{cap}" for cap in tool_data["capabilities"]])
        
        return " ".join(text_parts)
    
    async def semantic_search(
        self, 
        query: str, 
        top_k: int = 10,
        similarity_threshold: float = 0.1
    ) -> List[Dict[str, Any]]:
        """语义搜索"""
        try:
            # 生成查询向量
            query_embedding = await self.generate_embedding(query)
            
            # 计算与所有工具的相似度
            similarities = []
            
            for tool_id, tool_index in self.vector_index.items():
                tool_embedding = tool_index["embedding"]
                
                # 计算余弦相似度
                similarity = self._cosine_similarity(query_embedding, tool_embedding)
                
                if similarity >= similarity_threshold:
                    similarities.append({
                        "tool_id": tool_id,
                        "similarity": float(similarity),
                        "tool_data": tool_index["tool_data"],
                        "text_content": tool_index["text_content"]
                    })
            
            # 按相似度排序
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            
            return similarities[:top_k]
            
        except Exception as e:
            print(f"语义搜索失败: {e}")
            return []
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        try:
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            print(f"计算余弦相似度失败: {e}")
            return 0.0
    
    async def hybrid_search(
        self,
        query: str,
        keyword_results: List[Dict[str, Any]],
        semantic_weight: float = 0.3,
        keyword_weight: float = 0.7,
        top_k: int = 20
    ) -> List[Dict[str, Any]]:
        """混合搜索（关键词 + 语义）"""
        try:
            # 获取语义搜索结果
            semantic_results = await self.semantic_search(query, top_k=top_k*2)
            
            # 创建结果字典
            combined_results = {}
            
            # 处理关键词搜索结果
            for i, result in enumerate(keyword_results):
                tool_id = result.get("toolId")
                if tool_id:
                    keyword_score = result.get("_score", 0) / 10.0  # 归一化到0-1
                    # 位置权重：排名越靠前权重越高
                    position_weight = 1.0 / (i + 1)
                    final_keyword_score = keyword_score * position_weight
                    
                    combined_results[tool_id] = {
                        "tool_data": result,
                        "keyword_score": final_keyword_score,
                        "semantic_score": 0.0,
                        "combined_score": final_keyword_score * keyword_weight
                    }
            
            # 处理语义搜索结果
            for result in semantic_results:
                tool_id = result["tool_id"]
                semantic_score = result["similarity"]
                
                if tool_id in combined_results:
                    # 更新现有结果
                    combined_results[tool_id]["semantic_score"] = semantic_score
                    combined_results[tool_id]["combined_score"] = (
                        combined_results[tool_id]["keyword_score"] * keyword_weight +
                        semantic_score * semantic_weight
                    )
                else:
                    # 添加新结果（仅语义匹配）
                    combined_results[tool_id] = {
                        "tool_data": result["tool_data"],
                        "keyword_score": 0.0,
                        "semantic_score": semantic_score,
                        "combined_score": semantic_score * semantic_weight
                    }
            
            # 按综合分数排序
            sorted_results = sorted(
                combined_results.values(),
                key=lambda x: x["combined_score"],
                reverse=True
            )
            
            # 构建最终结果
            final_results = []
            for result in sorted_results[:top_k]:
                tool_data = result["tool_data"]
                tool_data["_hybrid_score"] = result["combined_score"]
                tool_data["_keyword_score"] = result["keyword_score"]
                tool_data["_semantic_score"] = result["semantic_score"]
                final_results.append(tool_data)
            
            return final_results
            
        except Exception as e:
            print(f"混合搜索失败: {e}")
            return keyword_results[:top_k]  # 降级到关键词搜索
    
    async def get_similar_tools(
        self, 
        tool_id: str, 
        top_k: int = 5,
        exclude_self: bool = True
    ) -> List[Dict[str, Any]]:
        """获取相似工具"""
        try:
            if tool_id not in self.vector_index:
                return []
            
            target_embedding = self.vector_index[tool_id]["embedding"]
            similarities = []
            
            for tid, tool_index in self.vector_index.items():
                if exclude_self and tid == tool_id:
                    continue
                
                tool_embedding = tool_index["embedding"]
                similarity = self._cosine_similarity(target_embedding, tool_embedding)
                
                similarities.append({
                    "tool_id": tid,
                    "similarity": float(similarity),
                    "tool_data": tool_index["tool_data"]
                })
            
            # 按相似度排序
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            
            return similarities[:top_k]
            
        except Exception as e:
            print(f"获取相似工具失败: {e}")
            return []
    
    async def update_tool_index(self, tool_id: str, tool_data: Dict[str, Any]):
        """更新工具索引"""
        return await self.index_tool(tool_id, tool_data)
    
    async def remove_tool_index(self, tool_id: str):
        """移除工具索引"""
        try:
            if tool_id in self.vector_index:
                del self.vector_index[tool_id]
                return True
            return False
        except Exception as e:
            print(f"移除工具索引失败: {e}")
            return False
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            return {
                "total_tools": len(self.vector_index),
                "cache_size": len(self.embeddings_cache),
                "dimension": self.dimension,
                "model": self.embedding_model,
                "memory_usage": self._estimate_memory_usage()
            }
        except Exception as e:
            print(f"获取索引统计失败: {e}")
            return {}
    
    def _estimate_memory_usage(self) -> str:
        """估算内存使用量"""
        try:
            # 估算向量索引内存使用
            vector_memory = len(self.vector_index) * self.dimension * 4  # float32
            cache_memory = len(self.embeddings_cache) * self.dimension * 4
            total_bytes = vector_memory + cache_memory
            
            if total_bytes < 1024:
                return f"{total_bytes} B"
            elif total_bytes < 1024 * 1024:
                return f"{total_bytes / 1024:.1f} KB"
            else:
                return f"{total_bytes / (1024 * 1024):.1f} MB"
        except:
            return "Unknown"
    
    async def clear_cache(self):
        """清除缓存"""
        try:
            self.embeddings_cache.clear()
            return True
        except Exception as e:
            print(f"清除缓存失败: {e}")
            return False

# 全局向量搜索服务实例
_vector_search_service = None

async def get_vector_search_service() -> VectorSearchService:
    """获取向量搜索服务实例"""
    global _vector_search_service
    
    if _vector_search_service is None:
        _vector_search_service = VectorSearchService()
        await _vector_search_service.initialize()
    
    return _vector_search_service

async def initialize_vector_search():
    """初始化向量搜索服务"""
    service = await get_vector_search_service()
    return service is not None