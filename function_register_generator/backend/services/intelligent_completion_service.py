"""
智能补全服务

集成 cURL 解析器、Python 解析器和 LLM 服务，提供智能的工具定义补全功能。
"""

import json
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

from parsers.curl_parser import parse_curl, curl_to_schema
from parsers.python_parser import analyze_python_function, python_to_schema
from services.llm_service import LMStudioService, get_llm_service

logger = logging.getLogger(__name__)


@dataclass
class CompletionRequest:
    """补全请求"""
    tool_name: Optional[str] = None
    description: Optional[str] = None
    transport: Optional[str] = None
    endpoint: Optional[str] = None
    curl_command: Optional[str] = None
    python_code: Optional[str] = None
    function_name: Optional[str] = None
    test_inputs: Optional[Dict[str, Any]] = None
    test_outputs: Optional[Dict[str, Any]] = None


@dataclass
class CompletionResult:
    """补全结果"""
    success: bool
    tool_definition: Dict[str, Any]
    suggestions: List[str]
    confidence: float
    source: str  # "curl", "python", "llm", "hybrid"
    error: Optional[str] = None


class IntelligentCompletionService:
    """智能补全服务"""
    
    def __init__(self, llm_service: Optional[LMStudioService] = None):
        self.llm_service = llm_service
    
    async def complete_tool_definition(self, request: CompletionRequest) -> CompletionResult:
        """智能补全工具定义"""
        try:
            # 1. 基于输入类型选择处理策略
            if request.curl_command:
                return await self._complete_from_curl(request)
            elif request.python_code and request.function_name:
                return await self._complete_from_python(request)
            elif request.description and not (request.tool_name or request.endpoint):
                # 只有描述，没有其他信息时使用纯LLM生成
                return await self._complete_from_description(request)
            else:
                # 有部分信息时使用混合模式
                return await self._complete_from_partial_info(request)
                
        except Exception as e:
            logger.error(f"Tool completion failed: {e}")
            return CompletionResult(
                success=False,
                tool_definition={},
                suggestions=[],
                confidence=0.0,
                source="error",
                error=str(e)
            )
    
    async def _complete_from_curl(self, request: CompletionRequest) -> CompletionResult:
        """从 cURL 命令补全工具定义"""
        try:
            # 1. 使用 cURL 解析器提取基础信息
            curl_result = curl_to_schema(request.curl_command)
            
            # 2. 构建基础工具定义
            tool_definition = {
                "toolId": self._generate_tool_id(request.tool_name, "http"),
                "displayName": request.tool_name or self._extract_name_from_url(curl_result["curl_info"]["url"]),
                "category": "api",
                "transport": "http",
                "runtime": {
                    "transport": "http",
                    "method": curl_result["curl_info"]["method"],
                    "endpoint": curl_result["curl_info"]["url"],
                    "headers": curl_result["curl_info"]["headers"]
                },
                "inputsDeveloperSchema": curl_result["input_schema"],
                "outputsSchema": curl_result["output_schema"]
            }
            
            # 3. 使用 LLM 增强描述和分类
            if self.llm_service:
                llm_enhancement = await self.llm_service.complete_tool_metadata(tool_definition)
                if llm_enhancement["success"]:
                    completions = llm_enhancement["completions"]
                    tool_definition.update({
                        "category": completions.get("category", "api"),
                        "descriptionUser": completions.get("descriptionUser", "API工具"),
                        "descriptionDev": completions.get("descriptionDev", "HTTP API调用"),
                        "capabilities": completions.get("capabilities", ["http", "sync"]),
                        "aliases": completions.get("aliases", [])
                    })
            
            # 4. 生成使用示例
            examples = await self._generate_examples(tool_definition)
            tool_definition["examples"] = examples
            
            return CompletionResult(
                success=True,
                tool_definition=tool_definition,
                suggestions=self._generate_suggestions(tool_definition, "curl"),
                confidence=0.9,
                source="curl"
            )
            
        except Exception as e:
            logger.error(f"cURL completion failed: {e}")
            return CompletionResult(
                success=False,
                tool_definition={},
                suggestions=[],
                confidence=0.0,
                source="curl",
                error=str(e)
            )
    
    async def _complete_from_python(self, request: CompletionRequest) -> CompletionResult:
        """从 Python 代码补全工具定义"""
        try:
            # 1. 使用 Python 解析器分析代码
            python_result = python_to_schema(request.python_code, request.function_name)
            func_info = python_result["function_info"]
            
            # 2. 构建基础工具定义
            tool_definition = {
                "toolId": self._generate_tool_id(request.tool_name or func_info["name"], "python"),
                "displayName": request.tool_name or func_info["name"],
                "category": "script",
                "transport": "python",
                "runtime": {
                    "transport": "python",
                    "entry_function": func_info["name"],
                    "code": request.python_code
                },
                "inputsDeveloperSchema": python_result["input_schema"],
                "outputsSchema": python_result["output_schema"],
                "descriptionDev": func_info.get("description", "Python函数"),
                "docstring": func_info.get("docstring", "")
            }
            
            # 3. 使用 LLM 增强描述和分类
            if self.llm_service:
                llm_enhancement = await self.llm_service.complete_tool_metadata(tool_definition)
                if llm_enhancement["success"]:
                    completions = llm_enhancement["completions"]
                    tool_definition.update({
                        "category": completions.get("category", "script"),
                        "descriptionUser": completions.get("descriptionUser", func_info.get("description", "Python工具")),
                        "capabilities": completions.get("capabilities", ["python", "sync"]),
                        "aliases": completions.get("aliases", [func_info["name"]])
                    })
            
            # 4. 生成使用示例
            examples = await self._generate_examples(tool_definition)
            tool_definition["examples"] = examples
            
            return CompletionResult(
                success=True,
                tool_definition=tool_definition,
                suggestions=self._generate_suggestions(tool_definition, "python"),
                confidence=0.95,
                source="python"
            )
            
        except Exception as e:
            logger.error(f"Python completion failed: {e}")
            return CompletionResult(
                success=False,
                tool_definition={},
                suggestions=[],
                confidence=0.0,
                source="python",
                error=str(e)
            )
    
    async def _complete_from_description(self, request: CompletionRequest) -> CompletionResult:
        """从自然语言描述补全工具定义"""
        try:
            if not self.llm_service:
                raise Exception("LLM service not available for description-based completion")
            
            # 1. 使用 LLM 生成工具定义
            generation_result = await self.llm_service.generate_tool_from_description(
                request.description, 
                {"inputs": request.test_inputs, "outputs": request.test_outputs}
            )
            
            if not generation_result["success"]:
                raise Exception(f"LLM generation failed: {generation_result.get('error', 'Unknown error')}")
            
            tool_definition = generation_result["tool_definition"]
            
            # 2. 验证和修正工具定义
            tool_definition = self._validate_and_fix_tool_definition(tool_definition)
            
            # 3. 如果有测试数据，用来改进 schema
            if request.test_inputs or request.test_outputs:
                tool_definition = self._enhance_schema_with_test_data(
                    tool_definition, 
                    request.test_inputs, 
                    request.test_outputs
                )
            
            return CompletionResult(
                success=True,
                tool_definition=tool_definition,
                suggestions=self._generate_suggestions(tool_definition, "llm"),
                confidence=0.8,
                source="llm"
            )
            
        except Exception as e:
            logger.error(f"Description completion failed: {e}")
            return CompletionResult(
                success=False,
                tool_definition={},
                suggestions=[],
                confidence=0.0,
                source="llm",
                error=str(e)
            )
    
    async def _complete_from_partial_info(self, request: CompletionRequest) -> CompletionResult:
        """从部分信息补全工具定义"""
        try:
            # 构建基础工具定义
            tool_definition = {
                "toolId": self._generate_tool_id(request.tool_name, request.transport or "http"),
                "displayName": request.tool_name or "未命名工具",
                "category": "utility",
                "transport": request.transport or "http",
                "runtime": {
                    "transport": request.transport or "http"
                },
                "inputsDeveloperSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                },
                "outputsSchema": {
                    "type": "object",
                    "properties": {
                        "result": {"type": "string", "description": "执行结果"}
                    }
                }
            }
            
            # 添加端点信息
            if request.endpoint:
                tool_definition["runtime"]["endpoint"] = request.endpoint
            
            # 使用 LLM 增强
            if self.llm_service and request.description:
                llm_enhancement = await self.llm_service.complete_tool_metadata({
                    **tool_definition,
                    "description": request.description
                })
                if llm_enhancement["success"]:
                    completions = llm_enhancement["completions"]
                    tool_definition.update(completions)
            
            return CompletionResult(
                success=True,
                tool_definition=tool_definition,
                suggestions=self._generate_suggestions(tool_definition, "partial"),
                confidence=0.6,
                source="hybrid"
            )
            
        except Exception as e:
            logger.error(f"Partial completion failed: {e}")
            return CompletionResult(
                success=False,
                tool_definition={},
                suggestions=[],
                confidence=0.0,
                source="hybrid",
                error=str(e)
            )
    
    def _generate_tool_id(self, name: Optional[str], transport: str) -> str:
        """生成工具ID"""
        if not name:
            name = "unnamed"
        
        # 清理名称
        clean_name = "".join(c.lower() if c.isalnum() else "_" for c in name)
        clean_name = clean_name.strip("_")
        
        # 根据传输方式确定分类
        category_map = {
            "http": "api",
            "python": "script", 
            "stdio": "mcp",
            "mcp": "mcp"
        }
        
        category = category_map.get(transport, "utility")
        
        return f"{category}.{transport}.{clean_name}"
    
    def _extract_name_from_url(self, url: str) -> str:
        """从URL提取工具名称"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            
            # 提取域名作为服务名
            domain = parsed.netloc.replace("www.", "").replace("api.", "")
            
            # 提取路径的最后一部分作为操作名
            path_parts = [p for p in parsed.path.split("/") if p]
            operation = path_parts[-1] if path_parts else "api"
            
            return f"{domain}_{operation}".replace(".", "_")
            
        except Exception:
            return "api_tool"
    
    def _validate_and_fix_tool_definition(self, tool_def: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修正工具定义"""
        # 确保必需字段存在
        required_fields = {
            "toolId": "utility.unknown.tool",
            "displayName": "未命名工具",
            "category": "utility",
            "transport": "http",
            "runtime": {"transport": "http"},
            "inputsDeveloperSchema": {"type": "object", "properties": {}},
            "outputsSchema": {"type": "object", "properties": {"result": {"type": "string"}}}
        }
        
        for field, default_value in required_fields.items():
            if field not in tool_def:
                tool_def[field] = default_value
        
        return tool_def
    
    def _enhance_schema_with_test_data(self, tool_def: Dict[str, Any], 
                                     test_inputs: Optional[Dict], 
                                     test_outputs: Optional[Dict]) -> Dict[str, Any]:
        """使用测试数据增强 schema"""
        if test_inputs:
            # 从测试输入推断输入 schema
            input_schema = self._infer_schema_from_data(test_inputs)
            tool_def["inputsDeveloperSchema"] = input_schema
        
        if test_outputs:
            # 从测试输出推断输出 schema
            output_schema = self._infer_schema_from_data(test_outputs)
            tool_def["outputsSchema"] = output_schema
        
        return tool_def
    
    def _infer_schema_from_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """从数据推断 JSON Schema"""
        schema = {
            "type": "object",
            "properties": {},
            "required": []
        }
        
        for key, value in data.items():
            # 注意：在Python中，bool是int的子类，所以需要先检查bool
            if isinstance(value, bool):
                schema["properties"][key] = {"type": "boolean", "description": f"参数: {key}"}
            elif isinstance(value, int):
                schema["properties"][key] = {"type": "integer", "description": f"参数: {key}"}
            elif isinstance(value, float):
                schema["properties"][key] = {"type": "number", "description": f"参数: {key}"}
            elif isinstance(value, str):
                schema["properties"][key] = {"type": "string", "description": f"参数: {key}"}
            elif isinstance(value, list):
                schema["properties"][key] = {"type": "array", "description": f"参数: {key}"}
            elif isinstance(value, dict):
                schema["properties"][key] = {"type": "object", "description": f"参数: {key}"}
            else:
                schema["properties"][key] = {"type": "string", "description": f"参数: {key}"}
            
            schema["required"].append(key)
        
        return schema
    
    async def _generate_examples(self, tool_def: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成使用示例"""
        try:
            if self.llm_service:
                examples = await self.llm_service.generate_examples(
                    tool_def["displayName"],
                    tool_def["inputsDeveloperSchema"]
                )
                return examples
            else:
                # 生成默认示例
                return [{
                    "userQuery": f"使用{tool_def['displayName']}",
                    "parsedInputs": {}
                }]
        except Exception as e:
            logger.error(f"Example generation failed: {e}")
            return []
    
    def _generate_suggestions(self, tool_def: Dict[str, Any], source: str) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于来源的建议
        if source == "curl":
            suggestions.extend([
                "考虑添加错误处理和重试机制",
                "检查API是否需要认证",
                "验证输出schema是否完整"
            ])
        elif source == "python":
            suggestions.extend([
                "考虑添加类型检查",
                "确保函数有适当的错误处理",
                "检查是否需要异步执行"
            ])
        elif source == "llm":
            suggestions.extend([
                "验证生成的schema是否准确",
                "测试工具定义是否可执行",
                "考虑添加更多使用示例"
            ])
        
        # 通用建议
        if not tool_def.get("descriptionUser"):
            suggestions.append("添加面向用户的友好描述")
        
        if not tool_def.get("examples"):
            suggestions.append("添加使用示例")
        
        if tool_def.get("transport") == "http" and not tool_def.get("runtime", {}).get("headers"):
            suggestions.append("考虑添加必要的HTTP头")
        
        return suggestions


# 全局智能补全服务实例
_completion_service = None

async def get_intelligent_completion_service() -> IntelligentCompletionService:
    """获取智能补全服务实例"""
    global _completion_service
    if _completion_service is None:
        llm_service = await get_llm_service()
        _completion_service = IntelligentCompletionService(llm_service)
    return _completion_service