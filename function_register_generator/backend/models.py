from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class TransportType(str, Enum):
    """传输方式枚举"""
    HTTP = "http"
    PYTHON = "python"
    STDIO = "stdio"
    MCP = "mcp"

class VisibilityType(str, Enum):
    """可见性枚举"""
    PUBLIC = "public"
    INTERNAL = "internal"
    PRIVATE = "private"

class AuthType(str, Enum):
    """认证方式枚举"""
    NONE = "none"
    API_KEY = "api_key"
    OAUTH2 = "oauth2"
    BEARER = "bearer"
    BASIC = "basic"
    INTERNAL = "internal"

class FaultToleranceLevel(str, Enum):
    """容错等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

# 嵌套模型定义

class OwnerInfo(BaseModel):
    """工具所有者信息"""
    org: Optional[str] = None
    contact: Optional[str] = None
    license: Optional[str] = None
    userId: Optional[str] = None
    apiKeyId: Optional[str] = None

class AuthConfig(BaseModel):
    """认证配置"""
    type: AuthType = AuthType.NONE
    env_keys: Optional[List[str]] = None

class RateLimits(BaseModel):
    """速率限制配置"""
    rpm: Optional[int] = None  # 每分钟请求数
    burst: Optional[int] = None  # 峰值请求数

class CostHints(BaseModel):
    """成本提示"""
    per_call_usd: Optional[float] = None

class LatencyHints(BaseModel):
    """延迟提示 (毫秒)"""
    p50: Optional[int] = None  # 50分位延迟
    p95: Optional[int] = None  # 95分位延迟

class RuntimeConfig(BaseModel):
    """运行时配置"""
    transport: TransportType
    endpoint: Optional[str] = None
    httpMethod: Optional[str] = "GET"
    
    # Python 脚本相关
    script_content: Optional[str] = None  # base64 编码的脚本内容
    script_path: Optional[str] = None
    entry_function: Optional[str] = None
    
    # MCP 相关
    command: Optional[str] = None
    args: Optional[List[str]] = None
    url: Optional[str] = None
    env: Optional[Dict[str, str]] = None
    headers: Optional[Dict[str, str]] = None
    
    # 通用配置
    auth: Optional[AuthConfig] = None
    rate_limits: Optional[RateLimits] = None
    cost_hints: Optional[CostHints] = None
    latency_hints_ms: Optional[LatencyHints] = None
    fault_tolerance: Optional[FaultToleranceLevel] = FaultToleranceLevel.MEDIUM
    
    @field_validator('httpMethod')
    @classmethod
    def validate_http_method(cls, v):
        if v and v.upper() not in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']:
            raise ValueError('Invalid HTTP method')
        return v.upper() if v else 'GET'

class ToolMetadata(BaseModel):
    """工具元数据"""
    has_variables_dependencies: bool = False
    user_facing: bool = True

class ToolExample(BaseModel):
    """工具使用示例"""
    userQuery: str
    parsedInputs: Dict[str, Any]
    resolvedInputs: Optional[Dict[str, Any]] = None

class ToolError(BaseModel):
    """工具错误定义"""
    code: str
    http: Optional[int] = None
    message: str
    recovery: Optional[str] = None

class QuickStart(BaseModel):
    """快速启动脚本"""
    kind: str  # curl, python, mcp, etc.
    title: str
    content: str

# 主要的工具文档模型

class ToolDocument(BaseModel):
    """Elasticsearch 工具文档模型"""
    
    # 基础信息
    toolId: str = Field(..., description="工具唯一ID")
    displayName: str = Field(..., description="工具显示名称")
    aliases: Optional[List[str]] = Field(default_factory=list, description="工具别名")
    version: str = Field(default="1.0.0", description="版本号")
    versionSeq: int = Field(default=1, description="版本序列号")
    visibility: VisibilityType = Field(default=VisibilityType.PUBLIC, description="可见性")
    category: Optional[str] = Field(None, description="工具分类")
    capabilities: List[str] = Field(default_factory=list, description="功能能力")
    locales: List[str] = Field(default_factory=lambda: ["zh-CN"], description="支持语言")
    
    # 所有者信息
    owner: Optional[OwnerInfo] = None
    
    # 运行时配置
    runtime: RuntimeConfig
    
    # 元数据
    metadata: Optional[ToolMetadata] = None
    
    # 描述信息
    descriptionDev: Optional[str] = Field(None, description="开发者描述")
    descriptionUser: Optional[str] = Field(None, description="用户描述")
    
    # Schema 定义
    inputsDeveloperSchema: Optional[Dict[str, Any]] = Field(None, description="输入参数Schema")
    inputsDeveloperValidation: Optional[Dict[str, Any]] = Field(None, description="输入验证规则")
    inputsDeveloperDependencies: Optional[Dict[str, Any]] = Field(None, description="输入依赖")
    outputsSchema: Optional[Dict[str, Any]] = Field(None, description="输出Schema")
    
    # 示例和文档
    examples: List[ToolExample] = Field(default_factory=list, description="使用示例")
    errors: List[ToolError] = Field(default_factory=list, description="错误定义")
    quickstarts: List[QuickStart] = Field(default_factory=list, description="快速启动")
    
    # 搜索相关
    content: Optional[str] = Field(None, description="聚合搜索内容")
    contentEmb: Optional[List[float]] = Field(None, description="内容向量")
    descriptionEmb: Optional[List[float]] = Field(None, description="描述向量")
    
    # 审计信息
    createdBy: Optional[str] = None
    createdTime: Optional[datetime] = None
    updatedBy: Optional[str] = None
    updatedTime: Optional[datetime] = None
    deleteFlag: int = Field(default=0, description="删除标记")
    
    @field_validator('toolId')
    @classmethod
    def validate_tool_id(cls, v):
        """验证工具ID格式"""
        if not v or not isinstance(v, str):
            raise ValueError('toolId must be a non-empty string')
        # 工具ID应该是点分格式，如 weather.openmeteo.forecast
        if not all(part.replace('_', '').replace('-', '').isalnum() for part in v.split('.')):
            raise ValueError('toolId must contain only alphanumeric characters, dots, hyphens, and underscores')
        return v.lower()
    
    @field_validator('displayName')
    @classmethod
    def validate_display_name(cls, v):
        """验证显示名称"""
        if not v or not v.strip():
            raise ValueError('displayName cannot be empty')
        return v.strip()
    
    @field_validator('version')
    @classmethod
    def validate_version(cls, v):
        """验证版本号格式"""
        import re
        if not re.match(r'^\d+\.\d+\.\d+(-\w+)?$', v):
            raise ValueError('version must follow semantic versioning (e.g., 1.0.0)')
        return v
    
    @model_validator(mode='before')
    @classmethod
    def set_defaults_and_generate_content(cls, data):
        """设置默认值和生成内容"""
        if isinstance(data, dict):
            # 设置时间戳
            if 'createdTime' not in data or data['createdTime'] is None:
                data['createdTime'] = datetime.now()
            if 'updatedTime' not in data or data['updatedTime'] is None:
                data['updatedTime'] = datetime.now()
            
            # 生成搜索内容
            if 'content' not in data or data['content'] is None:
                parts = []
                
                if 'displayName' in data and data['displayName']:
                    parts.append(data['displayName'])
                
                if 'aliases' in data and data['aliases']:
                    parts.extend(data['aliases'])
                
                if 'descriptionUser' in data and data['descriptionUser']:
                    parts.append(data['descriptionUser'])
                
                if 'descriptionDev' in data and data['descriptionDev']:
                    parts.append(data['descriptionDev'])
                
                if 'category' in data and data['category']:
                    parts.append(data['category'])
                
                data['content'] = ' '.join(parts) if parts else None
        
        return data
    
    def __init__(self, **data):
        """初始化时设置时间戳"""
        if 'createdTime' not in data or data['createdTime'] is None:
            data['createdTime'] = datetime.now()
        if 'updatedTime' not in data or data['updatedTime'] is None:
            data['updatedTime'] = datetime.now()
        super().__init__(**data)
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )

# 请求/响应模型

class ToolCreateRequest(BaseModel):
    """创建工具请求"""
    displayName: str
    category: Optional[str] = None
    transport: TransportType = TransportType.HTTP
    endpoint: Optional[str] = None
    httpMethod: Optional[str] = "GET"
    description: Optional[str] = None
    inputSchema: Optional[Dict[str, Any]] = None
    visibility: VisibilityType = VisibilityType.PUBLIC

class ToolUpdateRequest(BaseModel):
    """更新工具请求"""
    displayName: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    inputSchema: Optional[Dict[str, Any]] = None
    visibility: Optional[VisibilityType] = None

class ToolResponse(BaseModel):
    """工具响应"""
    success: bool
    tool: Optional[ToolDocument] = None
    message: Optional[str] = None

class ToolListResponse(BaseModel):
    """工具列表响应"""
    success: bool
    tools: List[ToolDocument]
    total: int
    limit: int
    offset: int

class ValidationError(BaseModel):
    """验证错误"""
    field: str
    message: str
    code: Optional[str] = None

class ToolValidationResponse(BaseModel):
    """工具验证响应"""
    success: bool
    errors: List[ValidationError] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)

# 工具文档验证函数

def validate_tool_document(tool: ToolDocument) -> ToolValidationResponse:
    """验证工具文档的完整性和正确性"""
    errors = []
    warnings = []
    suggestions = []
    
    # 检查必填字段
    if not tool.displayName:
        errors.append(ValidationError(field="displayName", message="显示名称不能为空"))
    
    if not tool.toolId:
        errors.append(ValidationError(field="toolId", message="工具ID不能为空"))
    
    # 检查运行时配置
    if tool.runtime.transport == TransportType.HTTP:
        if not tool.runtime.endpoint:
            errors.append(ValidationError(field="runtime.endpoint", message="HTTP工具必须指定endpoint"))
        elif not tool.runtime.endpoint.startswith(('http://', 'https://')):
            errors.append(ValidationError(field="runtime.endpoint", message="endpoint必须是有效的HTTP URL"))
    
    elif tool.runtime.transport == TransportType.PYTHON:
        if not tool.runtime.script_content and not tool.runtime.script_path:
            errors.append(ValidationError(field="runtime.script", message="Python工具必须指定脚本内容或路径"))
        if not tool.runtime.entry_function:
            errors.append(ValidationError(field="runtime.entry_function", message="Python工具必须指定入口函数"))
    
    elif tool.runtime.transport == TransportType.STDIO:
        if not tool.runtime.command:
            errors.append(ValidationError(field="runtime.command", message="STDIO工具必须指定命令"))
    
    # 检查Schema格式
    if tool.inputsDeveloperSchema:
        if not isinstance(tool.inputsDeveloperSchema, dict):
            errors.append(ValidationError(field="inputsDeveloperSchema", message="输入Schema必须是有效的JSON对象"))
        elif tool.inputsDeveloperSchema.get("type") != "object":
            warnings.append("建议输入Schema的根类型为object")
    
    if tool.outputsSchema:
        if not isinstance(tool.outputsSchema, dict):
            errors.append(ValidationError(field="outputsSchema", message="输出Schema必须是有效的JSON对象"))
    
    # 提供改进建议
    if not tool.descriptionUser:
        suggestions.append("建议添加用户友好的描述")
    
    if not tool.examples:
        suggestions.append("建议添加使用示例")
    
    if not tool.quickstarts:
        suggestions.append("建议添加快速启动代码")
    
    if not tool.category:
        suggestions.append("建议指定工具分类")
    
    return ToolValidationResponse(
        success=len(errors) == 0,
        errors=errors,
        warnings=warnings,
        suggestions=suggestions
    )