from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import json
import hashlib

from database import get_es_client, get_redis_client
from config import settings
from repositories.tool_repository import get_tool_repository
from repositories.query_builder import create_search_query

router = APIRouter(prefix="/api/tools", tags=["search"])

class SearchRequest(BaseModel):
    query: str = Field(..., min_length=1, description="搜索关键词")
    category: Optional[str] = Field(None, description="工具分类")
    transport: Optional[str] = Field(None, description="传输方式")
    visibility: Optional[str] = Field(None, description="可见性")
    limit: int = Field(20, ge=1, le=100, description="返回数量限制")
    offset: int = Field(0, ge=0, description="偏移量")

class AdvancedSearchRequest(BaseModel):
    """高级搜索请求"""
    # 基础搜索参数
    query: str = Field(..., description="搜索关键词")
    
    # 筛选条件
    categories: Optional[List[str]] = Field(None, description="工具分类列表")
    transports: Optional[List[str]] = Field(None, description="传输方式列表")
    visibility: Optional[str] = Field(None, description="可见性")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    # 搜索选项
    search_fields: List[str] = Field(
        default=["displayName", "descriptionUser", "content", "aliases"],
        description="搜索字段"
    )
    fuzzy: bool = Field(True, description="是否启用模糊搜索")
    boost_exact_match: bool = Field(True, description="是否提升精确匹配")
    
    # 排序和分页
    sort_by: str = Field("relevance", description="排序方式: relevance, name, date, popularity")
    sort_order: str = Field("desc", description="排序顺序: asc, desc")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    
    # 时间范围
    created_after: Optional[datetime] = Field(None, description="创建时间起始")
    created_before: Optional[datetime] = Field(None, description="创建时间结束")
    updated_after: Optional[datetime] = Field(None, description="更新时间起始")
    updated_before: Optional[datetime] = Field(None, description="更新时间结束")

class SearchResponse(BaseModel):
    """搜索响应"""
    success: bool
    query: str
    results: List[Dict[str, Any]]
    total: int
    page: int
    page_size: int
    total_pages: int
    took: Optional[int] = None
    filters: Dict[str, Any] = Field(default_factory=dict)
    aggregations: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None

class SearchSuggestion(BaseModel):
    """搜索建议"""
    text: str
    type: str  # tool_name, category, tag, alias
    score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class SearchStats(BaseModel):
    """搜索统计"""
    total_tools: int
    categories: Dict[str, int]
    transports: Dict[str, int]
    visibility: Dict[str, int]
    tags: Dict[str, int]
    recent_searches: List[str] = []
    popular_searches: List[str] = []

@router.get("/search", response_model=SearchResponse)
async def search_tools(
    q: str = Query(..., description="搜索关键词"),
    category: Optional[str] = Query(None, description="工具分类"),
    transport: Optional[str] = Query(None, description="传输方式"),
    visibility: Optional[str] = Query(None, description="可见性"),
    tags: Optional[str] = Query(None, description="标签，逗号分隔"),
    sort_by: str = Query("relevance", description="排序方式"),
    sort_order: str = Query("desc", description="排序顺序"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    fuzzy: bool = Query(True, description="是否启用模糊搜索"),
    include_aggregations: bool = Query(False, description="是否包含聚合信息")
):
    """关键词搜索工具 - 支持多种筛选和排序"""
    try:
        # 记录搜索查询
        await _record_search_query(q)
        
        # 检查缓存
        cache_key = _generate_cache_key(q, {
            "category": category,
            "transport": transport,
            "visibility": visibility,
            "tags": tags,
            "sort_by": sort_by,
            "sort_order": sort_order,
            "page": page,
            "page_size": page_size,
            "fuzzy": fuzzy
        })
        
        cached_result = await _get_cached_search_result(cache_key)
        if cached_result:
            return cached_result
        
        # 构建搜索请求
        search_request = AdvancedSearchRequest(
            query=q,
            categories=[category] if category else None,
            transports=[transport] if transport else None,
            visibility=visibility,
            tags=tags.split(",") if tags else None,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            page_size=page_size,
            fuzzy=fuzzy
        )
        
        # 执行搜索
        result = await _execute_advanced_search(search_request, include_aggregations)
        
        # 缓存结果
        await _cache_search_result(cache_key, result)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

async def _execute_advanced_search(request: AdvancedSearchRequest, include_aggregations: bool = False) -> SearchResponse:
    """执行高级搜索"""
    try:
        repository = await get_tool_repository()
        
        # 构建搜索查询
        query_body = await _build_search_query(request)
        
        # 添加聚合查询
        if include_aggregations:
            query_body["aggs"] = _build_aggregations()
        
        # 执行搜索
        start_time = datetime.now()
        
        if repository:
            # 使用 Elasticsearch
            search_result = await repository.search(
                query=request.query,
                filters={
                    "categories": request.categories,
                    "transports": request.transports,
                    "visibility": request.visibility,
                    "tags": request.tags
                },
                sort_by=request.sort_by,
                sort_order=request.sort_order,
                skip=(request.page - 1) * request.page_size,
                limit=request.page_size
            )
            
            results = search_result
            total = len(results)  # 这里应该从 ES 响应中获取总数
            took = int((datetime.now() - start_time).total_seconds() * 1000)
            
        else:
            # 使用 mock 数据
            results, total, took = await _search_mock_data(request)
        
        # 计算分页信息
        total_pages = (total + request.page_size - 1) // request.page_size
        
        # 构建响应
        response = SearchResponse(
            success=True,
            query=request.query,
            results=results,
            total=total,
            page=request.page,
            page_size=request.page_size,
            total_pages=total_pages,
            took=took,
            filters={
                "categories": request.categories,
                "transports": request.transports,
                "visibility": request.visibility,
                "tags": request.tags,
                "sort_by": request.sort_by,
                "sort_order": request.sort_order
            }
        )
        
        return response
        
    except Exception as e:
        raise Exception(f"搜索执行失败: {str(e)}")

async def _build_search_query(request: AdvancedSearchRequest) -> Dict[str, Any]:
    """构建搜索查询"""
    query_body = {
        "query": {
            "bool": {
                "should": [],
                "filter": [],
                "minimum_should_match": 1
            }
        },
        "from": (request.page - 1) * request.page_size,
        "size": request.page_size
    }
    
    # 构建多字段搜索
    for field in request.search_fields:
        field_query = {
            "match": {
                field: {
                    "query": request.query,
                    "boost": _get_field_boost(field)
                }
            }
        }
        
        if request.fuzzy:
            field_query["match"][field]["fuzziness"] = "AUTO"
        
        query_body["query"]["bool"]["should"].append(field_query)
    
    # 精确匹配提升
    if request.boost_exact_match:
        query_body["query"]["bool"]["should"].append({
            "match_phrase": {
                "displayName": {
                    "query": request.query,
                    "boost": 5.0
                }
            }
        })
    
    # 添加筛选条件
    if request.categories:
        query_body["query"]["bool"]["filter"].append({
            "terms": {"category": request.categories}
        })
    
    if request.transports:
        query_body["query"]["bool"]["filter"].append({
            "terms": {"transport": request.transports}
        })
    
    if request.visibility:
        query_body["query"]["bool"]["filter"].append({
            "term": {"visibility": request.visibility}
        })
    
    if request.tags:
        query_body["query"]["bool"]["filter"].append({
            "terms": {"tags": request.tags}
        })
    
    # 时间范围筛选
    date_filters = []
    if request.created_after or request.created_before:
        created_range = {}
        if request.created_after:
            created_range["gte"] = request.created_after.isoformat()
        if request.created_before:
            created_range["lte"] = request.created_before.isoformat()
        date_filters.append({"range": {"createdTime": created_range}})
    
    if request.updated_after or request.updated_before:
        updated_range = {}
        if request.updated_after:
            updated_range["gte"] = request.updated_after.isoformat()
        if request.updated_before:
            updated_range["lte"] = request.updated_before.isoformat()
        date_filters.append({"range": {"updatedTime": updated_range}})
    
    if date_filters:
        query_body["query"]["bool"]["filter"].extend(date_filters)
    
    # 添加排序
    sort_config = _build_sort_config(request.sort_by, request.sort_order)
    if sort_config:
        query_body["sort"] = sort_config
    
    return query_body

def _get_field_boost(field: str) -> float:
    """获取字段权重"""
    boost_map = {
        "displayName": 3.0,
        "aliases": 2.5,
        "descriptionUser": 2.0,
        "category": 1.5,
        "content": 1.0,
        "tags": 1.2
    }
    return boost_map.get(field, 1.0)

def _build_sort_config(sort_by: str, sort_order: str) -> List[Dict[str, Any]]:
    """构建排序配置"""
    sort_configs = {
        "relevance": [{"_score": {"order": sort_order}}],
        "name": [{"displayName.keyword": {"order": sort_order}}, "_score"],
        "date": [{"updatedTime": {"order": sort_order}}, "_score"],
        "created": [{"createdTime": {"order": sort_order}}, "_score"],
        "popularity": [{"metadata.usage_count": {"order": sort_order, "missing": 0}}, "_score"]
    }
    
    return sort_configs.get(sort_by, sort_configs["relevance"])

def _build_aggregations() -> Dict[str, Any]:
    """构建聚合查询"""
    return {
        "categories": {
            "terms": {"field": "category", "size": 20}
        },
        "transports": {
            "terms": {"field": "transport", "size": 10}
        },
        "visibility": {
            "terms": {"field": "visibility", "size": 5}
        },
        "tags": {
            "terms": {"field": "tags", "size": 30}
        },
        "created_date_histogram": {
            "date_histogram": {
                "field": "createdTime",
                "calendar_interval": "month"
            }
        }
    }

async def _search_mock_data(request: AdvancedSearchRequest) -> tuple[List[Dict[str, Any]], int, int]:
    """使用 mock 数据进行搜索"""
    from mock_data import MOCK_TOOLS
    
    start_time = datetime.now()
    
    # 简单的关键词匹配
    results = []
    query_lower = request.query.lower()
    
    for tool in MOCK_TOOLS:
        # 构建搜索内容
        search_content = " ".join([
            tool.get("displayName", ""),
            tool.get("descriptionUser", ""),
            tool.get("content", ""),
            " ".join(tool.get("aliases", [])),
            " ".join(tool.get("tags", []))
        ]).lower()
        
        # 关键词匹配
        if query_lower in search_content:
            # 应用筛选条件
            if request.categories and tool.get("category") not in request.categories:
                continue
            if request.transports and tool.get("transport") not in request.transports:
                continue
            if request.visibility and tool.get("visibility") != request.visibility:
                continue
            if request.tags:
                tool_tags = tool.get("tags", [])
                if not any(tag in tool_tags for tag in request.tags):
                    continue
            
            # 计算相关性分数
            score = _calculate_relevance_score(tool, request.query)
            tool_with_score = tool.copy()
            tool_with_score["_score"] = score
            
            results.append(tool_with_score)
    
    # 排序
    if request.sort_by == "relevance":
        results.sort(key=lambda x: x.get("_score", 0), reverse=(request.sort_order == "desc"))
    elif request.sort_by == "name":
        results.sort(key=lambda x: x.get("displayName", ""), reverse=(request.sort_order == "desc"))
    elif request.sort_by == "date":
        results.sort(key=lambda x: x.get("updatedTime", ""), reverse=(request.sort_order == "desc"))
    
    # 分页
    total = len(results)
    start_idx = (request.page - 1) * request.page_size
    end_idx = start_idx + request.page_size
    paginated_results = results[start_idx:end_idx]
    
    took = int((datetime.now() - start_time).total_seconds() * 1000)
    
    return paginated_results, total, took

def _calculate_relevance_score(tool: Dict[str, Any], query: str) -> float:
    """计算相关性分数"""
    score = 0.0
    query_lower = query.lower()
    
    # 工具名称匹配
    display_name = tool.get("displayName", "").lower()
    if query_lower == display_name:
        score += 10.0
    elif query_lower in display_name:
        score += 5.0
    
    # 别名匹配
    for alias in tool.get("aliases", []):
        if query_lower == alias.lower():
            score += 8.0
        elif query_lower in alias.lower():
            score += 3.0
    
    # 描述匹配
    description = tool.get("descriptionUser", "").lower()
    if query_lower in description:
        score += 2.0
    
    # 标签匹配
    for tag in tool.get("tags", []):
        if query_lower == tag.lower():
            score += 4.0
        elif query_lower in tag.lower():
            score += 1.5
    
    # 分类匹配
    category = tool.get("category", "").lower()
    if query_lower == category:
        score += 3.0
    elif query_lower in category:
        score += 1.0
    
    return score

@router.post("/")
async def advanced_search(request: SearchRequest):
    """高级搜索 - POST 方式支持复杂查询"""
    try:
        es_client = get_es_client()
        
        if not es_client:
            # Fallback to simple search
            return await search_tools(
                q=request.query,
                category=request.category,
                transport=request.transport,
                visibility=request.visibility,
                limit=request.limit,
                offset=request.offset
            )
        
        # 构建复杂查询
        query_body = {
            "query": {
                "bool": {
                    "should": [
                        # 精确匹配工具名称
                        {
                            "match": {
                                "displayName": {
                                    "query": request.query,
                                    "boost": 3.0
                                }
                            }
                        },
                        # 模糊匹配描述
                        {
                            "match": {
                                "descriptionUser": {
                                    "query": request.query,
                                    "boost": 2.0,
                                    "fuzziness": "AUTO"
                                }
                            }
                        },
                        # 别名匹配
                        {
                            "terms": {
                                "aliases": request.query.split(),
                                "boost": 2.5
                            }
                        },
                        # 内容全文搜索
                        {
                            "match": {
                                "content": {
                                    "query": request.query,
                                    "boost": 1.0
                                }
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            },
            "from": request.offset,
            "size": request.limit,
            "sort": [
                "_score",
                {"updatedTime": {"order": "desc"}}
            ]
        }
        
        # 添加筛选条件
        filters = []
        if request.category:
            filters.append({"term": {"category": request.category}})
        if request.transport:
            filters.append({"term": {"capabilities": request.transport}})
        if request.visibility:
            filters.append({"term": {"visibility": request.visibility}})
        
        if filters:
            query_body["query"]["bool"]["filter"] = filters
        
        response = await es_client.search(
            index=settings.es_index,
            body=query_body
        )
        
        results = [hit["_source"] for hit in response["hits"]["hits"]]
        total = response["hits"]["total"]["value"]
        
        return {
            "success": True,
            "query": request.query,
            "results": results,
            "total": total,
            "limit": request.limit,
            "offset": request.offset,
            "searchType": "advanced"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"高级搜索失败: {str(e)}")

@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., description="搜索前缀"),
    limit: int = Query(10, description="建议数量")
):
    """获取搜索建议"""
    try:
        es_client = get_es_client()
        redis_client = get_redis_client()
        
        if not es_client:
            # 使用 mock 数据生成建议
            from mock_data import MOCK_TOOLS
            
            suggestions = []
            for tool in MOCK_TOOLS:
                # 检查工具名称
                if q.lower() in tool.get("displayName", "").lower():
                    suggestions.append({
                        "text": tool["displayName"],
                        "type": "tool_name",
                        "toolId": tool["toolId"]
                    })
                
                # 检查别名
                for alias in tool.get("aliases", []):
                    if q.lower() in alias.lower():
                        suggestions.append({
                            "text": alias,
                            "type": "alias",
                            "toolId": tool["toolId"]
                        })
                
                # 检查分类
                if q.lower() in tool.get("category", "").lower():
                    suggestions.append({
                        "text": tool["category"],
                        "type": "category"
                    })
            
            # 去重并限制数量
            unique_suggestions = []
            seen = set()
            for suggestion in suggestions:
                key = f"{suggestion['text']}_{suggestion['type']}"
                if key not in seen:
                    seen.add(key)
                    unique_suggestions.append(suggestion)
                    if len(unique_suggestions) >= limit:
                        break
            
            return {
                "success": True,
                "query": q,
                "suggestions": unique_suggestions
            }
        
        # 使用仓库获取建议
        repository = get_tool_repository(es_client, redis_client)
        suggestions = await repository.get_suggestions(q, limit)
        
        return {
            "success": True,
            "query": q,
            "suggestions": suggestions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取搜索建议失败: {str(e)}")

@router.get("/categories")
async def get_categories():
    """获取所有工具分类"""
    try:
        es_client = get_es_client()
        redis_client = get_redis_client()
        
        if not es_client:
            # 从 mock 数据提取分类
            from mock_data import MOCK_TOOLS
            categories = list(set(tool.get("category") for tool in MOCK_TOOLS if tool.get("category")))
            
            return {
                "success": True,
                "categories": [
                    {"name": cat, "count": len([t for t in MOCK_TOOLS if t.get("category") == cat])}
                    for cat in sorted(categories)
                ]
            }
        
        # 使用仓库获取分类
        repository = get_tool_repository(es_client, redis_client)
        categories = await repository.get_categories()
        
        return {
            "success": True,
            "categories": categories
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.get("/stats")
async def get_search_stats():
    """获取搜索统计信息"""
    try:
        es_client = get_es_client()
        
        if not es_client:
            # 使用 mock 数据生成统计
            from mock_data import MOCK_TOOLS
            
            stats = {
                "total_tools": len(MOCK_TOOLS),
                "categories": len(set(tool.get("category") for tool in MOCK_TOOLS)),
                "transports": len(set(cap for tool in MOCK_TOOLS for cap in tool.get("capabilities", []))),
                "public_tools": len([t for t in MOCK_TOOLS if t.get("visibility") == "public"]),
                "internal_tools": len([t for t in MOCK_TOOLS if t.get("visibility") == "internal"])
            }
            
            return {
                "success": True,
                "stats": stats
            }
        
        # 使用聚合查询获取统计信息
        stats_body = {
            "size": 0,
            "aggs": {
                "total_tools": {
                    "value_count": {"field": "toolId"}
                },
                "categories": {
                    "cardinality": {"field": "category"}
                },
                "visibility": {
                    "terms": {"field": "visibility"}
                },
                "transports": {
                    "terms": {"field": "capabilities"}
                }
            }
        }
        
        response = await es_client.search(
            index=settings.es_index,
            body=stats_body
        )
        
        aggs = response["aggregations"]
        
        stats = {
            "total_tools": aggs["total_tools"]["value"],
            "categories": aggs["categories"]["value"],
            "visibility": {
                bucket["key"]: bucket["doc_count"] 
                for bucket in aggs["visibility"]["buckets"]
            },
            "transports": {
                bucket["key"]: bucket["doc_count"]
                for bucket in aggs["transports"]["buckets"]
            }
        }
        
        return {
            "success": True,
            "stats": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

# 缓存和辅助函数
async def _record_search_query(query: str):
    """记录搜索查询"""
    try:
        # 这里可以记录到数据库或日志
        print(f"搜索查询: {query}")
    except Exception as e:
        print(f"记录搜索查询失败: {e}")

def _generate_cache_key(query: str, filters: Dict[str, Any]) -> str:
    """生成缓存键"""
    cache_data = {"query": query, "filters": filters}
    cache_str = json.dumps(cache_data, sort_keys=True)
    return f"search:{hashlib.md5(cache_str.encode()).hexdigest()}"

async def _get_cached_search_result(cache_key: str) -> Optional[SearchResponse]:
    """获取缓存的搜索结果"""
    try:
        # 这里应该从 Redis 获取缓存
        # 暂时返回 None
        return None
    except Exception as e:
        print(f"获取缓存失败: {e}")
        return None

async def _cache_search_result(cache_key: str, result: SearchResponse, ttl: int = 300):
    """缓存搜索结果"""
    try:
        # 这里应该保存到 Redis
        # 暂时跳过
        pass
    except Exception as e:
        print(f"缓存结果失败: {e}")

@router.post("/search/advanced", response_model=SearchResponse)
async def advanced_search_tools(request: AdvancedSearchRequest):
    """高级搜索工具 - 支持复杂查询条件"""
    try:
        # 记录搜索查询
        await _record_search_query(request.query)
        
        # 执行搜索
        result = await _execute_advanced_search(request, include_aggregations=True)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"高级搜索失败: {str(e)}")

@router.get("/search/suggestions", response_model=List[SearchSuggestion])
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="搜索前缀"),
    limit: int = Query(10, ge=1, le=50, description="建议数量"),
    types: Optional[str] = Query(None, description="建议类型，逗号分隔")
):
    """获取搜索建议"""
    try:
        suggestion_types = types.split(",") if types else ["tool_name", "category", "tag", "alias"]
        
        suggestions = await _generate_search_suggestions(q, limit, suggestion_types)
        
        return suggestions
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取搜索建议失败: {str(e)}")

async def _generate_search_suggestions(query: str, limit: int, types: List[str]) -> List[SearchSuggestion]:
    """生成搜索建议"""
    suggestions = []
    query_lower = query.lower()
    
    try:
        repository = await get_tool_repository()
        
        if repository:
            # 使用 Elasticsearch 的建议功能
            # 这里应该实现 ES 的 completion suggester
            pass
        
        # 使用 mock 数据生成建议
        from mock_data import MOCK_TOOLS
        
        seen = set()
        
        for tool in MOCK_TOOLS:
            # 工具名称建议
            if "tool_name" in types:
                display_name = tool.get("displayName", "")
                if query_lower in display_name.lower() and display_name not in seen:
                    suggestions.append(SearchSuggestion(
                        text=display_name,
                        type="tool_name",
                        score=_calculate_suggestion_score(display_name, query),
                        metadata={"tool_id": tool.get("toolId")}
                    ))
                    seen.add(display_name)
            
            # 别名建议
            if "alias" in types:
                for alias in tool.get("aliases", []):
                    if query_lower in alias.lower() and alias not in seen:
                        suggestions.append(SearchSuggestion(
                            text=alias,
                            type="alias",
                            score=_calculate_suggestion_score(alias, query),
                            metadata={"tool_id": tool.get("toolId")}
                        ))
                        seen.add(alias)
            
            # 分类建议
            if "category" in types:
                category = tool.get("category", "")
                if query_lower in category.lower() and category not in seen:
                    suggestions.append(SearchSuggestion(
                        text=category,
                        type="category",
                        score=_calculate_suggestion_score(category, query)
                    ))
                    seen.add(category)
            
            # 标签建议
            if "tag" in types:
                for tag in tool.get("tags", []):
                    if query_lower in tag.lower() and tag not in seen:
                        suggestions.append(SearchSuggestion(
                            text=tag,
                            type="tag",
                            score=_calculate_suggestion_score(tag, query)
                        ))
                        seen.add(tag)
        
        # 按分数排序并限制数量
        suggestions.sort(key=lambda x: x.score or 0, reverse=True)
        return suggestions[:limit]
        
    except Exception as e:
        print(f"生成建议失败: {e}")
        return []

def _calculate_suggestion_score(text: str, query: str) -> float:
    """计算建议分数"""
    text_lower = text.lower()
    query_lower = query.lower()
    
    if text_lower == query_lower:
        return 10.0
    elif text_lower.startswith(query_lower):
        return 8.0
    elif query_lower in text_lower:
        return 5.0
    else:
        return 1.0

@router.get("/search/filters")
async def get_search_filters():
    """获取搜索筛选选项"""
    try:
        repository = await get_tool_repository()
        
        if repository:
            # 从 Elasticsearch 获取聚合数据
            filters = await repository.get_search_filters()
        else:
            # 使用 mock 数据
            from mock_data import MOCK_TOOLS
            
            categories = list(set(tool.get("category") for tool in MOCK_TOOLS if tool.get("category")))
            transports = list(set(tool.get("transport") for tool in MOCK_TOOLS if tool.get("transport")))
            visibilities = list(set(tool.get("visibility") for tool in MOCK_TOOLS if tool.get("visibility")))
            
            all_tags = []
            for tool in MOCK_TOOLS:
                all_tags.extend(tool.get("tags", []))
            tags = list(set(all_tags))
            
            filters = {
                "categories": [{"name": cat, "count": len([t for t in MOCK_TOOLS if t.get("category") == cat])} for cat in sorted(categories)],
                "transports": [{"name": trans, "count": len([t for t in MOCK_TOOLS if t.get("transport") == trans])} for trans in sorted(transports)],
                "visibilities": [{"name": vis, "count": len([t for t in MOCK_TOOLS if t.get("visibility") == vis])} for vis in sorted(visibilities)],
                "tags": [{"name": tag, "count": all_tags.count(tag)} for tag in sorted(tags)]
            }
        
        return {
            "success": True,
            "filters": filters
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取筛选选项失败: {str(e)}")

@router.get("/search/popular")
async def get_popular_searches(limit: int = Query(10, ge=1, le=50)):
    """获取热门搜索"""
    try:
        # 这里应该从搜索日志或统计数据中获取
        popular_searches = [
            {"query": "天气", "count": 150},
            {"query": "API", "count": 120},
            {"query": "数据库", "count": 100},
            {"query": "文件", "count": 85},
            {"query": "邮件", "count": 70},
            {"query": "图片", "count": 65},
            {"query": "用户", "count": 60},
            {"query": "密码", "count": 55},
            {"query": "日志", "count": 50},
            {"query": "配置", "count": 45}
        ]
        
        return {
            "success": True,
            "popular_searches": popular_searches[:limit]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门搜索失败: {str(e)}")

@router.get("/search/recent")
async def get_recent_searches(limit: int = Query(10, ge=1, le=50)):
    """获取最近搜索"""
    try:
        # 这里应该从用户会话或数据库中获取
        recent_searches = [
            {"query": "Python工具", "timestamp": datetime.now().isoformat()},
            {"query": "HTTP API", "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat()},
            {"query": "文件处理", "timestamp": (datetime.now() - timedelta(minutes=10)).isoformat()},
            {"query": "数据转换", "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat()},
            {"query": "邮件发送", "timestamp": (datetime.now() - timedelta(minutes=20)).isoformat()}
        ]
        
        return {
            "success": True,
            "recent_searches": recent_searches[:limit]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取最近搜索失败: {str(e)}")

@router.delete("/search/cache")
async def clear_search_cache():
    """清除搜索缓存"""
    try:
        # 这里应该清除 Redis 中的搜索缓存
        # 暂时模拟成功
        
        return {
            "success": True,
            "message": "搜索缓存已清除"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

# 语义搜索相关端点
@router.get("/search/semantic", response_model=SearchResponse)
async def semantic_search_tools(
    q: str = Query(..., description="搜索查询"),
    top_k: int = Query(20, ge=1, le=100, description="返回结果数量"),
    similarity_threshold: float = Query(0.1, ge=0.0, le=1.0, description="相似度阈值"),
    category: Optional[str] = Query(None, description="分类筛选"),
    transport: Optional[str] = Query(None, description="传输方式筛选"),
    include_scores: bool = Query(True, description="是否包含相似度分数")
):
    """语义搜索工具"""
    try:
        from services.vector_search_service import get_vector_search_service
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 执行语义搜索
        semantic_results = await vector_service.semantic_search(
            query=q,
            top_k=top_k,
            similarity_threshold=similarity_threshold
        )
        
        # 应用筛选条件
        filtered_results = []
        for result in semantic_results:
            tool_data = result["tool_data"]
            
            # 分类筛选
            if category and tool_data.get("category") != category:
                continue
            
            # 传输方式筛选
            if transport and tool_data.get("transport") != transport:
                continue
            
            # 添加语义分数
            if include_scores:
                tool_data["_semantic_score"] = result["similarity"]
            
            filtered_results.append(tool_data)
        
        # 构建响应
        response = SearchResponse(
            success=True,
            query=q,
            results=filtered_results,
            total=len(filtered_results),
            page=1,
            page_size=len(filtered_results),
            total_pages=1,
            filters={
                "category": category,
                "transport": transport,
                "similarity_threshold": similarity_threshold
            }
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"语义搜索失败: {str(e)}")

@router.get("/search/hybrid", response_model=SearchResponse)
async def hybrid_search_tools(
    q: str = Query(..., description="搜索查询"),
    semantic_weight: float = Query(0.3, ge=0.0, le=1.0, description="语义搜索权重"),
    keyword_weight: float = Query(0.7, ge=0.0, le=1.0, description="关键词搜索权重"),
    top_k: int = Query(20, ge=1, le=100, description="返回结果数量"),
    category: Optional[str] = Query(None, description="分类筛选"),
    transport: Optional[str] = Query(None, description="传输方式筛选"),
    include_scores: bool = Query(True, description="是否包含分数详情")
):
    """混合搜索（关键词 + 语义）"""
    try:
        from services.vector_search_service import get_vector_search_service
        
        # 确保权重和为1
        total_weight = semantic_weight + keyword_weight
        if total_weight > 0:
            semantic_weight = semantic_weight / total_weight
            keyword_weight = keyword_weight / total_weight
        
        # 执行关键词搜索
        keyword_search_request = AdvancedSearchRequest(
            query=q,
            categories=[category] if category else None,
            transports=[transport] if transport else None,
            page=1,
            page_size=top_k * 2  # 获取更多结果用于混合
        )
        
        keyword_results, _, _ = await _search_mock_data(keyword_search_request)
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 执行混合搜索
        hybrid_results = await vector_service.hybrid_search(
            query=q,
            keyword_results=keyword_results,
            semantic_weight=semantic_weight,
            keyword_weight=keyword_weight,
            top_k=top_k
        )
        
        # 添加分数信息
        if include_scores:
            for result in hybrid_results:
                if "_hybrid_score" not in result:
                    result["_hybrid_score"] = 0.0
                if "_keyword_score" not in result:
                    result["_keyword_score"] = 0.0
                if "_semantic_score" not in result:
                    result["_semantic_score"] = 0.0
        
        # 构建响应
        response = SearchResponse(
            success=True,
            query=q,
            results=hybrid_results,
            total=len(hybrid_results),
            page=1,
            page_size=len(hybrid_results),
            total_pages=1,
            filters={
                "category": category,
                "transport": transport,
                "semantic_weight": semantic_weight,
                "keyword_weight": keyword_weight
            }
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"混合搜索失败: {str(e)}")

@router.get("/tools/{tool_id}/similar")
async def get_similar_tools(
    tool_id: str,
    top_k: int = Query(5, ge=1, le=20, description="返回相似工具数量"),
    similarity_threshold: float = Query(0.2, ge=0.0, le=1.0, description="相似度阈值")
):
    """获取相似工具推荐"""
    try:
        from services.vector_search_service import get_vector_search_service
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 获取相似工具
        similar_tools = await vector_service.get_similar_tools(
            tool_id=tool_id,
            top_k=top_k
        )
        
        # 过滤低相似度结果
        filtered_tools = [
            tool for tool in similar_tools 
            if tool["similarity"] >= similarity_threshold
        ]
        
        return {
            "success": True,
            "tool_id": tool_id,
            "similar_tools": filtered_tools,
            "total": len(filtered_tools)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取相似工具失败: {str(e)}")

@router.post("/search/index-tool")
async def index_tool_for_search(
    tool_id: str,
    tool_data: Dict[str, Any]
):
    """为工具创建搜索索引"""
    try:
        from services.vector_search_service import get_vector_search_service
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 创建索引
        success = await vector_service.index_tool(tool_id, tool_data)
        
        if success:
            return {
                "success": True,
                "message": f"工具 {tool_id} 索引创建成功"
            }
        else:
            raise HTTPException(status_code=500, detail="索引创建失败")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建索引失败: {str(e)}")

@router.delete("/search/index-tool/{tool_id}")
async def remove_tool_index(tool_id: str):
    """移除工具搜索索引"""
    try:
        from services.vector_search_service import get_vector_search_service
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 移除索引
        success = await vector_service.remove_tool_index(tool_id)
        
        if success:
            return {
                "success": True,
                "message": f"工具 {tool_id} 索引移除成功"
            }
        else:
            return {
                "success": False,
                "message": f"工具 {tool_id} 索引不存在"
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"移除索引失败: {str(e)}")

@router.get("/search/vector-stats")
async def get_vector_search_stats():
    """获取向量搜索统计信息"""
    try:
        from services.vector_search_service import get_vector_search_service
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 获取统计信息
        stats = await vector_service.get_index_stats()
        
        return {
            "success": True,
            "stats": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/search/initialize-vectors")
async def initialize_vector_search():
    """初始化向量搜索（批量索引现有工具）"""
    try:
        from services.vector_search_service import get_vector_search_service
        from mock_data import MOCK_TOOLS
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 批量索引现有工具
        indexed_count = 0
        failed_count = 0
        
        for tool in MOCK_TOOLS:
            tool_id = tool.get("toolId")
            if tool_id:
                success = await vector_service.index_tool(tool_id, tool)
                if success:
                    indexed_count += 1
                else:
                    failed_count += 1
        
        return {
            "success": True,
            "message": "向量搜索初始化完成",
            "indexed_count": indexed_count,
            "failed_count": failed_count,
            "total_tools": len(MOCK_TOOLS)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"初始化向量搜索失败: {str(e)}")

@router.delete("/search/vector-cache")
async def clear_vector_cache():
    """清除向量缓存"""
    try:
        from services.vector_search_service import get_vector_search_service
        
        # 获取向量搜索服务
        vector_service = await get_vector_search_service()
        
        # 清除缓存
        success = await vector_service.clear_cache()
        
        if success:
            return {
                "success": True,
                "message": "向量缓存已清除"
            }
        else:
            raise HTTPException(status_code=500, detail="清除缓存失败")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")