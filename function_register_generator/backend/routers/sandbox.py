from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import asyncio
import json
import subprocess
import tempfile
import os
import base64
import time

router = APIRouter()

class HTTPSandboxRequest(BaseModel):
    url: str
    method: str = "GET"
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    data: Optional[Dict[str, Any]] = None
    timeout: int = 30

class PythonSandboxRequest(BaseModel):
    script_content: str
    entry_function: str
    args: Optional[Dict[str, Any]] = None
    timeout: int = 30

class MCPSandboxRequest(BaseModel):
    transport: str  # "stdio" or "http"
    command: Optional[str] = None
    args: Optional[List[str]] = None
    url: Optional[str] = None
    timeout: int = 30

@router.post("/validate-http")
async def validate_http_tool(request: HTTPSandboxRequest):
    """在沙盒环境中验证 HTTP 工具"""
    try:
        import httpx
        
        # 构建请求参数
        request_kwargs = {
            "method": request.method,
            "url": request.url,
            "timeout": request.timeout
        }
        
        if request.headers:
            request_kwargs["headers"] = request.headers
        
        if request.params:
            request_kwargs["params"] = request.params
            
        if request.data and request.method.upper() in ["POST", "PUT", "PATCH"]:
            request_kwargs["json"] = request.data
        
        start_time = time.time()
        
        # 执行 HTTP 请求
        async with httpx.AsyncClient() as client:
            response = await client.request(**request_kwargs)
        
        end_time = time.time()
        latency = int((end_time - start_time) * 1000)  # 毫秒
        
        # 尝试解析响应
        try:
            response_data = response.json()
        except:
            response_data = response.text
        
        # 推断输出 schema
        output_schema = infer_schema_from_data(response_data)
        
        return {
            "success": True,
            "validation": {
                "status_code": response.status_code,
                "latency_ms": latency,
                "response_size": len(response.content),
                "content_type": response.headers.get("content-type", "unknown")
            },
            "sample_response": response_data,
            "inferred_output_schema": output_schema,
            "quickstart": {
                "curl": generate_curl_command(request),
                "python": generate_python_code(request)
            }
        }
        
    except httpx.TimeoutException:
        raise HTTPException(status_code=408, detail="请求超时")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="无法连接到目标服务器")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"HTTP 验证失败: {str(e)}")

@router.post("/validate-python")
async def validate_python_tool(request: PythonSandboxRequest):
    """在沙盒环境中验证 Python 脚本"""
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            # 写入脚本内容
            f.write(request.script_content)
            f.write(f"\n\n# 测试调用\n")
            
            # 构建测试调用
            if request.args:
                args_str = ", ".join([f"{k}={repr(v)}" for k, v in request.args.items()])
                f.write(f"result = {request.entry_function}({args_str})\n")
            else:
                f.write(f"result = {request.entry_function}()\n")
            
            f.write("print('RESULT:', result)\n")
            script_path = f.name
        
        try:
            start_time = time.time()
            
            # 在受限环境中执行 Python 脚本
            process = await asyncio.create_subprocess_exec(
                "python3", script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/tmp",  # 限制工作目录
                env={
                    "PATH": "/usr/bin:/bin",  # 限制 PATH
                    "PYTHONPATH": "",
                    "HOME": "/tmp"
                }
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=request.timeout
            )
            
            end_time = time.time()
            latency = int((end_time - start_time) * 1000)
            
            # 解析输出
            stdout_text = stdout.decode('utf-8')
            stderr_text = stderr.decode('utf-8')
            
            # 提取结果
            result_data = None
            for line in stdout_text.split('\n'):
                if line.startswith('RESULT:'):
                    try:
                        result_str = line[7:].strip()
                        result_data = eval(result_str)  # 注意：生产环境应使用更安全的解析
                    except:
                        result_data = result_str
                    break
            
            # 推断输出 schema
            output_schema = infer_schema_from_data(result_data)
            
            return {
                "success": True,
                "validation": {
                    "exit_code": process.returncode,
                    "latency_ms": latency,
                    "stdout_lines": len(stdout_text.split('\n')),
                    "stderr_lines": len(stderr_text.split('\n'))
                },
                "execution": {
                    "stdout": stdout_text,
                    "stderr": stderr_text,
                    "result": result_data
                },
                "inferred_output_schema": output_schema,
                "quickstart": {
                    "python": f"result = {request.entry_function}({', '.join([f'{k}={repr(v)}' for k, v in (request.args or {}).items()])})\nprint(result)"
                }
            }
            
        finally:
            # 清理临时文件
            os.unlink(script_path)
            
    except asyncio.TimeoutError:
        raise HTTPException(status_code=408, detail="Python 脚本执行超时")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Python 验证失败: {str(e)}")

@router.post("/validate-mcp")
async def validate_mcp_server(request: MCPSandboxRequest):
    """在沙盒环境中验证 MCP 服务器"""
    try:
        if request.transport == "stdio":
            # 验证 stdio MCP 服务器
            if not request.command:
                raise HTTPException(status_code=400, detail="stdio 传输需要指定 command")
            
            cmd_args = [request.command] + (request.args or [])
            
            start_time = time.time()
            
            # 启动 MCP 服务器进程
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 发送初始化消息
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {"name": "tool-registry", "version": "1.0.0"}
                }
            }
            
            message_bytes = (json.dumps(init_message) + '\n').encode()
            process.stdin.write(message_bytes)
            await process.stdin.drain()
            
            # 读取响应
            response_line = await asyncio.wait_for(
                process.stdout.readline(),
                timeout=request.timeout
            )
            
            response_data = json.loads(response_line.decode())
            
            # 获取工具列表
            tools_message = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            message_bytes = (json.dumps(tools_message) + '\n').encode()
            process.stdin.write(message_bytes)
            await process.stdin.drain()
            
            tools_response = await asyncio.wait_for(
                process.stdout.readline(),
                timeout=5
            )
            
            tools_data = json.loads(tools_response.decode())
            
            # 终止进程
            process.terminate()
            await process.wait()
            
            end_time = time.time()
            latency = int((end_time - start_time) * 1000)
            
            return {
                "success": True,
                "validation": {
                    "transport": "stdio",
                    "latency_ms": latency,
                    "command": request.command,
                    "args": request.args
                },
                "server_info": response_data.get("result", {}),
                "available_tools": tools_data.get("result", {}).get("tools", []),
                "quickstart": {
                    "command": " ".join(cmd_args),
                    "mcp": json.dumps(init_message, indent=2)
                }
            }
            
        elif request.transport == "http":
            # 验证 HTTP MCP 服务器
            if not request.url:
                raise HTTPException(status_code=400, detail="http 传输需要指定 url")
            
            import httpx
            
            start_time = time.time()
            
            async with httpx.AsyncClient() as client:
                # 发送初始化请求
                init_response = await client.post(
                    request.url,
                    json={
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "initialize",
                        "params": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {}
                        }
                    },
                    timeout=request.timeout
                )
                
                init_data = init_response.json()
                
                # 获取工具列表
                tools_response = await client.post(
                    request.url,
                    json={
                        "jsonrpc": "2.0",
                        "id": 2,
                        "method": "tools/list"
                    },
                    timeout=5
                )
                
                tools_data = tools_response.json()
            
            end_time = time.time()
            latency = int((end_time - start_time) * 1000)
            
            return {
                "success": True,
                "validation": {
                    "transport": "http",
                    "latency_ms": latency,
                    "url": request.url,
                    "status_code": init_response.status_code
                },
                "server_info": init_data.get("result", {}),
                "available_tools": tools_data.get("result", {}).get("tools", []),
                "quickstart": {
                    "curl": f"curl -X POST {request.url} -H 'Content-Type: application/json' -d '{json.dumps(init_data)}'",
                    "mcp": json.dumps({"method": "tools/list"}, indent=2)
                }
            }
        
        else:
            raise HTTPException(status_code=400, detail="不支持的传输方式")
            
    except asyncio.TimeoutError:
        raise HTTPException(status_code=408, detail="MCP 服务器连接超时")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"MCP 验证失败: {str(e)}")

def infer_schema_from_data(data):
    """从数据推断 JSON Schema"""
    if data is None:
        return {"type": "null"}
    elif isinstance(data, bool):
        return {"type": "boolean"}
    elif isinstance(data, int):
        return {"type": "integer"}
    elif isinstance(data, float):
        return {"type": "number"}
    elif isinstance(data, str):
        return {"type": "string"}
    elif isinstance(data, list):
        if not data:
            return {"type": "array", "items": {}}
        # 推断数组项类型
        item_schema = infer_schema_from_data(data[0])
        return {"type": "array", "items": item_schema}
    elif isinstance(data, dict):
        properties = {}
        for key, value in data.items():
            properties[key] = infer_schema_from_data(value)
        return {
            "type": "object",
            "properties": properties,
            "required": list(data.keys())
        }
    else:
        return {"type": "string", "description": f"Unknown type: {type(data)}"}

def generate_curl_command(request: HTTPSandboxRequest) -> str:
    """生成 cURL 命令"""
    cmd_parts = ["curl"]
    
    if request.method.upper() != "GET":
        cmd_parts.extend(["-X", request.method.upper()])
    
    if request.headers:
        for key, value in request.headers.items():
            cmd_parts.extend(["-H", f"'{key}: {value}'"])
    
    if request.data and request.method.upper() in ["POST", "PUT", "PATCH"]:
        cmd_parts.extend(["-H", "'Content-Type: application/json'"])
        cmd_parts.extend(["-d", f"'{json.dumps(request.data)}'"])
    
    # 构建 URL
    url = request.url
    if request.params:
        import urllib.parse
        query_string = urllib.parse.urlencode(request.params)
        url = f"{url}?{query_string}"
    
    cmd_parts.append(f"'{url}'")
    
    return " ".join(cmd_parts)

def generate_python_code(request: HTTPSandboxRequest) -> str:
    """生成 Python 代码"""
    code_lines = [
        "import requests",
        "",
        f"response = requests.{request.method.lower()}("
    ]
    
    # URL
    url = request.url
    if request.params:
        import urllib.parse
        query_string = urllib.parse.urlencode(request.params)
        url = f"{url}?{query_string}"
    
    code_lines.append(f"    '{url}',")
    
    # Headers
    if request.headers:
        code_lines.append(f"    headers={json.dumps(request.headers, indent=4)},")
    
    # Data
    if request.data and request.method.upper() in ["POST", "PUT", "PATCH"]:
        code_lines.append(f"    json={json.dumps(request.data, indent=4)},")
    
    code_lines.append(f"    timeout={request.timeout}")
    code_lines.append(")")
    code_lines.append("")
    code_lines.append("data = response.json()")
    code_lines.append("print(data)")
    
    return "\n".join(code_lines)

@router.get("/status")
async def get_sandbox_status():
    """获取沙盒环境状态"""
    try:
        # 检查 Python 环境
        python_version = subprocess.check_output(
            ["python3", "--version"], 
            stderr=subprocess.STDOUT
        ).decode().strip()
        
        # 检查可用的包
        available_packages = []
        for package in ["requests", "httpx", "json", "base64", "datetime"]:
            try:
                subprocess.check_call(
                    ["python3", "-c", f"import {package}"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
                available_packages.append(package)
            except:
                pass
        
        # 检查系统资源
        import psutil
        
        return {
            "success": True,
            "sandbox_status": {
                "python_version": python_version,
                "available_packages": available_packages,
                "system": {
                    "cpu_percent": psutil.cpu_percent(),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_usage": psutil.disk_usage('/tmp').percent
                },
                "limits": {
                    "timeout_seconds": 30,
                    "memory_limit": "128MB",
                    "cpu_limit": "0.5 cores"
                }
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"无法获取沙盒状态: {str(e)}"
        }