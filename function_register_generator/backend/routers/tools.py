from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
import json

from database import get_es_client, get_redis_client
from config import settings
from mock_data import get_mock_tool_by_id
from services.llm_service import get_llm_service
from repositories.tool_repository import get_tool_repository

router = APIRouter()

class ToolRegistrationRequest(BaseModel):
    displayName: str
    category: str
    transport: str = "http"
    endpoint: Optional[str] = None
    httpMethod: Optional[str] = "GET"
    description: Optional[str] = None
    inputSchema: Optional[Dict[str, Any]] = None
    
class CurlRegistrationRequest(BaseModel):
    displayName: str
    curl: str
    description: Optional[str] = None

class PythonRegistrationRequest(BaseModel):
    displayName: str
    script_content: str
    entry_function: str
    description: Optional[str] = None

class MCPImportRequest(BaseModel):
    transport: str  # "stdio" or "http"
    command: Optional[str] = None
    args: Optional[List[str]] = None
    url: Optional[str] = None
    env: Optional[Dict[str, str]] = None

class NaturalLanguageToolRequest(BaseModel):
    description: str
    test_inputs: Optional[Dict[str, Any]] = None
    test_outputs: Optional[Dict[str, Any]] = None
    category: Optional[str] = None

class SmartRegistrationRequest(BaseModel):
    """智能注册请求"""
    # 基础信息
    display_name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    
    # cURL 相关
    curl_command: Optional[str] = None
    
    # 智能补全选项
    auto_complete: bool = True
    auto_validate: bool = True
    generate_examples: bool = True
    
    # 用户提供的额外信息
    expected_inputs: Optional[Dict[str, Any]] = None
    expected_outputs: Optional[Dict[str, Any]] = None
    use_cases: Optional[List[str]] = None

class SmartRegistrationResponse(BaseModel):
    """智能注册响应"""
    success: bool
    tool_id: Optional[str] = None
    message: str
    
    # 工具定义
    tool_definition: Optional[Dict[str, Any]] = None
    
    # 智能补全结果
    completion_suggestions: List[str] = []
    auto_generated_fields: List[str] = []
    confidence_score: Optional[float] = None
    
    # 验证结果
    validation_result: Optional[Dict[str, Any]] = None
    validation_warnings: List[str] = []
    
    # 修改建议
    improvement_suggestions: List[str] = []
    alternative_schemas: List[Dict[str, Any]] = []

class ToolModificationRequest(BaseModel):
    """工具修改请求"""
    tool_id: str
    modifications: Dict[str, Any]
    regenerate_examples: bool = False
    revalidate: bool = True

class MCPImportAdvancedRequest(BaseModel):
    """高级 MCP 导入请求"""
    # 连接配置
    transport: str  # "stdio" or "http"
    command: Optional[str] = None
    args: Optional[List[str]] = None
    url: Optional[str] = None
    env: Optional[Dict[str, str]] = None
    
    # 导入选项
    import_all: bool = True
    selected_tools: Optional[List[str]] = None
    
    # 自定义配置
    tool_prefix: Optional[str] = None
    category_override: Optional[str] = None
    description_template: Optional[str] = None
    
    # 验证和优化选项
    validate_tools: bool = True
    generate_examples: bool = True
    optimize_schemas: bool = True
    
    # 进度跟踪
    callback_url: Optional[str] = None
    async_import: bool = False

class MCPImportProgress(BaseModel):
    """MCP 导入进度"""
    import_id: str
    status: str  # "pending", "connecting", "discovering", "importing", "completed", "failed"
    progress: float  # 0.0 - 1.0
    current_step: str
    total_tools: int
    imported_tools: int
    failed_tools: int
    errors: List[str] = []
    warnings: List[str] = []
    start_time: str
    end_time: Optional[str] = None

class MCPImportResult(BaseModel):
    """MCP 导入结果"""
    success: bool
    import_id: str
    message: str
    
    # 服务器信息
    server_info: Optional[Dict[str, Any]] = None
    connection_time_ms: Optional[int] = None
    
    # 工具信息
    discovered_tools: List[Dict[str, Any]] = []
    imported_tools: List[Dict[str, Any]] = []
    failed_tools: List[Dict[str, Any]] = []
    
    # 统计信息
    total_discovered: int = 0
    total_imported: int = 0
    total_failed: int = 0
    
    # 进度和状态
    progress: Optional[MCPImportProgress] = None
    
    # 建议和警告
    suggestions: List[str] = []
    warnings: List[str] = []

class NaturalLanguageGenerationRequest(BaseModel):
    """自然语言工具生成请求"""
    # 基础描述
    description: str = Field(..., min_length=10, description="工具功能描述")
    
    # 可选的上下文信息
    use_cases: Optional[List[str]] = Field(None, description="使用场景列表")
    target_users: Optional[List[str]] = Field(None, description="目标用户群体")
    domain: Optional[str] = Field(None, description="应用领域")
    
    # 示例数据
    example_inputs: Optional[Dict[str, Any]] = Field(None, description="示例输入数据")
    example_outputs: Optional[Dict[str, Any]] = Field(None, description="示例输出数据")
    example_scenarios: Optional[List[Dict[str, Any]]] = Field(None, description="使用场景示例")
    
    # 生成选项
    preferred_transport: Optional[str] = Field(None, description="首选传输方式")
    complexity_level: str = Field("medium", description="复杂度级别: simple, medium, complex")
    include_validation: bool = Field(True, description="是否包含输入验证")
    generate_examples: bool = Field(True, description="是否生成使用示例")
    
    # 自定义配置
    tool_name_hint: Optional[str] = Field(None, description="工具名称提示")
    category_hint: Optional[str] = Field(None, description="分类提示")
    
    # 验证选项
    auto_validate: bool = Field(True, description="是否自动验证生成的工具")
    validation_timeout: int = Field(30, description="验证超时时间(秒)")

class GenerationResult(BaseModel):
    """工具生成结果"""
    success: bool
    generation_id: str
    message: str
    
    # 生成的工具定义
    tool_definition: Optional[Dict[str, Any]] = None
    
    # AI 分析结果
    analysis: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None
    reasoning: Optional[str] = None
    
    # 替代方案
    alternatives: List[Dict[str, Any]] = []
    
    # 验证结果
    validation_result: Optional[Dict[str, Any]] = None
    
    # 改进建议
    suggestions: List[str] = []
    warnings: List[str] = []
    
    # 生成统计
    generation_time_ms: Optional[int] = None
    tokens_used: Optional[int] = None

class ToolPreviewRequest(BaseModel):
    """工具预览请求"""
    tool_definition: Dict[str, Any]
    preview_type: str = Field("basic", description="预览类型: basic, detailed, interactive")
    include_examples: bool = Field(True, description="是否包含示例")
    include_schema_docs: bool = Field(True, description="是否包含Schema文档")

class ToolPreviewResponse(BaseModel):
    """工具预览响应"""
    success: bool
    preview_data: Dict[str, Any]
    interactive_demo: Optional[Dict[str, Any]] = None
    schema_documentation: Optional[str] = None
    usage_examples: List[Dict[str, Any]] = []
    warnings: List[str] = []

class ToolDraftRequest(BaseModel):
    """工具草稿创建请求"""
    tool_definition: Dict[str, Any]
    draft_name: Optional[str] = Field(None, description="草稿名称")
    description: Optional[str] = Field(None, description="草稿描述")
    tags: List[str] = Field(default_factory=list, description="草稿标签")
    auto_save: bool = Field(True, description="是否自动保存")
    expiry_hours: int = Field(24, description="草稿过期时间(小时)")

class ToolDraftResponse(BaseModel):
    """工具草稿响应"""
    success: bool
    draft_id: str
    message: str
    draft_data: Optional[Dict[str, Any]] = None
    expiry_time: Optional[str] = None
    edit_url: Optional[str] = None

class ToolEditRequest(BaseModel):
    """工具编辑请求"""
    draft_id: Optional[str] = Field(None, description="草稿ID")
    tool_id: Optional[str] = Field(None, description="现有工具ID")
    modifications: Dict[str, Any] = Field(..., description="修改内容")
    validation_level: str = Field("basic", description="验证级别: none, basic, full")
    auto_save: bool = Field(True, description="是否自动保存")
    create_backup: bool = Field(True, description="是否创建备份")

class ToolEditResponse(BaseModel):
    """工具编辑响应"""
    success: bool
    message: str
    updated_definition: Optional[Dict[str, Any]] = None
    validation_result: Optional[Dict[str, Any]] = None
    backup_id: Optional[str] = None
    warnings: List[str] = []
    errors: List[str] = []

class ToolPublishRequest(BaseModel):
    """工具发布请求"""
    draft_id: Optional[str] = Field(None, description="草稿ID")
    tool_definition: Optional[Dict[str, Any]] = Field(None, description="工具定义")
    publish_options: Dict[str, Any] = Field(default_factory=dict, description="发布选项")
    final_validation: bool = Field(True, description="是否进行最终验证")
    cleanup_draft: bool = Field(True, description="是否清理草稿")

class ToolPublishResponse(BaseModel):
    """工具发布响应"""
    success: bool
    message: str
    tool_id: Optional[str] = None
    published_tool: Optional[Dict[str, Any]] = None
    validation_result: Optional[Dict[str, Any]] = None
    warnings: List[str] = []

class ValidationRequest(BaseModel):
    """实时验证请求"""
    tool_definition: Dict[str, Any]
    validation_type: str = Field("full", description="验证类型: schema, runtime, full")
    field_name: Optional[str] = Field(None, description="特定字段验证")

class ValidationResponse(BaseModel):
    """实时验证响应"""
    success: bool
    field_errors: Dict[str, List[str]] = Field(default_factory=dict)
    global_errors: List[str] = []
    warnings: List[str] = []
    suggestions: List[str] = []
    validation_score: Optional[float] = None

@router.post("/register/smart", response_model=SmartRegistrationResponse)
async def smart_register_tool(request: SmartRegistrationRequest):
    """智能工具注册 - 集成解析、补全和验证"""
    try:
        print(f"开始智能注册工具: {request.display_name or 'unnamed'}")
        
        # 1. 解析输入数据
        parsed_data = await _parse_input_data(request)
        
        # 2. 智能补全工具定义
        completion_result = None
        if request.auto_complete:
            completion_result = await _intelligent_complete_tool(parsed_data, request)
        
        # 3. 生成最终工具定义
        tool_definition = await _generate_tool_definition(parsed_data, completion_result, request)
        
        # 4. 验证工具
        validation_result = None
        validation_warnings = []
        if request.auto_validate:
            validation_result, validation_warnings = await _validate_smart_tool(tool_definition, request)
        
        # 5. 生成示例
        if request.generate_examples and tool_definition.get("inputsDeveloperSchema"):
            examples = await _generate_tool_examples(tool_definition)
            tool_definition["examples"] = examples
        
        # 6. 保存工具
        repository = await get_tool_repository()
        await repository.create(tool_definition)
        
        # 7. 准备响应
        response = SmartRegistrationResponse(
            success=True,
            tool_id=tool_definition["toolId"],
            message="智能工具注册成功",
            tool_definition=tool_definition,
            completion_suggestions=completion_result.get("suggestions", []) if completion_result else [],
            auto_generated_fields=completion_result.get("generated_fields", []) if completion_result else [],
            confidence_score=completion_result.get("confidence", 0.0) if completion_result else None,
            validation_result=validation_result,
            validation_warnings=validation_warnings,
            improvement_suggestions=await _generate_improvement_suggestions(tool_definition),
            alternative_schemas=await _generate_alternative_schemas(tool_definition)
        )
        
        print(f"智能工具注册成功: {tool_definition['toolId']}")
        return response
        
    except Exception as e:
        print(f"智能工具注册失败: {e}")
        return SmartRegistrationResponse(
            success=False,
            message=f"智能工具注册失败: {str(e)}",
            validation_warnings=[str(e)]
        )

async def _parse_input_data(request: SmartRegistrationRequest) -> Dict[str, Any]:
    """解析输入数据"""
    parsed_data = {
        "display_name": request.display_name,
        "description": request.description,
        "category": request.category,
        "transport": None,
        "endpoint": None,
        "method": None,
        "headers": {},
        "params": {},
        "data": None
    }
    
    # 解析 cURL 命令
    if request.curl_command:
        try:
            from parsers.curl_parser import parse_curl
            curl_info = parse_curl(request.curl_command)
            parsed_data.update({
                "transport": "http",
                "endpoint": curl_info["url"],
                "method": curl_info["method"],
                "headers": curl_info.get("headers", {}),
                "params": curl_info.get("params", {}),
                "data": curl_info.get("data")
            })
            
            # 如果没有提供名称，从URL推断
            if not parsed_data["display_name"]:
                from urllib.parse import urlparse
                parsed_url = urlparse(curl_info["url"])
                parsed_data["display_name"] = f"{parsed_url.netloc} API"
                
        except Exception as e:
            print(f"cURL 解析失败: {e}")
            raise HTTPException(status_code=400, detail=f"cURL 解析失败: {str(e)}")
    
    return parsed_data

async def _intelligent_complete_tool(parsed_data: Dict[str, Any], request: SmartRegistrationRequest) -> Optional[Dict[str, Any]]:
    """智能补全工具定义"""
    try:
        from services.intelligent_completion_service import get_intelligent_completion_service, CompletionRequest
        
        completion_service = await get_intelligent_completion_service()
        
        # 构建补全请求
        completion_request = CompletionRequest(
            tool_name=parsed_data.get("display_name"),
            description=parsed_data.get("description"),
            transport=parsed_data.get("transport"),
            endpoint=parsed_data.get("endpoint"),
            curl_command=request.curl_command,
            test_inputs=request.expected_inputs,
            test_outputs=request.expected_outputs
        )
        
        result = await completion_service.complete_tool_definition(completion_request)
        
        if result.success:
            return {
                "tool_definition": result.tool_definition,
                "suggestions": result.suggestions,
                "confidence": result.confidence,
                "generated_fields": ["description", "inputSchema", "outputSchema", "examples"]
            }
        else:
            print(f"智能补全失败: {result.error}")
            return None
            
    except Exception as e:
        print(f"智能补全服务调用失败: {e}")
        return None

async def _generate_tool_definition(parsed_data: Dict[str, Any], completion_result: Optional[Dict[str, Any]], request: SmartRegistrationRequest) -> Dict[str, Any]:
    """生成最终工具定义"""
    import uuid
    
    # 基础工具定义
    tool_id = f"{parsed_data.get('transport', 'unknown')}.{(parsed_data.get('display_name', 'unnamed')).lower().replace(' ', '_')}.{uuid.uuid4().hex[:8]}"
    
    tool_definition = {
        "toolId": tool_id,
        "displayName": parsed_data.get("display_name", "未命名工具"),
        "category": parsed_data.get("category", "utility"),
        "transport": parsed_data.get("transport", "http"),
        "runtime": {
            "transport": parsed_data.get("transport", "http")
        },
        "inputsDeveloperSchema": {"type": "object", "properties": {}},
        "outputsSchema": {"type": "object", "properties": {"result": {"type": "string"}}},
        "descriptionUser": parsed_data.get("description", ""),
        "descriptionDev": parsed_data.get("description", ""),
        "capabilities": [],
        "aliases": [],
        "examples": [],
        "createdTime": datetime.utcnow().isoformat(),
        "updatedTime": datetime.utcnow().isoformat()
    }
    
    # 添加传输特定配置
    if parsed_data.get("transport") == "http":
        tool_definition["runtime"].update({
            "endpoint": parsed_data.get("endpoint"),
            "method": parsed_data.get("method", "GET"),
            "headers": parsed_data.get("headers", {}),
            "curl_command": request.curl_command
        })
        tool_definition["capabilities"].append("http_request")
    
    # 使用智能补全结果
    if completion_result and completion_result.get("tool_definition"):
        completed_def = completion_result["tool_definition"]
        
        # 合并补全结果
        tool_definition.update({
            "inputsDeveloperSchema": completed_def.get("inputsDeveloperSchema", tool_definition["inputsDeveloperSchema"]),
            "outputsSchema": completed_def.get("outputsSchema", tool_definition["outputsSchema"]),
            "descriptionUser": completed_def.get("descriptionUser", tool_definition["descriptionUser"]),
            "descriptionDev": completed_def.get("descriptionDev", tool_definition["descriptionDev"]),
            "examples": completed_def.get("examples", [])
        })
        
        if completed_def.get("aliases"):
            tool_definition["aliases"] = completed_def["aliases"]
    
    # 使用用户提供的期望输入输出
    if request.expected_inputs:
        tool_definition["inputsDeveloperSchema"] = _infer_schema_from_data(request.expected_inputs, "input")
    
    if request.expected_outputs:
        tool_definition["outputsSchema"] = _infer_schema_from_data(request.expected_outputs, "output")
    
    return tool_definition

def _infer_schema_from_data(data: Dict[str, Any], schema_type: str) -> Dict[str, Any]:
    """从数据推断 JSON Schema"""
    schema = {
        "type": "object",
        "properties": {}
    }
    
    for key, value in data.items():
        if isinstance(value, str):
            schema["properties"][key] = {"type": "string", "description": f"{key} 参数"}
        elif isinstance(value, int):
            schema["properties"][key] = {"type": "integer", "description": f"{key} 参数"}
        elif isinstance(value, float):
            schema["properties"][key] = {"type": "number", "description": f"{key} 参数"}
        elif isinstance(value, bool):
            schema["properties"][key] = {"type": "boolean", "description": f"{key} 参数"}
        elif isinstance(value, list):
            schema["properties"][key] = {"type": "array", "description": f"{key} 数组"}
        elif isinstance(value, dict):
            schema["properties"][key] = {"type": "object", "description": f"{key} 对象"}
        else:
            schema["properties"][key] = {"type": "string", "description": f"{key} 参数"}
    
    return schema

async def _validate_smart_tool(tool_definition: Dict[str, Any], request: SmartRegistrationRequest) -> tuple[Optional[Dict[str, Any]], List[str]]:
    """验证智能工具"""
    warnings = []
    
    try:
        if tool_definition.get("transport") == "http":
            from services.http_sandbox_service import get_http_sandbox_service, HTTPSandboxRequest
            
            sandbox = await get_http_sandbox_service()
            sandbox_request = HTTPSandboxRequest(
                url=tool_definition["runtime"]["endpoint"],
                method=tool_definition["runtime"].get("method", "GET"),
                headers=tool_definition["runtime"].get("headers", {})
            )
            
            async with sandbox:
                result = await sandbox.validate_http_tool(sandbox_request)
            
            if not result.success:
                warnings.append(f"HTTP 验证失败: {result.error}")
            
            return {
                "success": result.success,
                "status_code": result.status_code,
                "latency_ms": result.latency_ms,
                "content_type": result.content_type,
                "error": result.error
            }, warnings
            
    except Exception as e:
        warnings.append(f"验证过程出错: {str(e)}")
    
    return None, warnings

async def _generate_tool_examples(tool_definition: Dict[str, Any]) -> List[Dict[str, Any]]:
    """生成工具使用示例"""
    examples = []
    
    try:
        from services.llm_service import get_llm_service
        
        llm_service = await get_llm_service()
        
        # 使用 LLM 生成示例
        prompt = f"""
        为以下工具生成3个使用示例:
        工具名称: {tool_definition.get('displayName')}
        描述: {tool_definition.get('descriptionUser')}
        输入Schema: {json.dumps(tool_definition.get('inputsDeveloperSchema', {}), indent=2)}
        
        请生成实际可用的示例数据。
        """
        
        # 这里应该调用 LLM 服务，暂时返回基础示例
        input_schema = tool_definition.get("inputsDeveloperSchema", {})
        if input_schema.get("properties"):
            example_input = {}
            for prop_name, prop_def in input_schema["properties"].items():
                if prop_def.get("type") == "string":
                    example_input[prop_name] = f"示例{prop_name}"
                elif prop_def.get("type") == "integer":
                    example_input[prop_name] = 123
                elif prop_def.get("type") == "boolean":
                    example_input[prop_name] = True
                else:
                    example_input[prop_name] = "示例值"
            
            examples.append({
                "name": "基础使用示例",
                "description": f"使用 {tool_definition.get('displayName')} 的基础示例",
                "input": example_input,
                "expected_output": {"result": "示例输出"}
            })
    
    except Exception as e:
        print(f"生成示例失败: {e}")
    
    return examples

async def _generate_improvement_suggestions(tool_definition: Dict[str, Any]) -> List[str]:
    """生成改进建议"""
    suggestions = []
    
    # 检查描述
    if not tool_definition.get("descriptionUser") or len(tool_definition["descriptionUser"]) < 10:
        suggestions.append("建议添加更详细的工具描述")
    
    # 检查输入 schema
    input_schema = tool_definition.get("inputsDeveloperSchema", {})
    if not input_schema.get("properties"):
        suggestions.append("建议完善输入参数定义")
    
    # 检查示例
    if not tool_definition.get("examples"):
        suggestions.append("建议添加使用示例")
    
    # 检查别名
    if not tool_definition.get("aliases"):
        suggestions.append("建议添加工具别名以提高可发现性")
    
    return suggestions

async def _generate_alternative_schemas(tool_definition: Dict[str, Any]) -> List[Dict[str, Any]]:
    """生成替代 schema 方案"""
    alternatives = []
    
    # 这里可以基于工具类型生成不同的 schema 方案
    # 暂时返回空列表
    
    return alternatives

@router.post("/register")
async def register_tool(request: ToolRegistrationRequest):
    """基础工具注册 - 表单方式"""
    try:
        # 获取工具仓库
        repository = await get_tool_repository()
        
        # 生成工具ID
        tool_id = f"{request.transport}.{request.displayName.lower().replace(' ', '_')}"
        
        # 构建工具定义
        tool_definition = {
            "toolId": tool_id,
            "displayName": request.displayName,
            "category": request.category,
            "transport": request.transport,
            "runtime": {
                "transport": request.transport
            },
            "inputsDeveloperSchema": request.inputSchema or {"type": "object", "properties": {}},
            "outputsSchema": {"type": "object", "properties": {"result": {"type": "string"}}},
            "descriptionUser": request.description or "",
            "descriptionDev": request.description or "",
            "capabilities": [],
            "aliases": [],
            "examples": [],
            "createdTime": datetime.utcnow().isoformat(),
            "updatedTime": datetime.utcnow().isoformat()
        }
        
        # 根据传输方式添加特定配置
        if request.transport == "http":
            if request.endpoint:
                tool_definition["runtime"]["endpoint"] = request.endpoint
            if request.httpMethod:
                tool_definition["runtime"]["method"] = request.httpMethod
        
        # 保存工具
        await repository.create(tool_definition)
        
        return {
            "success": True,
            "tool_id": tool_id,
            "message": "工具注册成功",
            "tool_definition": tool_definition
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"工具注册失败: {str(e)}")
    try:
        # 生成工具 ID
        tool_id = request.displayName.lower().replace(" ", ".").replace("-", ".")
        
        # 构建工具文档
        tool_doc = {
            "toolId": tool_id,
            "displayName": request.displayName,
            "category": request.category,
            "version": "1.0.0",
            "versionSeq": 1,
            "visibility": "public",
            "capabilities": [request.transport, "sync"],
            
            "owner": {
                "org": "User Registered",
                "contact": "<EMAIL>",
                "userId": "current_user"
            },
            
            "runtime": {
                "transport": request.transport,
                "endpoint": request.endpoint,
                "httpMethod": request.httpMethod,
                "auth": {"type": "none"},
                "fault_tolerance": "medium"
            },
            
            "metadata": {
                "has_variables_dependencies": False,
                "user_facing": True
            },
            
            "descriptionUser": request.description or f"用户注册的{request.displayName}工具",
            "descriptionDev": request.description or f"User registered tool: {request.displayName}",
            
            "inputsDeveloperSchema": request.inputSchema or {
                "type": "object",
                "properties": {}
            },
            
            "outputsSchema": {
                "type": "object",
                "properties": {
                    "result": {"type": "string"}
                }
            },
            
            "examples": [{
                "userQuery": f"使用{request.displayName}",
                "parsedInputs": {}
            }],
            
            "quickstarts": [{
                "kind": "curl" if request.transport == "http" else "python",
                "title": "示例调用",
                "content": f"# {request.displayName} 调用示例\n# TODO: 添加具体调用代码"
            }],
            
            "content": f"{request.displayName} {request.category} {request.description or ''}",
            "createdBy": "current_user",
            "createdTime": datetime.now().isoformat(),
            "updatedBy": "current_user",
            "updatedTime": datetime.now().isoformat(),
            "deleteFlag": 0
        }
        
        # 保存到 Elasticsearch (模拟)
        es_client = get_es_client()
        if es_client:
            await es_client.index(
                index=settings.es_index,
                id=tool_id,
                body=tool_doc
            )
        
        return {
            "success": True,
            "toolId": tool_id,
            "message": f"工具 '{request.displayName}' 注册成功",
            "quickstart": tool_doc["quickstarts"][0]["content"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

@router.post("/register-from-curl")
async def register_from_curl(request: CurlRegistrationRequest):
    """从 cURL 命令智能注册工具"""
    try:
        # 使用 LLM 分析 cURL 命令
        llm_service = await get_llm_service()
        llm_result = await llm_service.infer_schema_from_curl(request.curl)
        
        # 基础 cURL 解析作为备选
        curl_parts = request.curl.split()
        url = None
        method = "GET"
        headers = {}
        
        for i, part in enumerate(curl_parts):
            if part.startswith("http"):
                url = part.strip("'\"")
            elif part == "-X" and i + 1 < len(curl_parts):
                method = curl_parts[i + 1]
            elif part == "-H" and i + 1 < len(curl_parts):
                header = curl_parts[i + 1].strip("'\"")
                if ":" in header:
                    key, value = header.split(":", 1)
                    headers[key.strip()] = value.strip()
        
        if not url:
            raise HTTPException(status_code=400, detail="无法从 cURL 命令中提取 URL")
        
        # 生成工具 ID
        tool_id = request.displayName.lower().replace(" ", ".").replace("-", ".")
        
        # 使用 LLM 推断的 schema 或默认 schema
        if llm_result["success"] and llm_result["schemas"].get("inputSchema"):
            input_schema = llm_result["schemas"]["inputSchema"]
            extracted_description = llm_result["schemas"].get("description", request.description)
        else:
            # 回退到简单推断
            input_schema = {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "查询参数"
                    }
                }
            }
            extracted_description = request.description
            
            # 如果是 POST 请求，添加 body 参数
            if method.upper() == "POST":
                input_schema["properties"]["data"] = {
                    "type": "object",
                    "description": "请求体数据"
                }
        
        tool_doc = {
            "toolId": tool_id,
            "displayName": request.displayName,
            "category": "api",
            "version": "1.0.0",
            "versionSeq": 1,
            "visibility": "public",
            "capabilities": ["http", "sync"],
            
            "owner": {
                "org": "Auto Generated",
                "contact": "<EMAIL>",
                "userId": "system"
            },
            
            "runtime": {
                "transport": "http",
                "endpoint": url,
                "httpMethod": method,
                "auth": {"type": "none"},
                "fault_tolerance": "medium"
            },
            
            "metadata": {
                "has_variables_dependencies": False,
                "user_facing": True
            },
            
            "descriptionUser": extracted_description or f"从 cURL 自动生成的 {request.displayName} 工具",
            "descriptionDev": f"Auto-generated from cURL: {request.curl[:100]}...",
            
            "inputsDeveloperSchema": input_schema,
            
            "outputsSchema": {
                "type": "object",
                "properties": {
                    "data": {"type": "object"},
                    "status": {"type": "integer"}
                }
            },
            
            "examples": [{
                "userQuery": f"调用 {request.displayName}",
                "parsedInputs": {"query": "示例查询"}
            }],
            
            "quickstarts": [{
                "kind": "curl",
                "title": "原始 cURL 命令",
                "content": request.curl
            }],
            
            "content": f"{request.displayName} API 自动生成 cURL {url}",
            "createdBy": "system",
            "createdTime": datetime.now().isoformat(),
            "updatedBy": "system",
            "updatedTime": datetime.now().isoformat(),
            "deleteFlag": 0
        }
        
        # 保存到 Elasticsearch (模拟)
        es_client = get_es_client()
        if es_client:
            await es_client.index(
                index=settings.es_index,
                id=tool_id,
                body=tool_doc
            )
        
        return {
            "success": True,
            "toolId": tool_id,
            "message": f"从 cURL 成功生成工具 '{request.displayName}'",
            "extracted": {
                "url": url,
                "method": method,
                "headers": headers
            },
            "inputSchema": input_schema,
            "quickstart": request.curl
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"cURL 解析失败: {str(e)}")

@router.post("/register-python")
async def register_python_tool(request: PythonRegistrationRequest):
    """注册 Python 脚本工具"""
    try:
        import base64
        
        # 编码脚本内容
        script_b64 = base64.b64encode(request.script_content.encode()).decode()
        
        # 生成工具 ID
        tool_id = request.displayName.lower().replace(" ", ".").replace("-", ".")
        
        # 简单的函数签名分析 (模拟)
        input_schema = {
            "type": "object",
            "properties": {
                "input": {
                    "type": "string",
                    "description": "输入参数"
                }
            }
        }
        
        # 如果脚本中包含参数提示，尝试解析
        if "def " + request.entry_function in request.script_content:
            # 这里可以添加更复杂的 AST 解析逻辑
            pass
        
        tool_doc = {
            "toolId": tool_id,
            "displayName": request.displayName,
            "category": "script",
            "version": "1.0.0",
            "versionSeq": 1,
            "visibility": "public",
            "capabilities": ["python", "sync", "local"],
            
            "owner": {
                "org": "User Script",
                "contact": "<EMAIL>",
                "userId": "current_user"
            },
            
            "runtime": {
                "transport": "python",
                "script_content": script_b64,
                "entry_function": request.entry_function,
                "auth": {"type": "none"},
                "fault_tolerance": "medium"
            },
            
            "metadata": {
                "has_variables_dependencies": False,
                "user_facing": True
            },
            
            "descriptionUser": request.description or f"Python 脚本工具: {request.displayName}",
            "descriptionDev": f"Python script tool with entry function: {request.entry_function}",
            
            "inputsDeveloperSchema": input_schema,
            
            "outputsSchema": {
                "type": "object",
                "properties": {
                    "result": {"type": "object"},
                    "success": {"type": "boolean"}
                }
            },
            
            "examples": [{
                "userQuery": f"运行 {request.displayName}",
                "parsedInputs": {"input": "示例输入"}
            }],
            
            "quickstarts": [{
                "kind": "python",
                "title": "Python 调用示例",
                "content": f"result = {request.entry_function}(input='示例参数')\nprint(result)"
            }],
            
            "content": f"{request.displayName} Python 脚本 {request.entry_function}",
            "createdBy": "current_user",
            "createdTime": datetime.now().isoformat(),
            "updatedBy": "current_user",
            "updatedTime": datetime.now().isoformat(),
            "deleteFlag": 0
        }
        
        # 保存到 Elasticsearch (模拟)
        es_client = get_es_client()
        if es_client:
            await es_client.index(
                index=settings.es_index,
                id=tool_id,
                body=tool_doc
            )
        
        return {
            "success": True,
            "toolId": tool_id,
            "message": f"Python 工具 '{request.displayName}' 注册成功",
            "entryFunction": request.entry_function,
            "inputSchema": input_schema
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Python 工具注册失败: {str(e)}")

@router.post("/import-mcp")
async def import_mcp_server(request: MCPImportRequest):
    """导入 MCP 服务器工具"""
    try:
        # 模拟 MCP 服务器连接和工具发现
        mock_mcp_tools = [
            {
                "name": "read_file",
                "description": "Read file contents",
                "inputSchema": {
                    "type": "object",
                    "required": ["path"],
                    "properties": {
                        "path": {"type": "string", "description": "File path to read"}
                    }
                }
            },
            {
                "name": "write_file", 
                "description": "Write content to file",
                "inputSchema": {
                    "type": "object",
                    "required": ["path", "content"],
                    "properties": {
                        "path": {"type": "string", "description": "File path to write"},
                        "content": {"type": "string", "description": "Content to write"}
                    }
                }
            }
        ]
        
        imported_tools = []
        
        for mcp_tool in mock_mcp_tools:
            tool_id = f"mcp.{mcp_tool['name']}"
            
            tool_doc = {
                "toolId": tool_id,
                "displayName": f"MCP: {mcp_tool['name']}",
                "category": "mcp",
                "version": "1.0.0",
                "versionSeq": 1,
                "visibility": "public",
                "capabilities": ["mcp", request.transport],
                
                "owner": {
                    "org": "MCP Import",
                    "contact": "<EMAIL>",
                    "userId": "system"
                },
                
                "runtime": {
                    "transport": request.transport,
                    "command": request.command,
                    "args": request.args,
                    "url": request.url,
                    "env": request.env,
                    "auth": {"type": "none"},
                    "fault_tolerance": "high"
                },
                
                "metadata": {
                    "has_variables_dependencies": False,
                    "user_facing": True
                },
                
                "descriptionUser": mcp_tool["description"],
                "descriptionDev": f"MCP tool: {mcp_tool['description']}",
                
                "inputsDeveloperSchema": mcp_tool["inputSchema"],
                
                "outputsSchema": {
                    "type": "object",
                    "properties": {
                        "content": {"type": "string"},
                        "success": {"type": "boolean"}
                    }
                },
                
                "examples": [{
                    "userQuery": f"使用 {mcp_tool['name']}",
                    "parsedInputs": {}
                }],
                
                "quickstarts": [{
                    "kind": "mcp",
                    "title": "MCP 调用示例",
                    "content": json.dumps({
                        "method": "tools/call",
                        "params": {
                            "name": mcp_tool["name"],
                            "arguments": {}
                        }
                    }, indent=2)
                }],
                
                "content": f"MCP {mcp_tool['name']} {mcp_tool['description']}",
                "createdBy": "system",
                "createdTime": datetime.now().isoformat(),
                "updatedBy": "system",
                "updatedTime": datetime.now().isoformat(),
                "deleteFlag": 0
            }
            
            # 保存到 Elasticsearch (模拟)
            es_client = get_es_client()
            if es_client:
                await es_client.index(
                    index=settings.es_index,
                    id=tool_id,
                    body=tool_doc
                )
            
            imported_tools.append({
                "toolId": tool_id,
                "name": mcp_tool["name"],
                "description": mcp_tool["description"]
            })
        
        return {
            "success": True,
            "message": f"成功导入 {len(imported_tools)} 个 MCP 工具",
            "transport": request.transport,
            "importedTools": imported_tools
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"MCP 导入失败: {str(e)}")

@router.post("/generate-from-description")
async def generate_tool_from_description(request: NaturalLanguageToolRequest):
    """从自然语言描述生成工具 - 零代码用户支持"""
    try:
        # 使用 LLM 生成工具定义
        llm_service = await get_llm_service()
        
        # 准备测试数据
        examples = {}
        if request.test_inputs:
            examples["inputs"] = request.test_inputs
        if request.test_outputs:
            examples["outputs"] = request.test_outputs
        
        # 生成工具定义
        llm_result = await llm_service.generate_tool_from_description(
            request.description, 
            examples if examples else None
        )
        
        if not llm_result["success"]:
            raise HTTPException(status_code=500, detail=f"工具生成失败: {llm_result.get('error', '未知错误')}")
        
        tool_definition = llm_result["tool_definition"]
        
        # 如果 LLM 生成失败，使用默认模板
        if not tool_definition:
            tool_definition = {
                "toolId": "generated.tool",
                "displayName": "生成的工具",
                "category": request.category or "utility",
                "transport": "python",
                "runtime": {
                    "transport": "python",
                    "entry_function": "main"
                },
                "inputsDeveloperSchema": {
                    "type": "object",
                    "properties": {
                        "input": {"type": "string", "description": "输入参数"}
                    }
                },
                "outputsSchema": {
                    "type": "object",
                    "properties": {
                        "result": {"type": "string", "description": "处理结果"}
                    }
                },
                "descriptionUser": request.description,
                "examples": []
            }
        
        # 生成使用示例
        if tool_definition.get("inputsDeveloperSchema"):
            examples_list = await llm_service.generate_examples(
                tool_definition.get("displayName", "工具"),
                tool_definition["inputsDeveloperSchema"]
            )
            if examples_list:
                tool_definition["examples"] = examples_list
        
        # 构建完整的工具文档
        tool_doc = {
            "toolId": tool_definition.get("toolId", "generated.tool"),
            "displayName": tool_definition.get("displayName", "生成的工具"),
            "category": tool_definition.get("category", request.category or "utility"),
            "version": "1.0.0",
            "versionSeq": 1,
            "visibility": "public",
            "capabilities": ["generated", "llm"],
            
            "owner": {
                "org": "AI Generated",
                "contact": "<EMAIL>",
                "userId": "system"
            },
            
            "runtime": tool_definition.get("runtime", {
                "transport": "python",
                "entry_function": "main"
            }),
            
            "metadata": {
                "has_variables_dependencies": False,
                "user_facing": True
            },
            
            "descriptionUser": tool_definition.get("descriptionUser", request.description),
            "descriptionDev": f"AI generated tool from description: {request.description[:100]}...",
            
            "inputsDeveloperSchema": tool_definition.get("inputsDeveloperSchema", {}),
            "outputsSchema": tool_definition.get("outputsSchema", {}),
            
            "examples": tool_definition.get("examples", []),
            
            "quickstarts": [{
                "kind": "description",
                "title": "工具描述",
                "content": request.description
            }],
            
            "content": f"{tool_definition.get('displayName', '')} {request.description} AI生成",
            "createdBy": "ai_system",
            "createdTime": datetime.now().isoformat(),
            "updatedBy": "ai_system",
            "updatedTime": datetime.now().isoformat(),
            "deleteFlag": 0
        }
        
        # 保存到 Elasticsearch (可选)
        tool_id = tool_doc["toolId"]
        es_client = get_es_client()
        if es_client:
            await es_client.index(
                index=settings.es_index,
                id=tool_id,
                body=tool_doc
            )
        
        return {
            "success": True,
            "toolId": tool_id,
            "message": f"成功生成工具 '{tool_doc['displayName']}'",
            "tool_definition": tool_doc,
            "llm_analysis": {
                "original_description": request.description,
                "generated_features": list(tool_definition.keys()) if tool_definition else [],
                "confidence": "high" if tool_definition else "low"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"工具生成失败: {str(e)}")

@router.post("/smart-complete")
async def smart_complete_tool(tool_info: Dict[str, Any]):
    """智能补全工具元数据"""
    try:
        llm_service = await get_llm_service()
        
        # 使用 LLM 补全元数据
        result = await llm_service.complete_tool_metadata(tool_info)
        
        if result["success"]:
            return {
                "success": True,
                "completions": result["completions"],
                "message": "元数据补全成功"
            }
        else:
            # 如果 LLM 失败，提供基础补全
            basic_completions = {
                "category": "utility",
                "descriptionUser": f"工具: {tool_info.get('displayName', '未知工具')}",
                "descriptionDev": f"Tool: {tool_info.get('displayName', 'Unknown tool')}",
                "capabilities": ["sync"],
                "aliases": []
            }
            
            return {
                "success": True,
                "completions": basic_completions,
                "message": "使用基础补全（LLM不可用）",
                "llm_error": result.get("error")
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"智能补全失败: {str(e)}")

@router.get("/{tool_id}")
async def get_tool(tool_id: str):
    """获取工具详情"""
    try:
        # 首先尝试从 mock 数据获取
        mock_tool = await get_mock_tool_by_id(tool_id)
        if mock_tool:
            return {
                "success": True,
                "tool": mock_tool
            }
        
        # 使用仓库获取工具
        es_client = get_es_client()
        redis_client = get_redis_client()
        if es_client:
            repository = get_tool_repository(es_client, redis_client)
            tool = await repository.get_by_id(tool_id)
            
            if tool:
                return {
                    "success": True,
                    "tool": tool
                }
        
        raise HTTPException(status_code=404, detail=f"工具 {tool_id} 不存在")
        
    except Exception as e:
        if "not_found" in str(e).lower():
            raise HTTPException(status_code=404, detail=f"工具 {tool_id} 不存在")
        raise HTTPException(status_code=500, detail=f"获取工具失败: {str(e)}")

@router.get("/")
async def list_tools(
    category: Optional[str] = None,
    transport: Optional[str] = None,
    visibility: Optional[str] = None,
    limit: int = 20,
    offset: int = 0
):
    """获取工具列表"""
    try:
        es_client = get_es_client()
        redis_client = get_redis_client()
        
        if not es_client:
            # 如果 ES 不可用，返回 mock 数据
            from mock_data import MOCK_TOOLS
            filtered_tools = MOCK_TOOLS
            
            # 应用筛选条件
            if category:
                filtered_tools = [t for t in filtered_tools if t.get("category") == category]
            if transport:
                filtered_tools = [t for t in filtered_tools if transport in t.get("capabilities", [])]
            if visibility:
                filtered_tools = [t for t in filtered_tools if t.get("visibility") == visibility]
            
            # 分页
            paginated_tools = filtered_tools[offset:offset + limit]
            
            return {
                "success": True,
                "tools": paginated_tools,
                "total": len(filtered_tools),
                "limit": limit,
                "offset": offset
            }
        
        # 使用仓库进行搜索
        repository = get_tool_repository(es_client, redis_client)
        result = await repository.search_tools(
            query="",  # 空查询获取所有
            category=category,
            transport=transport,
            visibility=visibility,
            size=limit,
            from_=offset,
            use_cache=True
        )
        
        return {
            "success": True,
            "tools": result["hits"],
            "total": result["total"],
            "limit": limit,
            "offset": offset,
            "took": result["took"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")

@router.delete("/{tool_id}")
async def delete_tool(tool_id: str):
    """删除工具"""
    try:
        es_client = get_es_client()
        if es_client:
            await es_client.delete(
                index=settings.es_index,
                id=tool_id
            )
        
        return {
            "success": True,
            "message": f"工具 {tool_id} 已删除"
        }
        
    except Exception as e:
        if "not_found" in str(e).lower():
            raise HTTPException(status_code=404, detail=f"工具 {tool_id} 不存在")
        raise HTTPException(status_code=500, detail=f"删除工具失败: {str(e)}")

@router.post("/register/curl")
async def register_curl_tool(request: CurlRegistrationRequest):
    """通过 cURL 命令注册工具"""
    try:
        # 导入 cURL 解析器
        from parsers.curl_parser import parse_curl
        
        # 解析 cURL 命令
        curl_info = parse_curl(request.curl)
        
        # 获取工具仓库
        repository = await get_tool_repository()
        
        # 生成工具ID
        tool_id = f"http.{request.displayName.lower().replace(' ', '_')}"
        
        # 构建工具定义
        tool_definition = {
            "toolId": tool_id,
            "displayName": request.displayName,
            "category": "api",
            "transport": "http",
            "runtime": {
                "transport": "http",
                "endpoint": curl_info["url"],
                "method": curl_info["method"],
                "headers": curl_info.get("headers", {}),
                "curl_command": request.curl
            },
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {
                    "params": {"type": "object"},
                    "data": {"type": "object"}
                }
            },
            "outputsSchema": {"type": "object", "properties": {"result": {"type": "string"}}},
            "descriptionUser": request.description or f"基于 cURL 的 API 工具: {request.displayName}",
            "descriptionDev": request.description or f"解析自 cURL 命令的 HTTP API 工具",
            "capabilities": ["http_request"],
            "aliases": [],
            "examples": [
                {
                    "name": "基础调用",
                    "description": "使用解析的参数调用 API",
                    "input": {"params": {}, "data": {}},
                    "curl_command": request.curl
                }
            ],
            "createdTime": datetime.utcnow().isoformat(),
            "updatedTime": datetime.utcnow().isoformat()
        }
        
        # 保存工具
        await repository.create(tool_definition)
        
        return {
            "success": True,
            "tool_id": tool_id,
            "message": "cURL 工具注册成功",
            "tool_definition": tool_definition,
            "parsed_curl": curl_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"cURL 工具注册失败: {str(e)}")

@router.post("/register/python")
async def register_python_tool(request: PythonRegistrationRequest):
    """注册 Python 脚本工具"""
    try:
        # 导入 Python 解析器
        from parsers.python_parser import parse_python_function
        
        # 解析 Python 函数
        function_info = parse_python_function(request.script_content, request.entry_function)
        
        # 获取工具仓库
        repository = await get_tool_repository()
        
        # 生成工具ID
        tool_id = f"python.{request.displayName.lower().replace(' ', '_')}"
        
        # 构建输入 schema
        input_schema = {"type": "object", "properties": {}}
        if function_info.get("parameters"):
            for param in function_info["parameters"]:
                input_schema["properties"][param["name"]] = {
                    "type": param.get("type", "string"),
                    "description": param.get("description", "")
                }
                if param.get("required", False):
                    input_schema.setdefault("required", []).append(param["name"])
        
        # 构建工具定义
        tool_definition = {
            "toolId": tool_id,
            "displayName": request.displayName,
            "category": "script",
            "transport": "python",
            "runtime": {
                "transport": "python",
                "code": request.script_content,
                "entry_function": request.entry_function
            },
            "inputsDeveloperSchema": input_schema,
            "outputsSchema": {
                "type": "object",
                "properties": {
                    "result": {"type": "string"},
                    "return_value": {"type": "object"}
                }
            },
            "descriptionUser": request.description or f"Python 脚本工具: {request.displayName}",
            "descriptionDev": request.description or f"执行 Python 函数 {request.entry_function}",
            "capabilities": ["python_execution"],
            "aliases": [],
            "examples": [
                {
                    "name": "函数调用",
                    "description": f"调用 {request.entry_function} 函数",
                    "input": {param["name"]: f"示例{param['name']}" for param in function_info.get("parameters", [])},
                    "function_signature": function_info.get("signature", "")
                }
            ],
            "createdTime": datetime.utcnow().isoformat(),
            "updatedTime": datetime.utcnow().isoformat()
        }
        
        # 保存工具
        await repository.create(tool_definition)
        
        return {
            "success": True,
            "tool_id": tool_id,
            "message": "Python 工具注册成功",
            "tool_definition": tool_definition,
            "function_info": function_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Python 工具注册失败: {str(e)}")

@router.post("/register/mcp")
async def register_mcp_tools(request: MCPImportRequest):
    """导入 MCP 服务器的工具"""
    try:
        # 导入 MCP 沙盒服务
        from services.mcp_sandbox_service import get_mcp_sandbox_service, MCPSandboxRequest
        
        # 获取 MCP 沙盒服务
        sandbox = await get_mcp_sandbox_service()
        
        # 构建 MCP 请求
        if request.transport == "http" and request.url:
            mcp_request = MCPSandboxRequest(
                transport="http",
                url=request.url
            )
        elif request.transport == "stdio" and request.command:
            mcp_request = MCPSandboxRequest(
                transport="stdio",
                command=request.command,
                args=request.args or [],
                env=request.env or {}
            )
        else:
            raise HTTPException(status_code=400, detail="无效的 MCP 配置")
        
        # 验证 MCP 服务器并获取工具列表
        validation_result = await sandbox.validate_mcp_server(mcp_request)
        
        if not validation_result.success:
            raise HTTPException(status_code=400, detail=f"MCP 服务器验证失败: {validation_result.error}")
        
        # 获取工具仓库
        repository = await get_tool_repository()
        
        # 注册每个工具
        registered_tools = []
        for tool in validation_result.available_tools:
            tool_id = f"mcp.{tool.name}"
            
            # 构建工具定义
            tool_definition = {
                "toolId": tool_id,
                "displayName": tool.name,
                "category": "mcp",
                "transport": "stdio" if request.transport == "stdio" else "http",
                "runtime": {
                    "transport": request.transport,
                    "mcp_tool_name": tool.name
                },
                "inputsDeveloperSchema": tool.inputSchema or {"type": "object", "properties": {}},
                "outputsSchema": {"type": "object", "properties": {"result": {"type": "string"}}},
                "descriptionUser": tool.description or f"MCP 工具: {tool.name}",
                "descriptionDev": tool.description or f"来自 MCP 服务器的工具",
                "capabilities": ["mcp_tool"],
                "aliases": [],
                "examples": [],
                "createdTime": datetime.utcnow().isoformat(),
                "updatedTime": datetime.utcnow().isoformat()
            }
            
            # 添加 MCP 特定配置
            if request.transport == "stdio":
                tool_definition["runtime"]["command"] = request.command
                tool_definition["runtime"]["args"] = request.args or []
                tool_definition["runtime"]["env"] = request.env or {}
            else:
                tool_definition["runtime"]["url"] = request.url
            
            # 保存工具
            await repository.create(tool_definition)
            registered_tools.append(tool_definition)
        
        return {
            "success": True,
            "message": f"成功注册 {len(registered_tools)} 个 MCP 工具",
            "registered_tools": registered_tools,
            "server_info": validation_result.server_info.__dict__ if validation_result.server_info else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"MCP 工具注册失败: {str(e)}")

@router.post("/register/natural")
async def register_natural_language_tool(request: NaturalLanguageToolRequest):
    """通过自然语言描述注册工具"""
    try:
        # 导入智能补全服务
        from services.intelligent_completion_service import get_intelligent_completion_service, CompletionRequest
        
        # 获取智能补全服务
        completion_service = await get_intelligent_completion_service()
        
        # 构建补全请求
        completion_request = CompletionRequest(
            description=request.description,
            test_inputs=request.test_inputs,
            test_outputs=request.test_outputs,
            category=request.category
        )
        
        # 执行智能补全
        completion_result = await completion_service.complete_tool_definition(completion_request)
        
        if not completion_result.success:
            raise HTTPException(status_code=400, detail=f"工具定义生成失败: {completion_result.error}")
        
        # 获取工具仓库
        repository = await get_tool_repository()
        
        # 添加时间戳
        tool_definition = completion_result.tool_definition
        tool_definition["createdTime"] = datetime.utcnow().isoformat()
        tool_definition["updatedTime"] = datetime.utcnow().isoformat()
        
        # 保存工具
        await repository.create(tool_definition)
        
        return {
            "success": True,
            "tool_id": tool_definition["toolId"],
            "message": "自然语言工具注册成功",
            "tool_definition": tool_definition,
            "suggestions": completion_result.suggestions,
            "confidence": completion_result.confidence
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"自然语言工具注册失败: {str(e)}")@
router.get("/")
async def list_tools(
    skip: int = 0,
    limit: int = 20,
    category: Optional[str] = None,
    transport: Optional[str] = None,
    search: Optional[str] = None
):
    """获取工具列表"""
    try:
        repository = await get_tool_repository()
        
        # 构建过滤条件
        filters = {}
        if category:
            filters["category"] = category
        if transport:
            filters["transport"] = transport
        
        # 执行搜索
        if search:
            tools = await repository.search(search, filters=filters, skip=skip, limit=limit)
        else:
            tools = await repository.list_all(filters=filters, skip=skip, limit=limit)
        
        return {
            "tools": tools,
            "total": len(tools),
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")

@router.get("/{tool_id}")
async def get_tool(tool_id: str):
    """获取单个工具详情"""
    try:
        repository = await get_tool_repository()
        tool = await repository.get_by_id(tool_id)
        
        if not tool:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        return tool
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具失败: {str(e)}")

@router.put("/{tool_id}")
async def update_tool(tool_id: str, updates: Dict[str, Any]):
    """更新工具"""
    try:
        repository = await get_tool_repository()
        
        # 检查工具是否存在
        existing_tool = await repository.get_by_id(tool_id)
        if not existing_tool:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 添加更新时间
        updates["updatedTime"] = datetime.utcnow().isoformat()
        
        # 更新工具
        updated_tool = await repository.update(tool_id, updates)
        
        return {
            "success": True,
            "message": "工具更新成功",
            "tool": updated_tool
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新工具失败: {str(e)}")

@router.delete("/{tool_id}")
async def delete_tool(tool_id: str):
    """删除工具"""
    try:
        repository = await get_tool_repository()
        
        # 检查工具是否存在
        existing_tool = await repository.get_by_id(tool_id)
        if not existing_tool:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 删除工具
        success = await repository.delete(tool_id)
        
        if not success:
            raise HTTPException(status_code=500, detail="删除工具失败")
        
        return {
            "success": True,
            "message": "工具删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除工具失败: {str(e)}")

@router.get("/categories/list")
async def get_categories():
    """获取所有工具分类"""
    try:
        repository = await get_tool_repository()
        categories = await repository.get_categories()
        
        return {
            "categories": categories
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.get("/transports/list")
async def get_transports():
    """获取所有传输方式"""
    return {
        "transports": [
            {
                "type": "http",
                "name": "HTTP API",
                "description": "基于 HTTP 协议的 API 调用"
            },
            {
                "type": "python",
                "name": "Python 脚本",
                "description": "Python 函数执行"
            },
            {
                "type": "stdio",
                "name": "MCP stdio",
                "description": "基于标准输入输出的 MCP 协议"
            }
        ]
    }@router.p
ost("/modify", response_model=SmartRegistrationResponse)
async def modify_tool(request: ToolModificationRequest):
    """修改工具定义并重新验证"""
    try:
        repository = await get_tool_repository()
        
        # 获取现有工具
        existing_tool = await repository.get_by_id(request.tool_id)
        if not existing_tool:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 应用修改
        modified_tool = existing_tool.copy()
        for key, value in request.modifications.items():
            if key in ["toolId", "createdTime"]:  # 保护字段
                continue
            modified_tool[key] = value
        
        # 更新时间戳
        modified_tool["updatedTime"] = datetime.utcnow().isoformat()
        
        # 重新生成示例
        if request.regenerate_examples:
            examples = await _generate_tool_examples(modified_tool)
            modified_tool["examples"] = examples
        
        # 重新验证
        validation_result = None
        validation_warnings = []
        if request.revalidate:
            smart_request = SmartRegistrationRequest(
                display_name=modified_tool.get("displayName"),
                description=modified_tool.get("descriptionUser"),
                auto_validate=True
            )
            validation_result, validation_warnings = await _validate_smart_tool(modified_tool, smart_request)
        
        # 保存修改
        updated_tool = await repository.update(request.tool_id, modified_tool)
        
        return SmartRegistrationResponse(
            success=True,
            tool_id=request.tool_id,
            message="工具修改成功",
            tool_definition=updated_tool,
            validation_result=validation_result,
            validation_warnings=validation_warnings,
            improvement_suggestions=await _generate_improvement_suggestions(updated_tool)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        return SmartRegistrationResponse(
            success=False,
            message=f"工具修改失败: {str(e)}",
            validation_warnings=[str(e)]
        )

@router.get("/suggest-improvements/{tool_id}")
async def suggest_tool_improvements(tool_id: str):
    """为现有工具提供改进建议"""
    try:
        repository = await get_tool_repository()
        tool = await repository.get_by_id(tool_id)
        
        if not tool:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 生成改进建议
        suggestions = await _generate_improvement_suggestions(tool)
        alternatives = await _generate_alternative_schemas(tool)
        
        # 分析工具质量
        quality_score = _calculate_tool_quality_score(tool)
        
        return {
            "tool_id": tool_id,
            "quality_score": quality_score,
            "improvement_suggestions": suggestions,
            "alternative_schemas": alternatives,
            "analysis": {
                "has_description": bool(tool.get("descriptionUser")),
                "has_examples": bool(tool.get("examples")),
                "has_aliases": bool(tool.get("aliases")),
                "schema_completeness": _analyze_schema_completeness(tool.get("inputsDeveloperSchema", {}))
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

def _calculate_tool_quality_score(tool: Dict[str, Any]) -> float:
    """计算工具质量分数"""
    score = 0.0
    max_score = 10.0
    
    # 描述质量 (2分)
    description = tool.get("descriptionUser", "")
    if description:
        if len(description) > 50:
            score += 2.0
        elif len(description) > 20:
            score += 1.0
        else:
            score += 0.5
    
    # 输入 schema 完整性 (3分)
    input_schema = tool.get("inputsDeveloperSchema", {})
    if input_schema.get("properties"):
        prop_count = len(input_schema["properties"])
        if prop_count >= 3:
            score += 3.0
        elif prop_count >= 2:
            score += 2.0
        else:
            score += 1.0
    
    # 示例数量 (2分)
    examples = tool.get("examples", [])
    if len(examples) >= 2:
        score += 2.0
    elif len(examples) >= 1:
        score += 1.0
    
    # 别名和可发现性 (1分)
    if tool.get("aliases"):
        score += 1.0
    
    # 分类准确性 (1分)
    if tool.get("category") and tool["category"] != "utility":
        score += 1.0
    
    # 运行时配置完整性 (1分)
    runtime = tool.get("runtime", {})
    if runtime.get("endpoint") or runtime.get("code") or runtime.get("command"):
        score += 1.0
    
    return round(score / max_score * 100, 1)

def _analyze_schema_completeness(schema: Dict[str, Any]) -> Dict[str, Any]:
    """分析 schema 完整性"""
    analysis = {
        "has_properties": bool(schema.get("properties")),
        "property_count": len(schema.get("properties", {})),
        "has_required": bool(schema.get("required")),
        "has_descriptions": False,
        "has_types": False
    }
    
    properties = schema.get("properties", {})
    if properties:
        # 检查是否有描述
        analysis["has_descriptions"] = any(
            prop.get("description") for prop in properties.values()
        )
        
        # 检查是否有类型
        analysis["has_types"] = any(
            prop.get("type") for prop in properties.values()
        )
    
    return analysis

@router.post("/import-mcp", response_model=MCPImportResult)
async def import_mcp_server_advanced(request: MCPImportAdvancedRequest):
    """高级 MCP 服务器工具导入"""
    import uuid
    import time
    
    # 生成导入 ID
    import_id = f"mcp_import_{uuid.uuid4().hex[:8]}"
    start_time = datetime.utcnow()
    
    # 初始化进度跟踪
    progress = MCPImportProgress(
        import_id=import_id,
        status="pending",
        progress=0.0,
        current_step="初始化导入",
        total_tools=0,
        imported_tools=0,
        failed_tools=0,
        start_time=start_time.isoformat()
    )
    
    try:
        print(f"开始 MCP 导入: {import_id}")
        
        # 如果是异步导入，立即返回进度信息
        if request.async_import:
            # 这里应该启动后台任务
            # 暂时返回同步结果
            pass
        
        # 1. 连接 MCP 服务器
        progress.status = "connecting"
        progress.current_step = "连接 MCP 服务器"
        progress.progress = 0.1
        
        connection_start = time.time()
        server_info, sandbox = await _connect_mcp_server(request)
        connection_time_ms = int((time.time() - connection_start) * 1000)
        
        # 2. 发现工具
        progress.status = "discovering"
        progress.current_step = "发现可用工具"
        progress.progress = 0.3
        
        discovered_tools = await _discover_mcp_tools(sandbox, request)
        progress.total_tools = len(discovered_tools)
        
        # 3. 过滤工具
        tools_to_import = _filter_tools_for_import(discovered_tools, request)
        
        # 4. 导入工具
        progress.status = "importing"
        progress.current_step = "导入工具"
        progress.progress = 0.5
        
        imported_tools = []
        failed_tools = []
        
        repository = await get_tool_repository()
        
        for i, tool_info in enumerate(tools_to_import):
            try:
                # 更新进度
                progress.progress = 0.5 + (i / len(tools_to_import)) * 0.4
                progress.current_step = f"导入工具: {tool_info['name']}"
                
                # 创建工具定义
                tool_definition = await _create_mcp_tool_definition(tool_info, request, server_info)
                
                # 验证工具（如果启用）
                if request.validate_tools:
                    validation_result = await _validate_mcp_tool(tool_definition, sandbox, request)
                    if not validation_result.get("success"):
                        progress.warnings.append(f"工具 {tool_info['name']} 验证失败: {validation_result.get('error')}")
                
                # 生成示例（如果启用）
                if request.generate_examples:
                    examples = await _generate_mcp_tool_examples(tool_definition, tool_info)
                    tool_definition["examples"] = examples
                
                # 优化 Schema（如果启用）
                if request.optimize_schemas:
                    tool_definition = await _optimize_mcp_tool_schema(tool_definition)
                
                # 保存工具
                await repository.create(tool_definition)
                
                imported_tools.append({
                    "tool_id": tool_definition["toolId"],
                    "name": tool_info["name"],
                    "description": tool_info.get("description", ""),
                    "status": "imported"
                })
                
                progress.imported_tools += 1
                
            except Exception as e:
                print(f"导入工具 {tool_info['name']} 失败: {e}")
                failed_tools.append({
                    "name": tool_info["name"],
                    "error": str(e),
                    "status": "failed"
                })
                progress.failed_tools += 1
                progress.errors.append(f"工具 {tool_info['name']}: {str(e)}")
        
        # 5. 完成导入
        progress.status = "completed"
        progress.current_step = "导入完成"
        progress.progress = 1.0
        progress.end_time = datetime.utcnow().isoformat()
        
        # 生成建议
        suggestions = _generate_mcp_import_suggestions(imported_tools, failed_tools, request)
        
        result = MCPImportResult(
            success=len(imported_tools) > 0,
            import_id=import_id,
            message=f"MCP 导入完成: 成功 {len(imported_tools)} 个，失败 {len(failed_tools)} 个",
            server_info=server_info,
            connection_time_ms=connection_time_ms,
            discovered_tools=discovered_tools,
            imported_tools=imported_tools,
            failed_tools=failed_tools,
            total_discovered=len(discovered_tools),
            total_imported=len(imported_tools),
            total_failed=len(failed_tools),
            progress=progress,
            suggestions=suggestions,
            warnings=progress.warnings
        )
        
        print(f"MCP 导入完成: {import_id}, 成功: {len(imported_tools)}, 失败: {len(failed_tools)}")
        return result
        
    except Exception as e:
        progress.status = "failed"
        progress.current_step = f"导入失败: {str(e)}"
        progress.errors.append(str(e))
        progress.end_time = datetime.utcnow().isoformat()
        
        print(f"MCP 导入失败: {import_id}, 错误: {e}")
        return MCPImportResult(
            success=False,
            import_id=import_id,
            message=f"MCP 导入失败: {str(e)}",
            progress=progress,
            warnings=[str(e)]
        )

async def _connect_mcp_server(request: MCPImportAdvancedRequest) -> tuple[Dict[str, Any], Any]:
    """连接 MCP 服务器"""
    from services.mcp_sandbox_service import get_mcp_sandbox_service, MCPSandboxRequest
    
    sandbox = await get_mcp_sandbox_service()
    
    # 构建 MCP 请求
    if request.transport == "http" and request.url:
        mcp_request = MCPSandboxRequest(
            transport="http",
            url=request.url
        )
    elif request.transport == "stdio" and request.command:
        mcp_request = MCPSandboxRequest(
            transport="stdio",
            command=request.command,
            args=request.args or [],
            env=request.env or {}
        )
    else:
        raise ValueError("无效的 MCP 配置")
    
    # 验证连接
    validation_result = await sandbox.validate_mcp_server(mcp_request)
    
    if not validation_result.success:
        raise Exception(f"MCP 服务器连接失败: {validation_result.error}")
    
    server_info = {
        "transport": request.transport,
        "connection_successful": True,
        "server_name": validation_result.server_info.name if validation_result.server_info else "Unknown",
        "server_version": validation_result.server_info.version if validation_result.server_info else "Unknown"
    }
    
    if request.transport == "stdio":
        server_info.update({
            "command": request.command,
            "args": request.args
        })
    else:
        server_info["url"] = request.url
    
    return server_info, sandbox

async def _discover_mcp_tools(sandbox: Any, request: MCPImportAdvancedRequest) -> List[Dict[str, Any]]:
    """发现 MCP 工具"""
    # 构建请求
    if request.transport == "http":
        mcp_request_data = {"transport": "http", "url": request.url}
    else:
        mcp_request_data = {
            "transport": "stdio",
            "command": request.command,
            "args": request.args or [],
            "env": request.env or {}
        }
    
    from services.mcp_sandbox_service import MCPSandboxRequest
    mcp_request = MCPSandboxRequest(**mcp_request_data)
    
    # 获取工具列表
    validation_result = await sandbox.validate_mcp_server(mcp_request)
    
    discovered_tools = []
    for tool in validation_result.available_tools:
        discovered_tools.append({
            "name": tool.name,
            "description": tool.description or f"MCP 工具: {tool.name}",
            "input_schema": tool.inputSchema or {"type": "object", "properties": {}},
            "mcp_tool_info": tool
        })
    
    return discovered_tools

def _filter_tools_for_import(discovered_tools: List[Dict[str, Any]], request: MCPImportAdvancedRequest) -> List[Dict[str, Any]]:
    """过滤要导入的工具"""
    if not request.import_all and request.selected_tools:
        return [tool for tool in discovered_tools if tool["name"] in request.selected_tools]
    
    return discovered_tools

async def _create_mcp_tool_definition(tool_info: Dict[str, Any], request: MCPImportAdvancedRequest, server_info: Dict[str, Any]) -> Dict[str, Any]:
    """创建 MCP 工具定义"""
    import uuid
    
    # 生成工具 ID
    prefix = request.tool_prefix or "mcp"
    tool_id = f"{prefix}.{tool_info['name']}.{uuid.uuid4().hex[:8]}"
    
    # 基础工具定义
    tool_definition = {
        "toolId": tool_id,
        "displayName": f"MCP: {tool_info['name']}",
        "category": request.category_override or "mcp",
        "transport": "stdio" if request.transport == "stdio" else "http",
        "runtime": {
            "transport": request.transport,
            "mcp_tool_name": tool_info["name"]
        },
        "inputsDeveloperSchema": tool_info["input_schema"],
        "outputsSchema": {
            "type": "object",
            "properties": {
                "result": {"type": "string", "description": "工具执行结果"},
                "content": {"type": "array", "items": {"type": "object"}, "description": "结构化内容"}
            }
        },
        "descriptionUser": _format_description(tool_info["description"], request.description_template),
        "descriptionDev": f"MCP 工具: {tool_info['description']}",
        "capabilities": ["mcp_tool"],
        "aliases": [tool_info["name"]],
        "examples": [],
        "createdTime": datetime.utcnow().isoformat(),
        "updatedTime": datetime.utcnow().isoformat()
    }
    
    # 添加 MCP 特定配置
    if request.transport == "stdio":
        tool_definition["runtime"].update({
            "command": request.command,
            "args": request.args or [],
            "env": request.env or {}
        })
    else:
        tool_definition["runtime"]["url"] = request.url
    
    return tool_definition

def _format_description(description: str, template: Optional[str]) -> str:
    """格式化工具描述"""
    if template:
        return template.format(description=description)
    return description

async def _validate_mcp_tool(tool_definition: Dict[str, Any], sandbox: Any, request: MCPImportAdvancedRequest) -> Dict[str, Any]:
    """验证 MCP 工具"""
    try:
        # 构建测试请求
        from services.mcp_sandbox_service import MCPSandboxRequest
        
        if request.transport == "http":
            mcp_request = MCPSandboxRequest(transport="http", url=request.url)
        else:
            mcp_request = MCPSandboxRequest(
                transport="stdio",
                command=request.command,
                args=request.args or [],
                env=request.env or {}
            )
        
        # 尝试调用工具
        tool_name = tool_definition["runtime"]["mcp_tool_name"]
        test_result = await sandbox.test_mcp_tool(mcp_request, tool_name, {})
        
        return {
            "success": True,
            "test_result": test_result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

async def _generate_mcp_tool_examples(tool_definition: Dict[str, Any], tool_info: Dict[str, Any]) -> List[Dict[str, Any]]:
    """生成 MCP 工具示例"""
    examples = []
    
    # 基础示例
    input_schema = tool_definition.get("inputsDeveloperSchema", {})
    if input_schema.get("properties"):
        example_input = {}
        for prop_name, prop_def in input_schema["properties"].items():
            if prop_def.get("type") == "string":
                example_input[prop_name] = f"示例{prop_name}"
            elif prop_def.get("type") == "integer":
                example_input[prop_name] = 1
            elif prop_def.get("type") == "boolean":
                example_input[prop_name] = True
            else:
                example_input[prop_name] = "示例值"
        
        examples.append({
            "name": f"使用 {tool_info['name']}",
            "description": f"调用 MCP 工具 {tool_info['name']} 的基础示例",
            "input": example_input,
            "mcp_call": {
                "method": "tools/call",
                "params": {
                    "name": tool_info["name"],
                    "arguments": example_input
                }
            }
        })
    
    return examples

async def _optimize_mcp_tool_schema(tool_definition: Dict[str, Any]) -> Dict[str, Any]:
    """优化 MCP 工具 Schema"""
    # 这里可以添加 Schema 优化逻辑
    # 例如：添加更详细的描述、优化类型定义等
    
    input_schema = tool_definition.get("inputsDeveloperSchema", {})
    if input_schema.get("properties"):
        for prop_name, prop_def in input_schema["properties"].items():
            # 如果没有描述，添加默认描述
            if not prop_def.get("description"):
                prop_def["description"] = f"{prop_name} 参数"
    
    return tool_definition

def _generate_mcp_import_suggestions(imported_tools: List[Dict[str, Any]], failed_tools: List[Dict[str, Any]], request: MCPImportAdvancedRequest) -> List[str]:
    """生成 MCP 导入建议"""
    suggestions = []
    
    if len(failed_tools) > 0:
        suggestions.append(f"有 {len(failed_tools)} 个工具导入失败，建议检查工具配置")
    
    if len(imported_tools) > 10:
        suggestions.append("导入了大量工具，建议为工具添加更具体的分类")
    
    if not request.validate_tools:
        suggestions.append("建议启用工具验证以确保工具可用性")
    
    if not request.generate_examples:
        suggestions.append("建议生成使用示例以提高工具可用性")
    
    return suggestions

@router.get("/import-mcp/{import_id}/progress")
async def get_mcp_import_progress(import_id: str):
    """获取 MCP 导入进度"""
    # 这里应该从缓存或数据库中获取进度信息
    # 暂时返回模拟数据
    return {
        "import_id": import_id,
        "status": "completed",
        "progress": 1.0,
        "message": "导入已完成"
    }

@router.post("/mcp/discover")
async def discover_mcp_tools(request: MCPImportAdvancedRequest):
    """发现 MCP 工具（不导入）"""
    try:
        server_info, sandbox = await _connect_mcp_server(request)
        discovered_tools = await _discover_mcp_tools(sandbox, request)
        
        return {
            "success": True,
            "server_info": server_info,
            "discovered_tools": discovered_tools,
            "total_tools": len(discovered_tools)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"工具发现失败: {str(e)}")

@router.post("/generate-from-description", response_model=GenerationResult)
async def generate_tool_from_natural_language(request: NaturalLanguageGenerationRequest):
    """从自然语言描述生成工具定义"""
    import uuid
    import time
    
    generation_id = f"gen_{uuid.uuid4().hex[:8]}"
    start_time = time.time()
    
    try:
        print(f"开始自然语言工具生成: {generation_id}")
        print(f"描述: {request.description[:100]}...")
        
        # 1. 分析用户描述
        analysis_result = await _analyze_user_description(request)
        
        # 2. 生成工具定义
        tool_definition = await _generate_tool_definition_from_nl(request, analysis_result)
        
        # 3. 优化和完善工具定义
        tool_definition = await _enhance_generated_tool(tool_definition, request)
        
        # 4. 生成替代方案
        alternatives = await _generate_alternative_tools(request, analysis_result)
        
        # 5. 验证生成的工具
        validation_result = None
        if request.auto_validate:
            validation_result = await _validate_generated_tool(tool_definition, request)
        
        # 6. 生成改进建议
        suggestions = await _generate_tool_suggestions(tool_definition, analysis_result)
        
        generation_time_ms = int((time.time() - start_time) * 1000)
        
        result = GenerationResult(
            success=True,
            generation_id=generation_id,
            message="工具生成成功",
            tool_definition=tool_definition,
            analysis=analysis_result,
            confidence_score=analysis_result.get("confidence", 0.8),
            reasoning=analysis_result.get("reasoning", ""),
            alternatives=alternatives,
            validation_result=validation_result,
            suggestions=suggestions,
            generation_time_ms=generation_time_ms,
            tokens_used=analysis_result.get("tokens_used", 0)
        )
        
        print(f"工具生成完成: {generation_id}, 耗时: {generation_time_ms}ms")
        return result
        
    except Exception as e:
        generation_time_ms = int((time.time() - start_time) * 1000)
        print(f"工具生成失败: {generation_id}, 错误: {e}")
        
        return GenerationResult(
            success=False,
            generation_id=generation_id,
            message=f"工具生成失败: {str(e)}",
            generation_time_ms=generation_time_ms,
            warnings=[str(e)]
        )

async def _analyze_user_description(request: NaturalLanguageGenerationRequest) -> Dict[str, Any]:
    """分析用户描述"""
    try:
        from services.llm_service import get_llm_service
        
        llm_service = await get_llm_service()
        
        # 构建分析 prompt
        analysis_prompt = f"""
        分析以下工具描述，提取关键信息：
        
        描述: {request.description}
        使用场景: {request.use_cases or '未指定'}
        目标用户: {request.target_users or '未指定'}
        应用领域: {request.domain or '未指定'}
        
        请分析并返回以下信息：
        1. 工具的核心功能
        2. 推荐的传输方式 (http, python, stdio)
        3. 输入参数类型和结构
        4. 输出数据类型和结构
        5. 适合的工具分类
        6. 复杂度评估
        7. 置信度评分 (0-1)
        8. 分析推理过程
        """
        
        # 调用 LLM 分析
        analysis_response = await llm_service.analyze_text(analysis_prompt)
        
        # 解析分析结果
        analysis_result = {
            "core_function": _extract_core_function(request.description),
            "recommended_transport": _infer_transport_type(request),
            "input_structure": _infer_input_structure(request),
            "output_structure": _infer_output_structure(request),
            "category": _infer_category(request),
            "complexity": request.complexity_level,
            "confidence": 0.85,  # 基础置信度
            "reasoning": f"基于描述'{request.description[:50]}...'的分析",
            "tokens_used": len(analysis_prompt.split()) * 2  # 估算
        }
        
        # 如果有 LLM 响应，更新结果
        if analysis_response and analysis_response.get("success"):
            analysis_result.update({
                "llm_analysis": analysis_response.get("content", ""),
                "confidence": min(analysis_result["confidence"] + 0.1, 1.0)
            })
        
        return analysis_result
        
    except Exception as e:
        print(f"描述分析失败: {e}")
        # 返回基础分析结果
        return {
            "core_function": request.description,
            "recommended_transport": "http",
            "input_structure": {"type": "object", "properties": {}},
            "output_structure": {"type": "object", "properties": {"result": {"type": "string"}}},
            "category": "utility",
            "complexity": request.complexity_level,
            "confidence": 0.6,
            "reasoning": "基础分析（LLM 分析失败）",
            "tokens_used": 0
        }

def _extract_core_function(description: str) -> str:
    """提取核心功能"""
    # 简单的关键词提取
    keywords = ["生成", "创建", "获取", "查询", "计算", "转换", "分析", "处理", "发送", "接收"]
    for keyword in keywords:
        if keyword in description:
            return f"{keyword}相关功能"
    return "通用工具功能"

def _infer_transport_type(request: NaturalLanguageGenerationRequest) -> str:
    """推断传输方式"""
    if request.preferred_transport:
        return request.preferred_transport
    
    description = request.description.lower()
    
    # API 相关关键词
    api_keywords = ["api", "接口", "请求", "http", "rest", "网络", "服务器"]
    if any(keyword in description for keyword in api_keywords):
        return "http"
    
    # 脚本相关关键词
    script_keywords = ["计算", "处理", "算法", "函数", "脚本", "python"]
    if any(keyword in description for keyword in script_keywords):
        return "python"
    
    # 默认返回 http
    return "http"

def _infer_input_structure(request: NaturalLanguageGenerationRequest) -> Dict[str, Any]:
    """推断输入结构"""
    if request.example_inputs:
        return _infer_schema_from_data(request.example_inputs, "input")
    
    # 基于描述推断
    description = request.description.lower()
    properties = {}
    
    # 常见参数模式
    if "查询" in description or "搜索" in description:
        properties["query"] = {"type": "string", "description": "查询关键词"}
    
    if "文件" in description:
        properties["file_path"] = {"type": "string", "description": "文件路径"}
    
    if "用户" in description:
        properties["user_id"] = {"type": "string", "description": "用户ID"}
    
    if "数量" in description or "个数" in description:
        properties["count"] = {"type": "integer", "description": "数量"}
    
    if "时间" in description or "日期" in description:
        properties["date"] = {"type": "string", "format": "date", "description": "日期"}
    
    # 如果没有推断出参数，添加通用参数
    if not properties:
        properties["input"] = {"type": "string", "description": "输入参数"}
    
    return {
        "type": "object",
        "properties": properties,
        "required": list(properties.keys())[:1]  # 第一个参数为必需
    }

def _infer_output_structure(request: NaturalLanguageGenerationRequest) -> Dict[str, Any]:
    """推断输出结构"""
    if request.example_outputs:
        return _infer_schema_from_data(request.example_outputs, "output")
    
    # 基于描述推断
    description = request.description.lower()
    properties = {"result": {"type": "string", "description": "执行结果"}}
    
    if "列表" in description or "多个" in description:
        properties["items"] = {"type": "array", "description": "结果列表"}
    
    if "数量" in description or "统计" in description:
        properties["count"] = {"type": "integer", "description": "统计数量"}
    
    if "状态" in description:
        properties["status"] = {"type": "string", "description": "执行状态"}
    
    if "时间" in description:
        properties["timestamp"] = {"type": "string", "description": "时间戳"}
    
    return {
        "type": "object",
        "properties": properties
    }

def _infer_category(request: NaturalLanguageGenerationRequest) -> str:
    """推断工具分类"""
    if request.category_hint:
        return request.category_hint
    
    description = request.description.lower()
    domain = (request.domain or "").lower()
    
    # 分类映射
    category_keywords = {
        "api": ["api", "接口", "网络", "服务"],
        "data": ["数据", "数据库", "存储", "查询"],
        "file": ["文件", "目录", "存储", "读取", "写入"],
        "text": ["文本", "字符串", "内容", "文档"],
        "image": ["图片", "图像", "照片", "视觉"],
        "security": ["安全", "加密", "密码", "认证"],
        "communication": ["通信", "邮件", "消息", "通知"],
        "utility": ["工具", "实用", "辅助", "帮助"],
        "analytics": ["分析", "统计", "报告", "监控"]
    }
    
    for category, keywords in category_keywords.items():
        if any(keyword in description or keyword in domain for keyword in keywords):
            return category
    
    return "utility"

async def _generate_tool_definition_from_nl(request: NaturalLanguageGenerationRequest, analysis: Dict[str, Any]) -> Dict[str, Any]:
    """从自然语言生成工具定义"""
    import uuid
    
    # 生成工具 ID
    tool_name = request.tool_name_hint or _generate_tool_name(request.description)
    tool_id = f"{analysis['recommended_transport']}.{tool_name.lower().replace(' ', '_')}.{uuid.uuid4().hex[:8]}"
    
    # 基础工具定义
    tool_definition = {
        "toolId": tool_id,
        "displayName": tool_name,
        "category": analysis["category"],
        "transport": analysis["recommended_transport"],
        "runtime": {
            "transport": analysis["recommended_transport"]
        },
        "inputsDeveloperSchema": analysis["input_structure"],
        "outputsSchema": analysis["output_structure"],
        "descriptionUser": request.description,
        "descriptionDev": f"AI 生成的工具: {request.description}",
        "capabilities": [analysis["recommended_transport"]],
        "aliases": [tool_name.lower().replace(" ", "_")],
        "examples": [],
        "createdTime": datetime.utcnow().isoformat(),
        "updatedTime": datetime.utcnow().isoformat()
    }
    
    # 根据传输方式添加特定配置
    if analysis["recommended_transport"] == "http":
        tool_definition["runtime"]["endpoint"] = "https://api.example.com/generated-tool"
        tool_definition["runtime"]["method"] = "POST"
    elif analysis["recommended_transport"] == "python":
        tool_definition["runtime"]["code"] = _generate_python_code(request, analysis)
        tool_definition["runtime"]["entry_function"] = "main"
    
    return tool_definition

def _generate_tool_name(description: str) -> str:
    """生成工具名称"""
    # 提取关键词生成名称
    words = description.split()[:3]  # 取前3个词
    name_words = []
    
    for word in words:
        # 过滤停用词
        if word.lower() not in ["的", "一个", "可以", "用于", "进行", "实现"]:
            name_words.append(word)
    
    if not name_words:
        return "AI生成工具"
    
    return " ".join(name_words[:2])  # 最多2个词

def _generate_python_code(request: NaturalLanguageGenerationRequest, analysis: Dict[str, Any]) -> str:
    """生成 Python 代码"""
    function_name = "main"
    
    # 基础代码模板
    code_template = f'''
def {function_name}(**kwargs):
    """
    {request.description}
    
    Args:
        **kwargs: 输入参数
    
    Returns:
        dict: 执行结果
    """
    try:
        # TODO: 实现具体逻辑
        result = "工具执行成功"
        
        return {{
            "success": True,
            "result": result,
            "message": "执行完成"
        }}
    except Exception as e:
        return {{
            "success": False,
            "error": str(e),
            "message": "执行失败"
        }}
'''
    
    return code_template.strip()

async def _enhance_generated_tool(tool_definition: Dict[str, Any], request: NaturalLanguageGenerationRequest) -> Dict[str, Any]:
    """增强生成的工具定义"""
    
    # 添加验证规则
    if request.include_validation:
        input_schema = tool_definition.get("inputsDeveloperSchema", {})
        if input_schema.get("properties"):
            for prop_name, prop_def in input_schema["properties"].items():
                if prop_def.get("type") == "string":
                    prop_def["minLength"] = 1
                elif prop_def.get("type") == "integer":
                    prop_def["minimum"] = 0
    
    # 生成使用示例
    if request.generate_examples:
        examples = await _generate_usage_examples(tool_definition, request)
        tool_definition["examples"] = examples
    
    # 添加用户场景信息
    if request.use_cases:
        tool_definition["use_cases"] = request.use_cases
    
    if request.target_users:
        tool_definition["target_users"] = request.target_users
    
    return tool_definition

async def _generate_usage_examples(tool_definition: Dict[str, Any], request: NaturalLanguageGenerationRequest) -> List[Dict[str, Any]]:
    """生成使用示例"""
    examples = []
    
    # 基于输入 schema 生成示例
    input_schema = tool_definition.get("inputsDeveloperSchema", {})
    if input_schema.get("properties"):
        example_input = {}
        for prop_name, prop_def in input_schema["properties"].items():
            if prop_def.get("type") == "string":
                example_input[prop_name] = f"示例{prop_name}"
            elif prop_def.get("type") == "integer":
                example_input[prop_name] = 123
            elif prop_def.get("type") == "boolean":
                example_input[prop_name] = True
            else:
                example_input[prop_name] = "示例值"
        
        examples.append({
            "name": "基础使用示例",
            "description": f"使用 {tool_definition.get('displayName')} 的基础示例",
            "input": example_input,
            "expected_output": {"result": "示例输出", "success": True}
        })
    
    # 如果用户提供了示例场景，添加更多示例
    if request.example_scenarios:
        for i, scenario in enumerate(request.example_scenarios[:2]):  # 最多2个
            examples.append({
                "name": f"场景示例 {i+1}",
                "description": scenario.get("description", f"场景 {i+1}"),
                "input": scenario.get("input", {}),
                "expected_output": scenario.get("output", {"result": "场景输出"})
            })
    
    return examples

async def _generate_alternative_tools(request: NaturalLanguageGenerationRequest, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """生成替代工具方案"""
    alternatives = []
    
    # 不同传输方式的替代方案
    transport_alternatives = ["http", "python", "stdio"]
    current_transport = analysis["recommended_transport"]
    
    for transport in transport_alternatives:
        if transport != current_transport:
            alt_analysis = analysis.copy()
            alt_analysis["recommended_transport"] = transport
            
            alt_tool = await _generate_tool_definition_from_nl(request, alt_analysis)
            alternatives.append({
                "name": f"{transport.upper()} 实现方案",
                "description": f"使用 {transport} 传输方式的实现",
                "tool_definition": alt_tool,
                "pros": _get_transport_pros(transport),
                "cons": _get_transport_cons(transport)
            })
    
    return alternatives[:2]  # 最多返回2个替代方案

def _get_transport_pros(transport: str) -> List[str]:
    """获取传输方式优点"""
    pros_map = {
        "http": ["易于集成", "标准化接口", "支持远程调用"],
        "python": ["执行效率高", "逻辑复杂度支持好", "本地执行"],
        "stdio": ["轻量级", "跨语言支持", "简单部署"]
    }
    return pros_map.get(transport, [])

def _get_transport_cons(transport: str) -> List[str]:
    """获取传输方式缺点"""
    cons_map = {
        "http": ["需要网络连接", "可能有延迟", "依赖外部服务"],
        "python": ["需要 Python 环境", "安全性需要考虑", "不支持远程调用"],
        "stdio": ["调试相对困难", "错误处理复杂", "功能相对简单"]
    }
    return cons_map.get(transport, [])

async def _validate_generated_tool(tool_definition: Dict[str, Any], request: NaturalLanguageGenerationRequest) -> Dict[str, Any]:
    """验证生成的工具"""
    validation_result = {
        "success": True,
        "checks": [],
        "warnings": [],
        "errors": []
    }
    
    # 基础验证
    required_fields = ["toolId", "displayName", "transport", "inputsDeveloperSchema", "outputsSchema"]
    for field in required_fields:
        if field not in tool_definition:
            validation_result["errors"].append(f"缺少必需字段: {field}")
            validation_result["success"] = False
        else:
            validation_result["checks"].append(f"✅ {field} 字段存在")
    
    # Schema 验证
    input_schema = tool_definition.get("inputsDeveloperSchema", {})
    if not input_schema.get("properties"):
        validation_result["warnings"].append("输入 Schema 为空，建议添加参数定义")
    else:
        validation_result["checks"].append(f"✅ 输入 Schema 包含 {len(input_schema['properties'])} 个参数")
    
    # 传输方式特定验证
    transport = tool_definition.get("transport")
    if transport == "http":
        runtime = tool_definition.get("runtime", {})
        if not runtime.get("endpoint"):
            validation_result["warnings"].append("HTTP 工具缺少端点配置")
    elif transport == "python":
        runtime = tool_definition.get("runtime", {})
        if not runtime.get("code"):
            validation_result["warnings"].append("Python 工具缺少代码实现")
    
    # 描述质量检查
    description = tool_definition.get("descriptionUser", "")
    if len(description) < 10:
        validation_result["warnings"].append("工具描述过于简短，建议添加更详细的说明")
    else:
        validation_result["checks"].append("✅ 工具描述长度适中")
    
    return validation_result

async def _generate_tool_suggestions(tool_definition: Dict[str, Any], analysis: Dict[str, Any]) -> List[str]:
    """生成工具改进建议"""
    suggestions = []
    
    # 基于置信度的建议
    confidence = analysis.get("confidence", 0.5)
    if confidence < 0.7:
        suggestions.append("AI 生成置信度较低，建议人工审核和调整")
    
    # 基于复杂度的建议
    complexity = analysis.get("complexity", "medium")
    if complexity == "complex":
        suggestions.append("工具复杂度较高，建议分解为多个简单工具")
    
    # 基于传输方式的建议
    transport = tool_definition.get("transport")
    if transport == "http":
        suggestions.append("建议添加 API 认证和错误处理机制")
    elif transport == "python":
        suggestions.append("建议添加输入参数验证和异常处理")
    
    # 基于 Schema 的建议
    input_schema = tool_definition.get("inputsDeveloperSchema", {})
    if len(input_schema.get("properties", {})) > 5:
        suggestions.append("输入参数较多，建议考虑参数分组或简化")
    
    # 通用建议
    if not tool_definition.get("examples"):
        suggestions.append("建议添加使用示例以提高工具可用性")
    
    return suggestions

@router.post("/preview-tool", response_model=ToolPreviewResponse)
async def preview_generated_tool(request: ToolPreviewRequest):
    """预览生成的工具"""
    try:
        tool_def = request.tool_definition
        
        # 基础预览数据
        preview_data = {
            "tool_id": tool_def.get("toolId"),
            "display_name": tool_def.get("displayName"),
            "category": tool_def.get("category"),
            "transport": tool_def.get("transport"),
            "description": tool_def.get("descriptionUser"),
            "input_parameters": _extract_input_parameters(tool_def),
            "output_structure": tool_def.get("outputsSchema", {}),
            "capabilities": tool_def.get("capabilities", [])
        }
        
        # 生成 Schema 文档
        schema_docs = None
        if request.include_schema_docs:
            schema_docs = _generate_schema_documentation(tool_def)
        
        # 提取使用示例
        usage_examples = []
        if request.include_examples:
            usage_examples = tool_def.get("examples", [])
        
        # 生成交互式演示
        interactive_demo = None
        if request.preview_type == "interactive":
            interactive_demo = _generate_interactive_demo(tool_def)
        
        return ToolPreviewResponse(
            success=True,
            preview_data=preview_data,
            interactive_demo=interactive_demo,
            schema_documentation=schema_docs,
            usage_examples=usage_examples
        )
        
    except Exception as e:
        return ToolPreviewResponse(
            success=False,
            preview_data={},
            warnings=[f"预览生成失败: {str(e)}"]
        )

def _extract_input_parameters(tool_def: Dict[str, Any]) -> List[Dict[str, Any]]:
    """提取输入参数信息"""
    parameters = []
    input_schema = tool_def.get("inputsDeveloperSchema", {})
    properties = input_schema.get("properties", {})
    required = input_schema.get("required", [])
    
    for param_name, param_def in properties.items():
        parameters.append({
            "name": param_name,
            "type": param_def.get("type", "string"),
            "description": param_def.get("description", ""),
            "required": param_name in required,
            "default": param_def.get("default"),
            "format": param_def.get("format"),
            "enum": param_def.get("enum")
        })
    
    return parameters

def _generate_schema_documentation(tool_def: Dict[str, Any]) -> str:
    """生成 Schema 文档"""
    docs = []
    docs.append(f"# {tool_def.get('displayName', '工具')} API 文档\n")
    docs.append(f"**描述**: {tool_def.get('descriptionUser', '')}\n")
    docs.append(f"**传输方式**: {tool_def.get('transport', '')}\n")
    docs.append(f"**分类**: {tool_def.get('category', '')}\n\n")
    
    # 输入参数文档
    docs.append("## 输入参数\n")
    parameters = _extract_input_parameters(tool_def)
    if parameters:
        for param in parameters:
            required_mark = " **(必需)**" if param["required"] else " (可选)"
            docs.append(f"- **{param['name']}** ({param['type']}){required_mark}: {param['description']}")
    else:
        docs.append("无输入参数")
    
    docs.append("\n## 输出结构\n")
    output_schema = tool_def.get("outputsSchema", {})
    if output_schema.get("properties"):
        for prop_name, prop_def in output_schema["properties"].items():
            docs.append(f"- **{prop_name}** ({prop_def.get('type', 'unknown')}): {prop_def.get('description', '')}")
    else:
        docs.append("输出结构未定义")
    
    return "\n".join(docs)

def _generate_interactive_demo(tool_def: Dict[str, Any]) -> Dict[str, Any]:
    """生成交互式演示"""
    parameters = _extract_input_parameters(tool_def)
    
    # 生成表单配置
    form_config = []
    for param in parameters:
        field_config = {
            "name": param["name"],
            "label": param["description"] or param["name"],
            "type": _map_type_to_form_field(param["type"]),
            "required": param["required"],
            "placeholder": f"请输入{param['name']}"
        }
        
        if param.get("enum"):
            field_config["type"] = "select"
            field_config["options"] = param["enum"]
        
        form_config.append(field_config)
    
    return {
        "form_config": form_config,
        "api_endpoint": f"/api/tools/{tool_def.get('toolId')}/execute",
        "method": "POST",
        "sample_request": _generate_sample_request(tool_def),
        "sample_response": _generate_sample_response(tool_def)
    }

def _map_type_to_form_field(param_type: str) -> str:
    """映射参数类型到表单字段类型"""
    type_mapping = {
        "string": "text",
        "integer": "number",
        "number": "number",
        "boolean": "checkbox",
        "array": "textarea",
        "object": "textarea"
    }
    return type_mapping.get(param_type, "text")

def _generate_sample_request(tool_def: Dict[str, Any]) -> Dict[str, Any]:
    """生成示例请求"""
    input_schema = tool_def.get("inputsDeveloperSchema", {})
    properties = input_schema.get("properties", {})
    
    sample_request = {}
    for param_name, param_def in properties.items():
        param_type = param_def.get("type", "string")
        if param_type == "string":
            sample_request[param_name] = f"示例{param_name}"
        elif param_type == "integer":
            sample_request[param_name] = 123
        elif param_type == "boolean":
            sample_request[param_name] = True
        elif param_type == "array":
            sample_request[param_name] = ["示例1", "示例2"]
        else:
            sample_request[param_name] = "示例值"
    
    return sample_request

def _generate_sample_response(tool_def: Dict[str, Any]) -> Dict[str, Any]:
    """生成示例响应"""
    output_schema = tool_def.get("outputsSchema", {})
    properties = output_schema.get("properties", {})
    
    sample_response = {}
    for prop_name, prop_def in properties.items():
        prop_type = prop_def.get("type", "string")
        if prop_type == "string":
            sample_response[prop_name] = f"示例{prop_name}"
        elif prop_type == "integer":
            sample_response[prop_name] = 456
        elif prop_type == "boolean":
            sample_response[prop_name] = True
        elif prop_type == "array":
            sample_response[prop_name] = ["结果1", "结果2"]
        else:
            sample_response[prop_name] = "示例结果"
    
    return sample_response

@router.post("/drafts", response_model=ToolDraftResponse)
async def create_tool_draft(request: ToolDraftRequest):
    """创建工具草稿"""
    import uuid
    from datetime import timedelta
    
    try:
        # 生成草稿 ID
        draft_id = f"draft_{uuid.uuid4().hex[:12]}"
        
        # 计算过期时间
        expiry_time = datetime.utcnow() + timedelta(hours=request.expiry_hours)
        
        # 构建草稿数据
        draft_data = {
            "draft_id": draft_id,
            "draft_name": request.draft_name or f"工具草稿 {datetime.now().strftime('%m-%d %H:%M')}",
            "description": request.description or "自动生成的工具草稿",
            "tool_definition": request.tool_definition,
            "tags": request.tags,
            "created_time": datetime.utcnow().isoformat(),
            "updated_time": datetime.utcnow().isoformat(),
            "expiry_time": expiry_time.isoformat(),
            "status": "draft",
            "version": 1,
            "auto_save": request.auto_save
        }
        
        # 这里应该保存到数据库或缓存
        # 暂时模拟保存成功
        await _save_draft_to_storage(draft_id, draft_data)
        
        # 生成编辑 URL
        edit_url = f"/tools/drafts/{draft_id}/edit"
        
        return ToolDraftResponse(
            success=True,
            draft_id=draft_id,
            message="工具草稿创建成功",
            draft_data=draft_data,
            expiry_time=expiry_time.isoformat(),
            edit_url=edit_url
        )
        
    except Exception as e:
        return ToolDraftResponse(
            success=False,
            draft_id="",
            message=f"草稿创建失败: {str(e)}"
        )

@router.get("/drafts/{draft_id}")
async def get_tool_draft(draft_id: str):
    """获取工具草稿"""
    try:
        draft_data = await _get_draft_from_storage(draft_id)
        
        if not draft_data:
            raise HTTPException(status_code=404, detail="草稿不存在或已过期")
        
        # 检查是否过期
        expiry_time = datetime.fromisoformat(draft_data["expiry_time"])
        if datetime.utcnow() > expiry_time:
            await _delete_draft_from_storage(draft_id)
            raise HTTPException(status_code=410, detail="草稿已过期")
        
        return {
            "success": True,
            "draft_data": draft_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取草稿失败: {str(e)}")

@router.put("/drafts/{draft_id}", response_model=ToolEditResponse)
async def edit_tool_draft(draft_id: str, request: ToolEditRequest):
    """编辑工具草稿"""
    try:
        # 获取现有草稿
        draft_data = await _get_draft_from_storage(draft_id)
        if not draft_data:
            raise HTTPException(status_code=404, detail="草稿不存在")
        
        # 创建备份
        backup_id = None
        if request.create_backup:
            backup_id = await _create_draft_backup(draft_id, draft_data)
        
        # 应用修改
        updated_definition = draft_data["tool_definition"].copy()
        for key, value in request.modifications.items():
            if key not in ["draft_id", "created_time"]:  # 保护字段
                updated_definition[key] = value
        
        # 更新时间戳
        updated_definition["updatedTime"] = datetime.utcnow().isoformat()
        
        # 验证修改
        validation_result = None
        if request.validation_level != "none":
            validation_result = await _validate_tool_definition(
                updated_definition, 
                request.validation_level
            )
        
        # 更新草稿数据
        draft_data["tool_definition"] = updated_definition
        draft_data["updated_time"] = datetime.utcnow().isoformat()
        draft_data["version"] += 1
        
        # 保存更新
        if request.auto_save:
            await _save_draft_to_storage(draft_id, draft_data)
        
        return ToolEditResponse(
            success=True,
            message="工具编辑成功",
            updated_definition=updated_definition,
            validation_result=validation_result,
            backup_id=backup_id,
            warnings=validation_result.get("warnings", []) if validation_result else [],
            errors=validation_result.get("errors", []) if validation_result else []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        return ToolEditResponse(
            success=False,
            message=f"编辑失败: {str(e)}",
            errors=[str(e)]
        )

@router.post("/drafts/{draft_id}/publish", response_model=ToolPublishResponse)
async def publish_tool_draft(draft_id: str, request: ToolPublishRequest):
    """发布工具草稿"""
    try:
        # 获取草稿数据
        draft_data = await _get_draft_from_storage(draft_id)
        if not draft_data:
            raise HTTPException(status_code=404, detail="草稿不存在")
        
        tool_definition = draft_data["tool_definition"]
        
        # 最终验证
        validation_result = None
        if request.final_validation:
            validation_result = await _validate_tool_definition(tool_definition, "full")
            
            if validation_result and not validation_result.get("success", True):
                return ToolPublishResponse(
                    success=False,
                    message="工具验证失败，无法发布",
                    validation_result=validation_result,
                    warnings=validation_result.get("warnings", [])
                )
        
        # 应用发布选项
        if request.publish_options:
            for key, value in request.publish_options.items():
                if key in ["visibility", "category", "tags"]:
                    tool_definition[key] = value
        
        # 设置发布状态
        tool_definition["status"] = "published"
        tool_definition["publishedTime"] = datetime.utcnow().isoformat()
        
        # 保存到工具仓库
        repository = await get_tool_repository()
        await repository.create(tool_definition)
        
        # 清理草稿
        if request.cleanup_draft:
            await _delete_draft_from_storage(draft_id)
        
        return ToolPublishResponse(
            success=True,
            message="工具发布成功",
            tool_id=tool_definition["toolId"],
            published_tool=tool_definition,
            validation_result=validation_result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        return ToolPublishResponse(
            success=False,
            message=f"发布失败: {str(e)}",
            warnings=[str(e)]
        )

@router.post("/validate", response_model=ValidationResponse)
async def validate_tool_realtime(request: ValidationRequest):
    """实时验证工具定义"""
    try:
        tool_def = request.tool_definition
        validation_type = request.validation_type
        field_name = request.field_name
        
        # 执行验证
        if field_name:
            # 单字段验证
            field_errors, warnings, suggestions = await _validate_single_field(
                tool_def, field_name
            )
            global_errors = []
        else:
            # 全量验证
            validation_result = await _validate_tool_definition(tool_def, validation_type)
            field_errors = validation_result.get("field_errors", {})
            global_errors = validation_result.get("global_errors", [])
            warnings = validation_result.get("warnings", [])
            suggestions = validation_result.get("suggestions", [])
        
        # 计算验证分数
        validation_score = _calculate_validation_score(field_errors, global_errors, warnings)
        
        return ValidationResponse(
            success=len(global_errors) == 0 and not any(field_errors.values()),
            field_errors=field_errors,
            global_errors=global_errors,
            warnings=warnings,
            suggestions=suggestions,
            validation_score=validation_score
        )
        
    except Exception as e:
        return ValidationResponse(
            success=False,
            global_errors=[f"验证失败: {str(e)}"]
        )

@router.get("/drafts")
async def list_tool_drafts(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    tags: Optional[str] = Query(None)
):
    """获取工具草稿列表"""
    try:
        # 这里应该从数据库查询
        # 暂时返回模拟数据
        drafts = await _list_drafts_from_storage(skip, limit, status, tags)
        
        return {
            "success": True,
            "drafts": drafts,
            "total": len(drafts),
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取草稿列表失败: {str(e)}")

@router.delete("/drafts/{draft_id}")
async def delete_tool_draft(draft_id: str):
    """删除工具草稿"""
    try:
        success = await _delete_draft_from_storage(draft_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="草稿不存在")
        
        return {
            "success": True,
            "message": "草稿删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除草稿失败: {str(e)}")

# 辅助函数
async def _save_draft_to_storage(draft_id: str, draft_data: Dict[str, Any]) -> bool:
    """保存草稿到存储"""
    # 这里应该保存到 Redis 或数据库
    # 暂时模拟保存成功
    print(f"保存草稿: {draft_id}")
    return True

async def _get_draft_from_storage(draft_id: str) -> Optional[Dict[str, Any]]:
    """从存储获取草稿"""
    # 这里应该从 Redis 或数据库获取
    # 暂时返回模拟数据
    if draft_id.startswith("draft_"):
        return {
            "draft_id": draft_id,
            "draft_name": "示例草稿",
            "tool_definition": {
                "toolId": f"tool.{draft_id}",
                "displayName": "示例工具",
                "category": "utility"
            },
            "created_time": datetime.utcnow().isoformat(),
            "updated_time": datetime.utcnow().isoformat(),
            "expiry_time": (datetime.utcnow() + timedelta(hours=24)).isoformat(),
            "version": 1
        }
    return None

async def _delete_draft_from_storage(draft_id: str) -> bool:
    """从存储删除草稿"""
    # 这里应该从 Redis 或数据库删除
    print(f"删除草稿: {draft_id}")
    return True

async def _create_draft_backup(draft_id: str, draft_data: Dict[str, Any]) -> str:
    """创建草稿备份"""
    import uuid
    backup_id = f"backup_{uuid.uuid4().hex[:8]}"
    
    backup_data = draft_data.copy()
    backup_data["backup_id"] = backup_id
    backup_data["original_draft_id"] = draft_id
    backup_data["backup_time"] = datetime.utcnow().isoformat()
    
    # 保存备份
    await _save_draft_to_storage(backup_id, backup_data)
    
    return backup_id

async def _validate_tool_definition(tool_def: Dict[str, Any], validation_level: str) -> Dict[str, Any]:
    """验证工具定义"""
    field_errors = {}
    global_errors = []
    warnings = []
    suggestions = []
    
    # 基础验证
    required_fields = ["toolId", "displayName", "transport"]
    for field in required_fields:
        if field not in tool_def or not tool_def[field]:
            field_errors.setdefault(field, []).append(f"{field} 是必需字段")
    
    # Schema 验证
    if validation_level in ["full", "schema"]:
        input_schema = tool_def.get("inputsDeveloperSchema", {})
        if not input_schema.get("properties"):
            warnings.append("输入 Schema 为空，建议添加参数定义")
        
        output_schema = tool_def.get("outputsSchema", {})
        if not output_schema.get("properties"):
            warnings.append("输出 Schema 为空，建议定义返回结构")
    
    # 运行时验证
    if validation_level == "full":
        transport = tool_def.get("transport")
        runtime = tool_def.get("runtime", {})
        
        if transport == "http" and not runtime.get("endpoint"):
            field_errors.setdefault("runtime.endpoint", []).append("HTTP 工具需要配置端点")
        elif transport == "python" and not runtime.get("code"):
            field_errors.setdefault("runtime.code", []).append("Python 工具需要提供代码")
    
    # 质量建议
    description = tool_def.get("descriptionUser", "")
    if len(description) < 20:
        suggestions.append("建议添加更详细的工具描述")
    
    if not tool_def.get("examples"):
        suggestions.append("建议添加使用示例")
    
    return {
        "success": len(global_errors) == 0 and not any(field_errors.values()),
        "field_errors": field_errors,
        "global_errors": global_errors,
        "warnings": warnings,
        "suggestions": suggestions
    }

async def _validate_single_field(tool_def: Dict[str, Any], field_name: str) -> tuple[Dict[str, List[str]], List[str], List[str]]:
    """验证单个字段"""
    field_errors = {}
    warnings = []
    suggestions = []
    
    field_value = tool_def.get(field_name)
    
    # 字段特定验证
    if field_name == "toolId":
        if not field_value:
            field_errors[field_name] = ["工具ID不能为空"]
        elif not isinstance(field_value, str) or len(field_value) < 3:
            field_errors[field_name] = ["工具ID长度至少3个字符"]
    
    elif field_name == "displayName":
        if not field_value:
            field_errors[field_name] = ["工具名称不能为空"]
        elif len(field_value) > 100:
            warnings.append("工具名称过长，建议简化")
    
    elif field_name == "descriptionUser":
        if field_value and len(field_value) < 10:
            suggestions.append("建议添加更详细的描述")
    
    elif field_name == "transport":
        valid_transports = ["http", "python", "stdio"]
        if field_value not in valid_transports:
            field_errors[field_name] = [f"传输方式必须是: {', '.join(valid_transports)}"]
    
    return field_errors, warnings, suggestions

def _calculate_validation_score(field_errors: Dict[str, List[str]], global_errors: List[str], warnings: List[str]) -> float:
    """计算验证分数"""
    total_issues = len(global_errors) + sum(len(errors) for errors in field_errors.values()) + len(warnings) * 0.5
    
    if total_issues == 0:
        return 1.0
    elif total_issues <= 2:
        return 0.8
    elif total_issues <= 5:
        return 0.6
    else:
        return 0.4

async def _list_drafts_from_storage(skip: int, limit: int, status: Optional[str], tags: Optional[str]) -> List[Dict[str, Any]]:
    """从存储获取草稿列表"""
    # 模拟草稿数据
    drafts = [
        {
            "draft_id": f"draft_{i:03d}",
            "draft_name": f"工具草稿 {i}",
            "description": f"这是第 {i} 个工具草稿",
            "status": "draft",
            "created_time": datetime.utcnow().isoformat(),
            "updated_time": datetime.utcnow().isoformat(),
            "tags": ["test", "demo"] if i % 2 == 0 else ["utility"],
            "tool_type": "http" if i % 3 == 0 else "python"
        }
        for i in range(1, 11)
    ]
    
    # 应用过滤
    if status:
        drafts = [d for d in drafts if d["status"] == status]
    
    if tags:
        tag_list = tags.split(",")
        drafts = [d for d in drafts if any(tag in d["tags"] for tag in tag_list)]
    
    # 应用分页
    return drafts[skip:skip + limit]

@router.post("/batch-register")
async def batch_register_tools(requests: List[SmartRegistrationRequest]):
    """批量智能注册工具"""
    results = []
    
    for i, request in enumerate(requests):
        try:
            result = await smart_register_tool(request)
            results.append({
                "index": i,
                "success": result.success,
                "tool_id": result.tool_id,
                "message": result.message
            })
        except Exception as e:
            results.append({
                "index": i,
                "success": False,
                "error": str(e)
            })
    
    success_count = sum(1 for r in results if r.get("success"))
    
    return {
        "total": len(requests),
        "success_count": success_count,
        "failure_count": len(requests) - success_count,
        "results": results
    }