"""
MCP 导入端点测试
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

# 测试数据
SAMPLE_MCP_STDIO_REQUEST = {
    "transport": "stdio",
    "command": "uvx",
    "args": ["mcp-server-filesystem"],
    "env": {"ALLOWED_DIRECTORIES": "/tmp"},
    "import_all": True,
    "validate_tools": True,
    "generate_examples": True,
    "optimize_schemas": True,
    "async_import": False
}

SAMPLE_MCP_HTTP_REQUEST = {
    "transport": "http",
    "url": "http://localhost:3000/mcp",
    "import_all": True,
    "selected_tools": ["read_file", "write_file"],
    "tool_prefix": "fs",
    "category_override": "filesystem",
    "description_template": "文件系统工具: {description}",
    "validate_tools": True,
    "generate_examples": True
}

class TestMCPImport:
    """MCP 导入测试类"""
    
    @pytest.fixture
    def mock_repository(self):
        """模拟工具仓库"""
        repository = AsyncMock()
        repository.create = AsyncMock(return_value=True)
        return repository
    
    @pytest.fixture
    def mock_mcp_sandbox(self):
        """模拟 MCP 沙盒"""
        # 模拟工具信息
        mock_tool1 = MagicMock()
        mock_tool1.name = "read_file"
        mock_tool1.description = "读取文件内容"
        mock_tool1.inputSchema = {
            "type": "object",
            "properties": {
                "path": {"type": "string", "description": "文件路径"}
            },
            "required": ["path"]
        }
        
        mock_tool2 = MagicMock()
        mock_tool2.name = "write_file"
        mock_tool2.description = "写入文件内容"
        mock_tool2.inputSchema = {
            "type": "object",
            "properties": {
                "path": {"type": "string", "description": "文件路径"},
                "content": {"type": "string", "description": "文件内容"}
            },
            "required": ["path", "content"]
        }
        
        # 模拟服务器信息
        mock_server_info = MagicMock()
        mock_server_info.name = "filesystem-server"
        mock_server_info.version = "1.0.0"
        
        # 模拟验证结果
        mock_validation_result = MagicMock()
        mock_validation_result.success = True
        mock_validation_result.available_tools = [mock_tool1, mock_tool2]
        mock_validation_result.server_info = mock_server_info
        mock_validation_result.error = None
        
        # 模拟沙盒
        mock_sandbox = AsyncMock()
        mock_sandbox.validate_mcp_server = AsyncMock(return_value=mock_validation_result)
        mock_sandbox.test_mcp_tool = AsyncMock(return_value={"success": True, "result": "测试成功"})
        
        return mock_sandbox
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.get_mcp_sandbox_service')
    async def test_import_mcp_server_stdio_success(self, mock_get_sandbox, mock_get_repo,
                                                  mock_repository, mock_mcp_sandbox):
        """测试 stdio MCP 服务器导入成功"""
        mock_get_repo.return_value = mock_repository
        mock_get_sandbox.return_value = mock_mcp_sandbox
        
        from backend.routers.tools import import_mcp_server_advanced, MCPImportAdvancedRequest
        
        request = MCPImportAdvancedRequest(**SAMPLE_MCP_STDIO_REQUEST)
        result = await import_mcp_server_advanced(request)
        
        # 验证结果
        assert result.success is True
        assert result.import_id is not None
        assert result.total_discovered == 2
        assert result.total_imported == 2
        assert result.total_failed == 0
        assert result.server_info is not None
        assert result.server_info["transport"] == "stdio"
        assert result.progress.status == "completed"
        assert result.progress.progress == 1.0
        
        # 验证服务调用
        mock_mcp_sandbox.validate_mcp_server.assert_called()
        assert mock_repository.create.call_count == 2  # 两个工具
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.get_mcp_sandbox_service')
    async def test_import_mcp_server_http_selective(self, mock_get_sandbox, mock_get_repo,
                                                   mock_repository, mock_mcp_sandbox):
        """测试 HTTP MCP 服务器选择性导入"""
        mock_get_repo.return_value = mock_repository
        mock_get_sandbox.return_value = mock_mcp_sandbox
        
        from backend.routers.tools import import_mcp_server_advanced, MCPImportAdvancedRequest
        
        request = MCPImportAdvancedRequest(**SAMPLE_MCP_HTTP_REQUEST)
        result = await import_mcp_server_advanced(request)
        
        # 验证结果
        assert result.success is True
        assert result.total_discovered == 2
        assert result.total_imported == 2  # 选择了两个工具
        assert result.server_info["transport"] == "http"
        
        # 验证工具定义包含自定义配置
        call_args_list = mock_repository.create.call_args_list
        for call_args in call_args_list:
            tool_def = call_args[0][0]
            assert tool_def["toolId"].startswith("fs.")  # 自定义前缀
            assert tool_def["category"] == "filesystem"  # 自定义分类
            assert "文件系统工具:" in tool_def["descriptionUser"]  # 自定义描述模板
    
    @patch('backend.routers.tools.get_mcp_sandbox_service')
    async def test_import_mcp_server_connection_failure(self, mock_get_sandbox):
        """测试 MCP 服务器连接失败"""
        # 模拟连接失败
        mock_sandbox = AsyncMock()
        mock_validation_result = MagicMock()
        mock_validation_result.success = False
        mock_validation_result.error = "连接超时"
        mock_sandbox.validate_mcp_server = AsyncMock(return_value=mock_validation_result)
        mock_get_sandbox.return_value = mock_sandbox
        
        from backend.routers.tools import import_mcp_server_advanced, MCPImportAdvancedRequest
        
        request = MCPImportAdvancedRequest(**SAMPLE_MCP_STDIO_REQUEST)
        result = await import_mcp_server_advanced(request)
        
        # 验证失败结果
        assert result.success is False
        assert "连接失败" in result.message
        assert result.progress.status == "failed"
        assert len(result.progress.errors) > 0
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.get_mcp_sandbox_service')
    async def test_import_mcp_server_partial_failure(self, mock_get_sandbox, mock_get_repo,
                                                    mock_mcp_sandbox):
        """测试 MCP 导入部分失败"""
        # 模拟仓库创建失败
        mock_repository = AsyncMock()
        mock_repository.create = AsyncMock(side_effect=[True, Exception("保存失败")])
        mock_get_repo.return_value = mock_repository
        mock_get_sandbox.return_value = mock_mcp_sandbox
        
        from backend.routers.tools import import_mcp_server_advanced, MCPImportAdvancedRequest
        
        request = MCPImportAdvancedRequest(**SAMPLE_MCP_STDIO_REQUEST)
        result = await import_mcp_server_advanced(request)
        
        # 验证部分成功结果
        assert result.success is True  # 至少有一个成功
        assert result.total_imported == 1
        assert result.total_failed == 1
        assert len(result.failed_tools) == 1
        assert len(result.progress.errors) == 1
    
    @patch('backend.routers.tools.get_mcp_sandbox_service')
    async def test_discover_mcp_tools(self, mock_get_sandbox, mock_mcp_sandbox):
        """测试 MCP 工具发现（不导入）"""
        mock_get_sandbox.return_value = mock_mcp_sandbox
        
        from backend.routers.tools import discover_mcp_tools, MCPImportAdvancedRequest
        
        request = MCPImportAdvancedRequest(**SAMPLE_MCP_STDIO_REQUEST)
        result = await discover_mcp_tools(request)
        
        # 验证发现结果
        assert result["success"] is True
        assert result["total_tools"] == 2
        assert len(result["discovered_tools"]) == 2
        assert result["server_info"] is not None
        
        # 验证工具信息
        tools = result["discovered_tools"]
        tool_names = [tool["name"] for tool in tools]
        assert "read_file" in tool_names
        assert "write_file" in tool_names
    
    def test_filter_tools_for_import(self):
        """测试工具过滤逻辑"""
        from backend.routers.tools import _filter_tools_for_import, MCPImportAdvancedRequest
        
        discovered_tools = [
            {"name": "read_file", "description": "读取文件"},
            {"name": "write_file", "description": "写入文件"},
            {"name": "delete_file", "description": "删除文件"}
        ]
        
        # 测试导入全部
        request_all = MCPImportAdvancedRequest(
            transport="stdio",
            command="test",
            import_all=True
        )
        filtered = _filter_tools_for_import(discovered_tools, request_all)
        assert len(filtered) == 3
        
        # 测试选择性导入
        request_selective = MCPImportAdvancedRequest(
            transport="stdio",
            command="test",
            import_all=False,
            selected_tools=["read_file", "write_file"]
        )
        filtered = _filter_tools_for_import(discovered_tools, request_selective)
        assert len(filtered) == 2
        assert all(tool["name"] in ["read_file", "write_file"] for tool in filtered)
    
    async def test_create_mcp_tool_definition(self):
        """测试 MCP 工具定义创建"""
        from backend.routers.tools import _create_mcp_tool_definition, MCPImportAdvancedRequest
        
        tool_info = {
            "name": "read_file",
            "description": "读取文件内容",
            "input_schema": {
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "文件路径"}
                }
            }
        }
        
        request = MCPImportAdvancedRequest(
            transport="stdio",
            command="uvx",
            args=["mcp-server-filesystem"],
            tool_prefix="fs",
            category_override="filesystem",
            description_template="文件系统: {description}"
        )
        
        server_info = {"transport": "stdio", "command": "uvx"}
        
        tool_def = await _create_mcp_tool_definition(tool_info, request, server_info)
        
        # 验证工具定义
        assert tool_def["toolId"].startswith("fs.read_file.")
        assert tool_def["displayName"] == "MCP: read_file"
        assert tool_def["category"] == "filesystem"
        assert tool_def["transport"] == "stdio"
        assert tool_def["runtime"]["mcp_tool_name"] == "read_file"
        assert tool_def["runtime"]["command"] == "uvx"
        assert tool_def["descriptionUser"] == "文件系统: 读取文件内容"
        assert "read_file" in tool_def["aliases"]
    
    def test_format_description(self):
        """测试描述格式化"""
        from backend.routers.tools import _format_description
        
        # 无模板
        result = _format_description("测试描述", None)
        assert result == "测试描述"
        
        # 有模板
        template = "工具: {description}"
        result = _format_description("测试描述", template)
        assert result == "工具: 测试描述"
    
    async def test_generate_mcp_tool_examples(self):
        """测试 MCP 工具示例生成"""
        from backend.routers.tools import _generate_mcp_tool_examples
        
        tool_definition = {
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {
                    "path": {"type": "string"},
                    "encoding": {"type": "string"}
                }
            }
        }
        
        tool_info = {"name": "read_file", "description": "读取文件"}
        
        examples = await _generate_mcp_tool_examples(tool_definition, tool_info)
        
        assert len(examples) == 1
        example = examples[0]
        assert example["name"] == "使用 read_file"
        assert "path" in example["input"]
        assert "encoding" in example["input"]
        assert example["mcp_call"]["method"] == "tools/call"
        assert example["mcp_call"]["params"]["name"] == "read_file"
    
    def test_generate_mcp_import_suggestions(self):
        """测试 MCP 导入建议生成"""
        from backend.routers.tools import _generate_mcp_import_suggestions, MCPImportAdvancedRequest
        
        imported_tools = [{"name": f"tool{i}"} for i in range(15)]  # 15个工具
        failed_tools = [{"name": "failed_tool", "error": "测试错误"}]
        
        request = MCPImportAdvancedRequest(
            transport="stdio",
            command="test",
            validate_tools=False,
            generate_examples=False
        )
        
        suggestions = _generate_mcp_import_suggestions(imported_tools, failed_tools, request)
        
        # 验证建议
        assert any("1 个工具导入失败" in s for s in suggestions)
        assert any("导入了大量工具" in s for s in suggestions)
        assert any("建议启用工具验证" in s for s in suggestions)
        assert any("建议生成使用示例" in s for s in suggestions)

if __name__ == "__main__":
    pytest.main([__file__])