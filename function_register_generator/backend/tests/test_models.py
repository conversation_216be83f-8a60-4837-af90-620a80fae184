import pytest
from datetime import datetime
from pydantic import ValidationError as PydanticValidationError

from models import (
    ToolDocument, RuntimeConfig, OwnerInfo, AuthConfig, ToolExample,
    TransportType, VisibilityType, AuthType, FaultToleranceLevel,
    validate_tool_document, ToolCreateRequest
)

class TestToolDocument:
    """测试 ToolDocument 模型"""
    
    def test_minimal_valid_tool(self):
        """测试最小有效工具文档"""
        tool = ToolDocument(
            toolId="test.tool",
            displayName="测试工具",
            runtime=RuntimeConfig(transport=TransportType.HTTP, endpoint="https://api.example.com")
        )
        
        assert tool.toolId == "test.tool"
        assert tool.displayName == "测试工具"
        assert tool.version == "1.0.0"
        assert tool.versionSeq == 1
        assert tool.visibility == VisibilityType.PUBLIC
        assert tool.runtime.transport == TransportType.HTTP
        assert tool.deleteFlag == 0
        assert isinstance(tool.createdTime, datetime)
        assert isinstance(tool.updatedTime, datetime)
    
    def test_tool_id_validation(self):
        """测试工具ID验证"""
        # 有效的工具ID
        valid_ids = [
            "weather.api",
            "data.processor.v2",
            "my-tool.service",
            "tool_name.category"
        ]
        
        for tool_id in valid_ids:
            tool = ToolDocument(
                toolId=tool_id,
                displayName="测试",
                runtime=RuntimeConfig(transport=TransportType.HTTP, endpoint="https://api.example.com")
            )
            assert tool.toolId == tool_id.lower()
        
        # 无效的工具ID
        invalid_ids = [
            "",
            "invalid id with spaces",
            "<EMAIL>",
            "tool#hash"
        ]
        
        for tool_id in invalid_ids:
            with pytest.raises(PydanticValidationError):
                ToolDocument(
                    toolId=tool_id,
                    displayName="测试",
                    runtime=RuntimeConfig(transport=TransportType.HTTP, endpoint="https://api.example.com")
                )
    
    def test_version_validation(self):
        """测试版本号验证"""
        # 有效版本号
        valid_versions = ["1.0.0", "2.1.3", "1.0.0-beta", "3.2.1-alpha"]
        
        for version in valid_versions:
            tool = ToolDocument(
                toolId="test.tool",
                displayName="测试",
                version=version,
                runtime=RuntimeConfig(transport=TransportType.HTTP, endpoint="https://api.example.com")
            )
            assert tool.version == version
        
        # 无效版本号
        invalid_versions = ["1.0", "v1.0.0", "1.0.0.0", "latest"]
        
        for version in invalid_versions:
            with pytest.raises(PydanticValidationError):
                ToolDocument(
                    toolId="test.tool",
                    displayName="测试",
                    version=version,
                    runtime=RuntimeConfig(transport=TransportType.HTTP, endpoint="https://api.example.com")
                )
    
    def test_content_generation(self):
        """测试搜索内容自动生成"""
        tool = ToolDocument(
            toolId="weather.api",
            displayName="天气API",
            aliases=["weather", "forecast"],
            category="weather",
            descriptionUser="获取天气信息",
            runtime=RuntimeConfig(transport=TransportType.HTTP, endpoint="https://api.weather.com")
        )
        
        expected_content = "天气API weather forecast 获取天气信息 weather"
        assert tool.content == expected_content
    
    def test_http_runtime_config(self):
        """测试HTTP运行时配置"""
        runtime = RuntimeConfig(
            transport=TransportType.HTTP,
            endpoint="https://api.example.com/data",
            httpMethod="POST",
            auth=AuthConfig(type=AuthType.API_KEY, env_keys=["API_KEY"])
        )
        
        assert runtime.transport == TransportType.HTTP
        assert runtime.endpoint == "https://api.example.com/data"
        assert runtime.httpMethod == "POST"
        assert runtime.auth.type == AuthType.API_KEY
        assert runtime.auth.env_keys == ["API_KEY"]
    
    def test_python_runtime_config(self):
        """测试Python运行时配置"""
        runtime = RuntimeConfig(
            transport=TransportType.PYTHON,
            script_content="ZGVmIGhlbGxvKCk6CiAgICByZXR1cm4gIkhlbGxvIg==",  # base64: def hello(): return "Hello"
            entry_function="hello"
        )
        
        assert runtime.transport == TransportType.PYTHON
        assert runtime.script_content == "ZGVmIGhlbGxvKCk6CiAgICByZXR1cm4gIkhlbGxvIg=="
        assert runtime.entry_function == "hello"
    
    def test_mcp_runtime_config(self):
        """测试MCP运行时配置"""
        runtime = RuntimeConfig(
            transport=TransportType.STDIO,
            command="uvx",
            args=["mcp-server-filesystem", "--path", "/tmp"],
            env={"PATH": "/usr/bin"}
        )
        
        assert runtime.transport == TransportType.STDIO
        assert runtime.command == "uvx"
        assert runtime.args == ["mcp-server-filesystem", "--path", "/tmp"]
        assert runtime.env == {"PATH": "/usr/bin"}
    
    def test_complete_tool_document(self):
        """测试完整的工具文档"""
        tool = ToolDocument(
            toolId="weather.openmeteo.forecast",
            displayName="天气预报查询",
            aliases=["weather", "forecast"],
            version="1.2.0",
            versionSeq=2,
            visibility=VisibilityType.PUBLIC,
            category="weather",
            capabilities=["http", "sync"],
            locales=["zh-CN", "en-US"],
            
            owner=OwnerInfo(
                org="Weather Team",
                contact="<EMAIL>",
                license="MIT",
                userId="user001"
            ),
            
            runtime=RuntimeConfig(
                transport=TransportType.HTTP,
                endpoint="https://api.open-meteo.com/v1/forecast",
                httpMethod="GET",
                auth=AuthConfig(type=AuthType.NONE),
                fault_tolerance=FaultToleranceLevel.HIGH
            ),
            
            descriptionUser="获取指定位置的天气预报",
            descriptionDev="调用Open-Meteo API获取天气数据",
            
            inputsDeveloperSchema={
                "type": "object",
                "required": ["latitude", "longitude"],
                "properties": {
                    "latitude": {"type": "number"},
                    "longitude": {"type": "number"}
                }
            },
            
            outputsSchema={
                "type": "object",
                "properties": {
                    "temperature": {"type": "number"},
                    "humidity": {"type": "number"}
                }
            },
            
            examples=[
                ToolExample(
                    userQuery="查询北京天气",
                    parsedInputs={"latitude": 39.9042, "longitude": 116.4074}
                )
            ]
        )
        
        # 验证所有字段
        assert tool.toolId == "weather.openmeteo.forecast"
        assert tool.displayName == "天气预报查询"
        assert tool.version == "1.2.0"
        assert tool.owner.org == "Weather Team"
        assert tool.runtime.endpoint == "https://api.open-meteo.com/v1/forecast"
        assert len(tool.examples) == 1
        assert tool.examples[0].userQuery == "查询北京天气"

class TestToolValidation:
    """测试工具文档验证"""
    
    def test_valid_tool_validation(self):
        """测试有效工具验证"""
        tool = ToolDocument(
            toolId="valid.tool",
            displayName="有效工具",
            descriptionUser="这是一个有效的工具",
            category="test",
            runtime=RuntimeConfig(
                transport=TransportType.HTTP,
                endpoint="https://api.example.com"
            ),
            inputsDeveloperSchema={
                "type": "object",
                "properties": {"param": {"type": "string"}}
            },
            examples=[
                ToolExample(userQuery="测试", parsedInputs={"param": "value"})
            ]
        )
        
        result = validate_tool_document(tool)
        assert result.success is True
        assert len(result.errors) == 0
    
    def test_invalid_http_tool_validation(self):
        """测试无效HTTP工具验证"""
        tool = ToolDocument(
            toolId="invalid.http.tool",
            displayName="无效HTTP工具",
            runtime=RuntimeConfig(transport=TransportType.HTTP)  # 缺少endpoint
        )
        
        result = validate_tool_document(tool)
        assert result.success is False
        assert len(result.errors) > 0
        assert any("endpoint" in error.field for error in result.errors)
    
    def test_invalid_python_tool_validation(self):
        """测试无效Python工具验证"""
        tool = ToolDocument(
            toolId="invalid.python.tool",
            displayName="无效Python工具",
            runtime=RuntimeConfig(transport=TransportType.PYTHON)  # 缺少脚本和入口函数
        )
        
        result = validate_tool_document(tool)
        assert result.success is False
        assert len(result.errors) > 0
    
    def test_validation_suggestions(self):
        """测试验证建议"""
        tool = ToolDocument(
            toolId="minimal.tool",
            displayName="最小工具",
            runtime=RuntimeConfig(
                transport=TransportType.HTTP,
                endpoint="https://api.example.com"
            )
        )
        
        result = validate_tool_document(tool)
        assert result.success is True  # 没有错误
        assert len(result.suggestions) > 0  # 但有改进建议

class TestToolCreateRequest:
    """测试工具创建请求模型"""
    
    def test_minimal_create_request(self):
        """测试最小创建请求"""
        request = ToolCreateRequest(displayName="新工具")
        
        assert request.displayName == "新工具"
        assert request.transport == TransportType.HTTP
        assert request.httpMethod == "GET"
        assert request.visibility == VisibilityType.PUBLIC
    
    def test_complete_create_request(self):
        """测试完整创建请求"""
        request = ToolCreateRequest(
            displayName="完整工具",
            category="api",
            transport=TransportType.HTTP,
            endpoint="https://api.example.com",
            httpMethod="POST",
            description="这是一个完整的工具",
            inputSchema={
                "type": "object",
                "properties": {"param": {"type": "string"}}
            },
            visibility=VisibilityType.INTERNAL
        )
        
        assert request.displayName == "完整工具"
        assert request.category == "api"
        assert request.endpoint == "https://api.example.com"
        assert request.httpMethod == "POST"
        assert request.visibility == VisibilityType.INTERNAL

if __name__ == "__main__":
    pytest.main([__file__])