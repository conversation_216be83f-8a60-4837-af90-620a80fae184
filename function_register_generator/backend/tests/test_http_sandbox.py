import pytest
import async<PERSON>
import json
from unittest.mock import AsyncMock, MagicMock, patch
import httpx

from services.http_sandbox_service import (
    HTTPSandboxService,
    HTTPSandboxRequest,
    HTTPSandboxConfig,
    HTTPValidationResult,
    get_http_sandbox_service
)


class TestHTTPSandboxRequest:
    """测试 HTTP 沙盒请求模型"""
    
    def test_valid_request(self):
        """测试有效请求"""
        request = HTTPSandboxRequest(
            url="https://api.example.com/data",
            method="GET",
            headers={"Authorization": "Bearer token"},
            params={"limit": 10},
            timeout=30
        )
        
        assert request.url == "https://api.example.com/data"
        assert request.method == "GET"
        assert request.headers["Authorization"] == "Bearer token"
        assert request.params["limit"] == 10
        assert request.timeout == 30
    
    def test_method_validation(self):
        """测试 HTTP 方法验证"""
        # 有效方法
        for method in ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"]:
            request = HTTPSandboxRequest(url="https://example.com", method=method)
            assert request.method == method.upper()
        
        # 无效方法
        with pytest.raises(ValueError, match="不支持的 HTTP 方法"):
            HTTPSandboxRequest(url="https://example.com", method="INVALID")
    
    def test_timeout_validation(self):
        """测试超时验证"""
        # 有效超时
        request = HTTPSandboxRequest(url="https://example.com", timeout=30)
        assert request.timeout == 30
        
        # 无效超时
        with pytest.raises(ValueError, match="超时时间必须在"):
            HTTPSandboxRequest(url="https://example.com", timeout=0)
        
        with pytest.raises(ValueError, match="超时时间必须在"):
            HTTPSandboxRequest(url="https://example.com", timeout=200)
    
    def test_url_validation(self):
        """测试 URL 验证"""
        # 有效 URL
        valid_urls = [
            "https://api.example.com",
            "http://localhost:8080/api",
            "https://api.github.com/users/octocat"
        ]
        
        for url in valid_urls:
            request = HTTPSandboxRequest(url=url)
            assert request.url == url
        
        # 无效 URL
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",
            "javascript:alert('xss')",
            ""
        ]
        
        for url in invalid_urls:
            with pytest.raises(ValueError):
                HTTPSandboxRequest(url=url)


class TestHTTPSandboxService:
    """测试 HTTP 沙盒服务"""
    
    @pytest.fixture
    def sandbox_service(self):
        """创建沙盒服务实例"""
        return HTTPSandboxService()
    
    @pytest.fixture
    def mock_response(self):
        """模拟 HTTP 响应"""
        response = MagicMock()
        response.status_code = 200
        response.headers = {"content-type": "application/json"}
        response.content = b'{"message": "success", "data": [1, 2, 3]}'
        response.json.return_value = {"message": "success", "data": [1, 2, 3]}
        response.text = '{"message": "success", "data": [1, 2, 3]}'
        return response
    
    @pytest.mark.asyncio
    async def test_successful_validation(self, sandbox_service, mock_response):
        """测试成功的验证"""
        request = HTTPSandboxRequest(
            url="https://api.example.com/data",
            method="GET"
        )
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.aclose = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.request.return_value = mock_response
            
            # 手动设置客户端
            sandbox_service.client = mock_client
            result = await sandbox_service.validate_http_tool(request)
            
            assert result.success
            assert result.status_code == 200
            assert result.content_type == "application/json"
            assert result.sample_response == {"message": "success", "data": [1, 2, 3]}
            assert result.latency_ms is not None
            assert result.inferred_output_schema is not None
            assert result.quickstart is not None
    
    @pytest.mark.asyncio
    async def test_timeout_error(self, sandbox_service):
        """测试超时错误"""
        request = HTTPSandboxRequest(
            url="https://api.example.com/slow",
            timeout=1
        )
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.aclose = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.request.side_effect = httpx.TimeoutException("Request timed out")
            
            # 手动设置客户端
            sandbox_service.client = mock_client
            result = await sandbox_service.validate_http_tool(request)
            
            assert not result.success
            assert result.error == "请求超时"
    
    @pytest.mark.asyncio
    async def test_connection_error(self, sandbox_service):
        """测试连接错误"""
        request = HTTPSandboxRequest(
            url="https://nonexistent.example.com/api"
        )
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.aclose = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.request.side_effect = httpx.ConnectError("Connection failed")
            
            # 手动设置客户端
            sandbox_service.client = mock_client
            result = await sandbox_service.validate_http_tool(request)
            
            assert not result.success
            assert "连接失败" in result.error
    
    @pytest.mark.asyncio
    async def test_http_status_error(self, sandbox_service):
        """测试 HTTP 状态错误"""
        request = HTTPSandboxRequest(
            url="https://api.example.com/notfound"
        )
        
        error_response = MagicMock()
        error_response.status_code = 404
        error_response.reason_phrase = "Not Found"
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.aclose = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.request.side_effect = httpx.HTTPStatusError(
                "404 Not Found", request=None, response=error_response
            )
            
            # 手动设置客户端
            sandbox_service.client = mock_client
            result = await sandbox_service.validate_http_tool(request)
            
            assert not result.success
            assert result.status_code == 404
            assert "HTTP 错误: 404" in result.error
    
    @pytest.mark.asyncio
    async def test_security_check(self, sandbox_service):
        """测试安全检查"""
        # 测试私有IP地址
        private_urls = [
            "http://127.0.0.1:8080/api",
            "http://localhost/api",
            "http://***********/api"
        ]
        
        for url in private_urls:
            warnings = await sandbox_service._security_check(url)
            assert len(warnings) > 0
            assert any("私有IP" in warning or "禁止访问" in warning for warning in warnings)
    
    def test_is_private_ip(self, sandbox_service):
        """测试私有IP检查"""
        # 私有IP
        private_ips = ["127.0.0.1", "***********", "********", "**********"]
        for ip in private_ips:
            assert sandbox_service._is_private_ip(ip)
        
        # 公共IP
        public_ips = ["*******", "*******", "**************"]
        for ip in public_ips:
            assert not sandbox_service._is_private_ip(ip)
        
        # 域名
        domains = ["example.com", "api.github.com"]
        for domain in domains:
            assert not sandbox_service._is_private_ip(domain)
    
    def test_infer_output_schema(self, sandbox_service):
        """测试输出 Schema 推断"""
        # 测试不同数据类型
        test_cases = [
            (None, {"type": "null"}),
            (True, {"type": "boolean"}),
            (42, {"type": "integer"}),
            (3.14, {"type": "number"}),
            ("hello", {"type": "string"}),
            ([], {"type": "array", "items": {}}),
            ([1, 2, 3], {"type": "array", "items": {"type": "integer"}}),
            ({"key": "value"}, {
                "type": "object",
                "properties": {"key": {"type": "string"}},
                "required": ["key"]
            })
        ]
        
        for data, expected_type in test_cases:
            schema = sandbox_service._infer_output_schema(data)
            assert schema["type"] == expected_type["type"]
    
    def test_datetime_string_detection(self, sandbox_service):
        """测试日期时间字符串检测"""
        datetime_strings = [
            "2023-12-25T10:30:00",
            "2023-12-25 10:30:00",
            "12/25/2023"
        ]
        
        for dt_str in datetime_strings:
            assert sandbox_service._is_datetime_string(dt_str)
        
        non_datetime_strings = ["hello", "123", "not-a-date"]
        for non_dt_str in non_datetime_strings:
            assert not sandbox_service._is_datetime_string(non_dt_str)
    
    def test_email_string_detection(self, sandbox_service):
        """测试邮箱字符串检测"""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            assert sandbox_service._is_email_string(email)
        
        invalid_emails = ["not-an-email", "@domain.com", "user@", "user.domain.com"]
        for email in invalid_emails:
            assert not sandbox_service._is_email_string(email)
    
    def test_url_string_detection(self, sandbox_service):
        """测试 URL 字符串检测"""
        valid_urls = [
            "https://example.com",
            "http://api.github.com/users",
            "https://subdomain.example.org/path?query=value"
        ]
        
        for url in valid_urls:
            assert sandbox_service._is_url_string(url)
        
        invalid_urls = ["not-a-url", "ftp://example.com", "example.com", ""]
        for url in invalid_urls:
            assert not sandbox_service._is_url_string(url)
    
    def test_generate_curl_command(self, sandbox_service):
        """测试 cURL 命令生成"""
        request = HTTPSandboxRequest(
            url="https://api.example.com/data",
            method="POST",
            headers={"Authorization": "Bearer token"},
            params={"limit": 10},
            data={"name": "test"}
        )
        
        curl_cmd = sandbox_service._generate_curl_command(request)
        
        assert "curl" in curl_cmd
        assert "-X POST" in curl_cmd
        assert "Authorization: Bearer token" in curl_cmd
        assert "https://api.example.com/data?limit=10" in curl_cmd
        assert '{"name": "test"}' in curl_cmd
    
    def test_generate_python_code(self, sandbox_service):
        """测试 Python 代码生成"""
        request = HTTPSandboxRequest(
            url="https://api.example.com/data",
            method="GET",
            headers={"Authorization": "Bearer token"},
            params={"limit": 10}
        )
        
        python_code = sandbox_service._generate_python_code(request)
        
        assert "import requests" in python_code
        assert "requests.get(" in python_code
        assert "https://api.example.com/data?limit=10" in python_code
        assert "Authorization" in python_code
        assert "response.json()" in python_code
    
    def test_generate_javascript_code(self, sandbox_service):
        """测试 JavaScript 代码生成"""
        request = HTTPSandboxRequest(
            url="https://api.example.com/data",
            method="POST",
            data={"name": "test"}
        )
        
        js_code = sandbox_service._generate_javascript_code(request)
        
        assert "fetch(" in js_code
        assert "https://api.example.com/data" in js_code
        assert '"method": "POST"' in js_code
        assert '\\"name\\": \\"test\\"' in js_code
        assert "response.json()" in js_code


class TestHTTPSandboxConfig:
    """测试 HTTP 沙盒配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = HTTPSandboxConfig()
        
        assert config.DEFAULT_TIMEOUT == 30
        assert config.MAX_TIMEOUT == 120
        assert config.MAX_RESPONSE_SIZE == 10 * 1024 * 1024
        assert config.MAX_REDIRECTS == 5
        assert "http" in config.ALLOWED_SCHEMES
        assert "https" in config.ALLOWED_SCHEMES
        assert "localhost" in config.BLOCKED_DOMAINS
        assert config.ALLOWED_DOMAINS is None
    
    def test_default_headers(self):
        """测试默认请求头"""
        config = HTTPSandboxConfig()
        
        assert "User-Agent" in config.DEFAULT_HEADERS
        assert "Accept" in config.DEFAULT_HEADERS
        assert "Accept-Encoding" in config.DEFAULT_HEADERS
        assert "ToolRegistry-Sandbox" in config.DEFAULT_HEADERS["User-Agent"]


class TestGlobalService:
    """测试全局服务获取"""
    
    @pytest.mark.asyncio
    async def test_get_http_sandbox_service(self):
        """测试获取全局服务实例"""
        service1 = await get_http_sandbox_service()
        service2 = await get_http_sandbox_service()
        
        # 应该返回同一个实例
        assert service1 is service2
        assert isinstance(service1, HTTPSandboxService)


class TestComplexScenarios:
    """测试复杂场景"""
    
    @pytest.mark.asyncio
    async def test_json_response_schema_inference(self):
        """测试复杂 JSON 响应的 Schema 推断"""
        sandbox_service = HTTPSandboxService()
        
        complex_response = {
            "user": {
                "id": 123,
                "name": "John Doe",
                "email": "<EMAIL>",
                "active": True,
                "created_at": "2023-12-25T10:30:00Z",
                "profile": {
                    "avatar": "https://example.com/avatar.jpg",
                    "bio": "Software developer"
                }
            },
            "posts": [
                {"id": 1, "title": "First Post", "likes": 10},
                {"id": 2, "title": "Second Post", "likes": 5}
            ],
            "meta": {
                "total": 2,
                "page": 1,
                "per_page": 10
            }
        }
        
        schema = sandbox_service._infer_output_schema(complex_response)
        
        assert schema["type"] == "object"
        assert "user" in schema["properties"]
        assert "posts" in schema["properties"]
        assert "meta" in schema["properties"]
        
        # 检查用户对象
        user_schema = schema["properties"]["user"]
        assert user_schema["type"] == "object"
        assert "email" in user_schema["properties"]
        assert user_schema["properties"]["email"]["format"] == "email"
        assert user_schema["properties"]["created_at"]["format"] == "date-time"
        assert user_schema["properties"]["profile"]["properties"]["avatar"]["format"] == "uri"
        
        # 检查数组
        posts_schema = schema["properties"]["posts"]
        assert posts_schema["type"] == "array"
        assert posts_schema["items"]["type"] == "object"
    
    @pytest.mark.asyncio
    async def test_request_with_all_parameters(self):
        """测试包含所有参数的请求"""
        sandbox_service = HTTPSandboxService()
        
        request = HTTPSandboxRequest(
            url="https://api.example.com/users",
            method="POST",
            headers={
                "Authorization": "Bearer token123",
                "Content-Type": "application/json",
                "X-Custom-Header": "custom-value"
            },
            params={
                "page": 1,
                "limit": 20,
                "sort": "created_at"
            },
            data={
                "name": "John Doe",
                "email": "<EMAIL>",
                "age": 30,
                "active": True
            },
            timeout=45,
            follow_redirects=False,
            verify_ssl=True
        )
        
        # 测试请求准备
        request_kwargs = await sandbox_service._prepare_request(request)
        
        assert request_kwargs["method"] == "POST"
        assert request_kwargs["url"] == "https://api.example.com/users"
        assert request_kwargs["timeout"] == 45
        assert request_kwargs["follow_redirects"] is False
        assert "Authorization" in request_kwargs["headers"]
        assert "params" in request_kwargs
        assert "json" in request_kwargs
        
        # 测试快速开始代码生成
        quickstart = sandbox_service._generate_quickstart(request)
        
        assert "curl" in quickstart
        assert "python" in quickstart
        assert "javascript" in quickstart
        
        # 验证 cURL 命令
        curl_cmd = quickstart["curl"]
        assert "-X POST" in curl_cmd
        assert "Authorization: Bearer token123" in curl_cmd
        assert "page=1" in curl_cmd
        assert "limit=20" in curl_cmd
        
        # 验证 Python 代码
        python_code = quickstart["python"]
        assert "requests.post(" in python_code
        assert "json=" in python_code
        assert "headers=" in python_code
        
        # 验证 JavaScript 代码
        js_code = quickstart["javascript"]
        assert "fetch(" in js_code
        assert '"method": "POST"' in js_code
        assert "JSON.stringify" in js_code or '"body"' in js_code


if __name__ == "__main__":
    pytest.main([__file__])