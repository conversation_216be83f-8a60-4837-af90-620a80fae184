import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from services.intelligent_completion_service import (
    IntelligentCompletionService, 
    CompletionRequest, 
    CompletionResult,
    get_intelligent_completion_service
)
from services.llm_service import LMStudioService


class TestIntelligentCompletionService:
    """测试智能补全服务"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟 LLM 服务"""
        mock_service = AsyncMock(spec=LMStudioService)
        
        # 模拟元数据补全响应
        mock_service.complete_tool_metadata.return_value = {
            "success": True,
            "completions": {
                "category": "weather",
                "descriptionUser": "获取天气信息",
                "descriptionDev": "调用天气API获取气象数据",
                "capabilities": ["http", "sync", "public"],
                "aliases": ["天气", "weather"]
            }
        }
        
        # 模拟工具生成响应
        mock_service.generate_tool_from_description.return_value = {
            "success": True,
            "tool_definition": {
                "toolId": "text.processor.count",
                "displayName": "文本统计",
                "category": "utility",
                "transport": "python",
                "runtime": {
                    "transport": "python",
                    "entry_function": "count_text"
                },
                "inputsDeveloperSchema": {
                    "type": "object",
                    "required": ["text"],
                    "properties": {
                        "text": {"type": "string", "description": "要统计的文本"}
                    }
                },
                "outputsSchema": {
                    "type": "object",
                    "properties": {
                        "character_count": {"type": "integer"},
                        "word_count": {"type": "integer"}
                    }
                },
                "descriptionUser": "统计文本字符和单词数量"
            }
        }
        
        # 模拟示例生成响应
        mock_service.generate_examples.return_value = [
            {
                "userQuery": "统计这段文本",
                "parsedInputs": {"text": "示例文本"}
            }
        ]
        
        return mock_service
    
    @pytest.fixture
    def completion_service(self, mock_llm_service):
        """创建补全服务实例"""
        return IntelligentCompletionService(mock_llm_service)
    
    @pytest.mark.asyncio
    async def test_complete_from_curl(self, completion_service):
        """测试从 cURL 命令补全"""
        request = CompletionRequest(
            tool_name="GitHub API",
            curl_command='curl -X GET "https://api.github.com/users/octocat" -H "Accept: application/json"'
        )
        
        with patch('services.intelligent_completion_service.curl_to_schema') as mock_curl_parser:
            mock_curl_parser.return_value = {
                "curl_info": {
                    "method": "GET",
                    "url": "https://api.github.com/users/octocat",
                    "headers": {"Accept": "application/json"}
                },
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "username": {"type": "string", "description": "GitHub用户名"}
                    }
                },
                "output_schema": {
                    "type": "object",
                    "properties": {
                        "login": {"type": "string"},
                        "name": {"type": "string"}
                    }
                }
            }
            
            result = await completion_service.complete_tool_definition(request)
            
            assert result.success
            assert result.source == "curl"
            assert result.confidence == 0.9
            assert "toolId" in result.tool_definition
            assert result.tool_definition["displayName"] == "GitHub API"
            assert result.tool_definition["transport"] == "http"
            assert len(result.suggestions) > 0
    
    @pytest.mark.asyncio
    async def test_complete_from_python(self, completion_service):
        """测试从 Python 代码补全"""
        python_code = '''
def calculate_area(length: float, width: float = 1.0) -> float:
    """计算矩形面积"""
    return length * width
'''
        
        request = CompletionRequest(
            tool_name="面积计算器",
            python_code=python_code,
            function_name="calculate_area"
        )
        
        with patch('services.intelligent_completion_service.python_to_schema') as mock_python_parser:
            mock_python_parser.return_value = {
                "function_info": {
                    "name": "calculate_area",
                    "description": "计算矩形面积",
                    "parameters": [
                        {"name": "length", "type_hint": "float"},
                        {"name": "width", "type_hint": "float", "default_value": 1.0}
                    ]
                },
                "input_schema": {
                    "type": "object",
                    "required": ["length"],
                    "properties": {
                        "length": {"type": "number", "description": "长度"},
                        "width": {"type": "number", "description": "宽度", "default": 1.0}
                    }
                },
                "output_schema": {
                    "type": "number",
                    "description": "面积"
                }
            }
            
            result = await completion_service.complete_tool_definition(request)
            
            assert result.success
            assert result.source == "python"
            assert result.confidence == 0.95
            assert result.tool_definition["displayName"] == "面积计算器"
            assert result.tool_definition["transport"] == "python"
            assert "entry_function" in result.tool_definition["runtime"]
    
    @pytest.mark.asyncio
    async def test_complete_from_description(self, completion_service):
        """测试从描述补全"""
        request = CompletionRequest(
            description="统计文本中的字符和单词数量",
            test_inputs={"text": "示例文本"},
            test_outputs={"character_count": 4, "word_count": 2}
        )
        
        result = await completion_service.complete_tool_definition(request)
        
        assert result.success
        assert result.source == "llm"
        assert result.confidence == 0.8
        assert result.tool_definition["displayName"] == "文本统计"
        assert result.tool_definition["category"] == "utility"
    
    @pytest.mark.asyncio
    async def test_complete_from_partial_info(self, completion_service):
        """测试从部分信息补全"""
        request = CompletionRequest(
            tool_name="测试工具",
            transport="http",
            endpoint="https://api.example.com/test",
            description="这是一个测试工具"
        )
        
        result = await completion_service.complete_tool_definition(request)
        
        assert result.success
        assert result.source == "hybrid"
        assert result.confidence == 0.6
        assert result.tool_definition["displayName"] == "测试工具"
        assert result.tool_definition["transport"] == "http"
        assert result.tool_definition["runtime"]["endpoint"] == "https://api.example.com/test"
    
    @pytest.mark.asyncio
    async def test_error_handling(self, completion_service):
        """测试错误处理"""
        request = CompletionRequest(
            curl_command="invalid curl command"
        )
        
        with patch('services.intelligent_completion_service.curl_to_schema') as mock_curl_parser:
            mock_curl_parser.side_effect = Exception("Parse error")
            
            result = await completion_service.complete_tool_definition(request)
            
            assert not result.success
            assert result.source == "curl"
            assert result.error is not None
            assert "Parse error" in result.error
    
    def test_generate_tool_id(self, completion_service):
        """测试工具ID生成"""
        # 测试HTTP工具
        tool_id = completion_service._generate_tool_id("GitHub API", "http")
        assert tool_id == "api.http.github_api"
        
        # 测试Python工具
        tool_id = completion_service._generate_tool_id("calculate_area", "python")
        assert tool_id == "script.python.calculate_area"
        
        # 测试特殊字符处理
        tool_id = completion_service._generate_tool_id("Test-Tool@123", "http")
        assert tool_id == "api.http.test_tool_123"
    
    def test_extract_name_from_url(self, completion_service):
        """测试从URL提取名称"""
        # 测试GitHub API
        name = completion_service._extract_name_from_url("https://api.github.com/users/octocat")
        assert name == "github_com_octocat"
        
        # 测试简单API
        name = completion_service._extract_name_from_url("https://httpbin.org/get")
        assert name == "httpbin_org_get"
    
    def test_infer_schema_from_data(self, completion_service):
        """测试从数据推断schema"""
        test_data = {
            "name": "John",
            "age": 30,
            "active": True,
            "scores": [85, 90, 78],
            "profile": {"city": "Beijing"}
        }
        
        schema = completion_service._infer_schema_from_data(test_data)
        
        assert schema["type"] == "object"
        assert "name" in schema["properties"]
        assert schema["properties"]["name"]["type"] == "string"
        assert schema["properties"]["age"]["type"] == "integer"
        assert schema["properties"]["active"]["type"] == "boolean"
        assert schema["properties"]["scores"]["type"] == "array"
        assert schema["properties"]["profile"]["type"] == "object"
        assert len(schema["required"]) == 5
    
    def test_validate_and_fix_tool_definition(self, completion_service):
        """测试工具定义验证和修正"""
        # 测试不完整的工具定义
        incomplete_def = {
            "displayName": "测试工具"
        }
        
        fixed_def = completion_service._validate_and_fix_tool_definition(incomplete_def)
        
        assert "toolId" in fixed_def
        assert "category" in fixed_def
        assert "transport" in fixed_def
        assert "runtime" in fixed_def
        assert "inputsDeveloperSchema" in fixed_def
        assert "outputsSchema" in fixed_def
        assert fixed_def["displayName"] == "测试工具"
    
    def test_enhance_schema_with_test_data(self, completion_service):
        """测试使用测试数据增强schema"""
        tool_def = {
            "toolId": "test.tool",
            "displayName": "测试工具",
            "inputsDeveloperSchema": {"type": "object"},
            "outputsSchema": {"type": "object"}
        }
        
        test_inputs = {"query": "test", "limit": 10}
        test_outputs = {"results": [], "count": 0}
        
        enhanced_def = completion_service._enhance_schema_with_test_data(
            tool_def, test_inputs, test_outputs
        )
        
        # 检查输入schema
        input_props = enhanced_def["inputsDeveloperSchema"]["properties"]
        assert "query" in input_props
        assert input_props["query"]["type"] == "string"
        assert "limit" in input_props
        assert input_props["limit"]["type"] == "integer"
        
        # 检查输出schema
        output_props = enhanced_def["outputsSchema"]["properties"]
        assert "results" in output_props
        assert output_props["results"]["type"] == "array"
        assert "count" in output_props
        assert output_props["count"]["type"] == "integer"
    
    def test_generate_suggestions(self, completion_service):
        """测试生成建议"""
        tool_def = {
            "displayName": "测试工具",
            "transport": "http",
            "runtime": {}
        }
        
        # 测试cURL来源的建议
        suggestions = completion_service._generate_suggestions(tool_def, "curl")
        assert len(suggestions) > 0
        assert any("错误处理" in s for s in suggestions)
        
        # 测试Python来源的建议
        suggestions = completion_service._generate_suggestions(tool_def, "python")
        assert len(suggestions) > 0
        assert any("类型检查" in s for s in suggestions)
        
        # 测试LLM来源的建议
        suggestions = completion_service._generate_suggestions(tool_def, "llm")
        assert len(suggestions) > 0
        assert any("验证" in s for s in suggestions)


class TestGlobalService:
    """测试全局服务获取"""
    
    @pytest.mark.asyncio
    async def test_get_intelligent_completion_service(self):
        """测试获取全局服务实例"""
        with patch('services.intelligent_completion_service.get_llm_service') as mock_get_llm:
            mock_llm = AsyncMock()
            mock_get_llm.return_value = mock_llm
            
            service1 = await get_intelligent_completion_service()
            service2 = await get_intelligent_completion_service()
            
            # 应该返回同一个实例
            assert service1 is service2
            assert isinstance(service1, IntelligentCompletionService)


if __name__ == "__main__":
    pytest.main([__file__])