import pytest
from parsers.python_parser import (
    PythonScriptAnalyzer, FunctionInfo, FunctionParameter, ParameterType,
    PythonCodeGenerator, analyze_python_code, analyze_python_function, python_to_schema
)


class TestPythonScriptAnalyzer:
    """测试 Python 脚本分析器"""

    def test_simple_function_analysis(self):
        """测试简单函数分析"""
        code = '''
def hello_world():
    """返回问候语"""
    return "Hello, World!"
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        assert len(functions) == 1
        func = functions[0]
        assert func.name == "hello_world"
        assert func.description == "返回问候语"
        assert len(func.parameters) == 0

    def test_function_with_parameters(self):
        """测试带参数的函数"""
        code = '''
def greet(name: str, age: int = 25, active: bool = True):
    """
    问候用户
    name (str): 用户名称
    age (int): 用户年龄
    active (bool): 是否活跃
    """
    return f"Hello {name}, age {age}, active: {active}"
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        assert len(functions) == 1
        func = functions[0]
        assert func.name == "greet"
        assert len(func.parameters) == 3
        
        # 检查第一个参数
        param1 = func.parameters[0]
        assert param1.name == "name"
        assert param1.type_hint == "str"
        assert not param1.has_default
        assert param1.parameter_type == ParameterType.POSITIONAL
        
        # 检查第二个参数
        param2 = func.parameters[1]
        assert param2.name == "age"
        assert param2.type_hint == "int"
        assert param2.has_default
        assert param2.default_value == 25
        assert param2.parameter_type == ParameterType.KEYWORD
        
        # 检查第三个参数
        param3 = func.parameters[2]
        assert param3.name == "active"
        assert param3.type_hint == "bool"
        assert param3.has_default
        assert param3.default_value is True

    def test_function_with_complex_types(self):
        """测试复杂类型的函数"""
        code = '''
from typing import List, Dict, Optional
def process_data(
    items: List[str], 
    config: Dict[str, Any], 
    callback: Optional[callable] = None,
    *args,
    **kwargs
) -> Dict[str, Any]:
    """处理数据"""
    return {"processed": len(items)}
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        assert len(functions) == 1
        func = functions[0]
        assert func.name == "process_data"
        assert func.return_type_hint == "Dict[str, Any]"
        assert len(func.parameters) == 5
        
        # 检查 List 类型参数
        param1 = func.parameters[0]
        assert param1.name == "items"
        assert param1.type_hint == "List[str]"
        
        # 检查 *args 参数
        args_param = func.parameters[3]
        assert args_param.name == "args"
        assert args_param.parameter_type == ParameterType.VAR_POSITIONAL
        
        # 检查 **kwargs 参数
        kwargs_param = func.parameters[4]
        assert kwargs_param.name == "kwargs"
        assert kwargs_param.parameter_type == ParameterType.VAR_KEYWORD

    def test_multiple_functions(self):
        """测试多个函数"""
        code = '''
def func1():
    """第一个函数"""
    pass

def func2(x: int, y: int) -> int:
    """第二个函数"""
    return x + y

class MyClass:
    def method1(self):
        """类方法"""
        pass
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        # 应该找到 3 个函数（包括类方法）
        assert len(functions) == 3
        func_names = [f.name for f in functions]
        assert "func1" in func_names
        assert "func2" in func_names
        assert "method1" in func_names

    def test_invalid_syntax(self):
        """测试无效语法处理"""
        code = '''
def invalid_function(
    # 缺少闭合括号
'''
        analyzer = PythonScriptAnalyzer()
        with pytest.raises(ValueError, match="Invalid Python syntax"):
            analyzer.analyze_code(code)

    def test_analyze_function_by_name(self):
        """测试按名称分析函数"""
        code = '''
def target_function(x: int, y: str = "default") -> str:
    """目标函数"""
    return f"{x}: {y}"

def other_function():
    """其他函数"""
    pass
'''
        analyzer = PythonScriptAnalyzer()
        func = analyzer.analyze_function(code, "target_function")
        
        assert func is not None
        assert func.name == "target_function"
        assert len(func.parameters) == 2
        assert func.return_type_hint == "str"

    def test_analyze_nonexistent_function(self):
        """测试分析不存在的函数"""
        code = '''
def existing_function():
    pass
'''
        analyzer = PythonScriptAnalyzer()
        func = analyzer.analyze_function(code, "nonexistent_function")
        assert func is None


class TestFunctionParameter:
    """测试函数参数类"""

    def test_parameter_to_json_schema_basic_types(self):
        """测试基本类型转换为 JSON Schema"""
        # 字符串类型
        param = FunctionParameter("name", type_hint="str")
        schema = param.to_json_schema()
        assert schema["type"] == "string"
        assert "description" in schema

        # 整数类型
        param = FunctionParameter("age", type_hint="int")
        schema = param.to_json_schema()
        assert schema["type"] == "integer"

        # 布尔类型
        param = FunctionParameter("active", type_hint="bool")
        schema = param.to_json_schema()
        assert schema["type"] == "boolean"

    def test_parameter_to_json_schema_with_default(self):
        """测试带默认值的参数"""
        param = FunctionParameter(
            "count", 
            type_hint="int", 
            default_value=10, 
            has_default=True
        )
        schema = param.to_json_schema()
        assert schema["type"] == "integer"
        assert schema["default"] == 10

    def test_parameter_to_json_schema_complex_types(self):
        """测试复杂类型转换"""
        # List 类型
        param = FunctionParameter("items", type_hint="List[str]")
        schema = param.to_json_schema()
        assert schema["type"] == "array"
        assert schema["items"]["type"] == "string"

        # Optional 类型
        param = FunctionParameter("optional_name", type_hint="Optional[str]")
        schema = param.to_json_schema()
        assert schema["type"] == "string"
        assert schema["nullable"] is True

    def test_parameter_infer_from_default_value(self):
        """测试从默认值推断类型"""
        # 从字符串默认值推断
        param = FunctionParameter("name", default_value="test", has_default=True)
        schema = param.to_json_schema()
        assert schema["type"] == "string"

        # 从整数默认值推断
        param = FunctionParameter("count", default_value=42, has_default=True)
        schema = param.to_json_schema()
        assert schema["type"] == "integer"

        # 从列表默认值推断
        param = FunctionParameter("items", default_value=[], has_default=True)
        schema = param.to_json_schema()
        assert schema["type"] == "array"


class TestFunctionInfo:
    """测试函数信息类"""

    def test_to_input_schema(self):
        """测试生成输入 Schema"""
        func = FunctionInfo(
            name="test_func",
            parameters=[
                FunctionParameter("required_param", type_hint="str"),
                FunctionParameter("optional_param", type_hint="int", default_value=10, has_default=True),
                FunctionParameter("args", parameter_type=ParameterType.VAR_POSITIONAL),
                FunctionParameter("kwargs", parameter_type=ParameterType.VAR_KEYWORD)
            ]
        )
        
        schema = func.to_input_schema()
        assert schema["type"] == "object"
        assert "required_param" in schema["properties"]
        assert "optional_param" in schema["properties"]
        # *args 和 **kwargs 不应该在 properties 中
        assert "args" not in schema["properties"]
        assert "kwargs" not in schema["properties"]
        # 必填参数检查
        assert "required_param" in schema["required"]
        assert "optional_param" not in schema["required"]

    def test_to_output_schema_with_return_type(self):
        """测试带返回类型的输出 Schema"""
        func = FunctionInfo(
            name="test_func",
            parameters=[],
            return_type_hint="Dict[str, Any]"
        )
        
        schema = func.to_output_schema()
        assert schema["type"] == "object"

    def test_to_output_schema_without_return_type(self):
        """测试无返回类型的输出 Schema"""
        func = FunctionInfo(name="test_func", parameters=[])
        
        schema = func.to_output_schema()
        assert schema["type"] == "object"
        assert "result" in schema["properties"]
        assert "success" in schema["properties"]


class TestPythonCodeGenerator:
    """测试 Python 代码生成器"""

    def test_generate_function_template(self):
        """测试生成函数模板"""
        func = FunctionInfo(
            name="example_func",
            parameters=[
                FunctionParameter("name", type_hint="str"),
                FunctionParameter("age", type_hint="int", default_value=25, has_default=True)
            ],
            return_type_hint="str",
            description="示例函数"
        )
        
        generator = PythonCodeGenerator()
        template = generator.generate_function_template(func)
        
        assert "def example_func(name: str, age: int = 25) -> str:" in template
        assert "示例函数" in template
        assert "TODO: 实现函数逻辑" in template

    def test_generate_test_code(self):
        """测试生成测试代码"""
        func = FunctionInfo(
            name="example_func",
            parameters=[
                FunctionParameter("name", type_hint="str")
            ]
        )
        
        generator = PythonCodeGenerator()
        test_code = generator.generate_test_code(func)
        
        assert "def test_example_func():" in test_code
        assert "测试 example_func 函数" in test_code
        assert "TODO: 添加测试用例" in test_code


class TestConvenienceFunctions:
    """测试便捷函数"""

    def test_analyze_python_code(self):
        """测试分析 Python 代码便捷函数"""
        code = '''
def hello(name: str) -> str:
    """问候函数"""
    return f"Hello, {name}!"
'''
        functions = analyze_python_code(code)
        assert len(functions) == 1
        assert functions[0].name == "hello"

    def test_analyze_python_function(self):
        """测试分析指定函数便捷函数"""
        code = '''
def func1():
    pass

def func2(x: int) -> int:
    return x * 2
'''
        func = analyze_python_function(code, "func2")
        assert func is not None
        assert func.name == "func2"
        assert len(func.parameters) == 1

    def test_python_to_schema(self):
        """测试 Python 代码转 Schema 便捷函数"""
        code = '''
def calculate(x: int, y: int = 10) -> int:
    """计算函数"""
    return x + y
'''
        schema = python_to_schema(code, "calculate")
        
        assert "function_info" in schema
        assert "input_schema" in schema
        assert "output_schema" in schema
        
        func_info = schema["function_info"]
        assert func_info["name"] == "calculate"
        assert len(func_info["parameters"]) == 2
        
        input_schema = schema["input_schema"]
        assert input_schema["type"] == "object"
        assert "x" in input_schema["properties"]
        assert "y" in input_schema["properties"]

    def test_python_to_schema_function_not_found(self):
        """测试函数不存在的情况"""
        code = '''
def existing_function():
    pass
'''
        with pytest.raises(ValueError, match="Function 'nonexistent' not found"):
            python_to_schema(code, "nonexistent")


class TestComplexScenarios:
    """测试复杂场景"""

    def test_class_with_methods(self):
        """测试包含类和方法的代码"""
        code = '''
class Calculator:
    """计算器类"""
    
    def __init__(self, precision: int = 2):
        """初始化计算器"""
        self.precision = precision
    
    def add(self, a: float, b: float) -> float:
        """加法运算"""
        return round(a + b, self.precision)
    
    def multiply(self, a: float, b: float) -> float:
        """乘法运算"""
        return round(a * b, self.precision)

def standalone_function():
    """独立函数"""
    pass
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        # 应该找到 4 个函数：__init__, add, multiply, standalone_function
        assert len(functions) == 4
        func_names = [f.name for f in functions]
        assert "__init__" in func_names
        assert "add" in func_names
        assert "multiply" in func_names
        assert "standalone_function" in func_names

    def test_function_with_decorators(self):
        """测试带装饰器的函数"""
        code = '''
from functools import wraps

def my_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    return wrapper

@my_decorator
def decorated_function(x: int) -> int:
    """被装饰的函数"""
    return x * 2
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        # 应该找到 wrapper 和 decorated_function
        func_names = [f.name for f in functions]
        assert "wrapper" in func_names
        assert "decorated_function" in func_names

    def test_nested_functions(self):
        """测试嵌套函数"""
        code = '''
def outer_function(x: int) -> int:
    """外层函数"""
    
    def inner_function(y: int) -> int:
        """内层函数"""
        return y * 2
    
    return inner_function(x)
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        # 应该找到外层和内层函数
        func_names = [f.name for f in functions]
        assert "outer_function" in func_names
        assert "inner_function" in func_names

    def test_function_with_complex_docstring(self):
        """测试复杂文档字符串解析"""
        code = '''
def complex_function(name: str, age: int, active: bool = True) -> dict:
    """
    处理用户信息的复杂函数
    
    这个函数用于处理用户的基本信息，包括姓名、年龄和状态。
    
    Args:
        name (str): 用户的姓名
        age (int): 用户的年龄，必须大于0
        active (bool): 用户是否活跃，默认为True
    
    Returns:
        dict: 包含处理结果的字典
        
    Raises:
        ValueError: 当年龄小于等于0时抛出
    """
    if age <= 0:
        raise ValueError("年龄必须大于0")
    
    return {
        "name": name,
        "age": age,
        "active": active,
        "processed": True
    }
'''
        analyzer = PythonScriptAnalyzer()
        functions = analyzer.analyze_code(code)
        
        assert len(functions) == 1
        func = functions[0]
        assert func.name == "complex_function"
        assert "处理用户信息的复杂函数" in func.description
        
        # 检查参数描述是否被正确解析
        param_names = [p.name for p in func.parameters]
        assert "name" in param_names
        assert "age" in param_names
        assert "active" in param_names


if __name__ == "__main__":
    pytest.main([__file__])