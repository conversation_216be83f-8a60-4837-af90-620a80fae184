import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime

from repositories.tool_repository import ToolRepository
from repositories.query_builder import ToolQueryBuilder, create_search_query

class TestToolRepository:
    """测试工具仓库"""
    
    @pytest.fixture
    def mock_es_client(self):
        """模拟 Elasticsearch 客户端"""
        client = AsyncMock()
        
        # 模拟搜索响应
        client.search.return_value = {
            "hits": {
                "hits": [
                    {
                        "_source": {
                            "toolId": "test.tool",
                            "displayName": "测试工具",
                            "category": "test",
                            "deleteFlag": 0
                        },
                        "_score": 1.0
                    }
                ],
                "total": {"value": 1},
                "max_score": 1.0
            },
            "took": 5
        }
        
        # 模拟获取文档响应
        client.get.return_value = {
            "_source": {
                "toolId": "test.tool",
                "displayName": "测试工具",
                "category": "test",
                "deleteFlag": 0
            }
        }
        
        # 模拟索引响应
        client.index.return_value = {
            "result": "created"
        }
        
        # 模拟更新响应
        client.update.return_value = {
            "result": "updated"
        }
        
        # 模拟删除响应
        client.delete.return_value = {
            "result": "deleted"
        }
        
        # 模拟存在检查
        client.exists.return_value = True
        
        # 模拟计数
        client.count.return_value = {"count": 10}
        
        return client
    
    @pytest.fixture
    def mock_redis_client(self):
        """模拟 Redis 客户端"""
        client = AsyncMock()
        client.get.return_value = None  # 默认缓存未命中
        client.setex.return_value = True
        client.delete.return_value = 1
        client.keys.return_value = []
        return client
    
    @pytest.fixture
    def repository(self, mock_es_client, mock_redis_client):
        """创建工具仓库实例"""
        return ToolRepository(mock_es_client, mock_redis_client)
    
    @pytest.mark.asyncio
    async def test_create_tool(self, repository, mock_es_client):
        """测试创建工具"""
        tool_data = {
            "toolId": "test.tool",
            "displayName": "测试工具",
            "category": "test"
        }
        
        result = await repository.create("test.tool", tool_data)
        
        assert result["success"] is True
        assert result["id"] == "test.tool"
        
        # 验证调用了 ES 索引方法
        mock_es_client.index.assert_called_once()
        call_args = mock_es_client.index.call_args
        assert call_args[1]["index"] == repository.index_name
        assert call_args[1]["id"] == "test.tool"
        
        # 验证添加了时间戳
        indexed_doc = call_args[1]["body"]
        assert "createdTime" in indexed_doc
        assert "updatedTime" in indexed_doc
        assert indexed_doc["deleteFlag"] == 0
    
    @pytest.mark.asyncio
    async def test_get_by_id(self, repository, mock_es_client):
        """测试根据ID获取工具"""
        tool = await repository.get_by_id("test.tool")
        
        assert tool is not None
        assert tool["toolId"] == "test.tool"
        assert tool["displayName"] == "测试工具"
        
        # 验证调用了 ES 获取方法
        mock_es_client.get.assert_called_once_with(
            index=repository.index_name,
            id="test.tool"
        )
    
    @pytest.mark.asyncio
    async def test_get_by_id_not_found(self, repository, mock_es_client):
        """测试获取不存在的工具"""
        # 模拟文档不存在
        mock_es_client.get.side_effect = Exception("not_found")
        
        tool = await repository.get_by_id("nonexistent")
        
        assert tool is None
    
    @pytest.mark.asyncio
    async def test_update_tool(self, repository, mock_es_client):
        """测试更新工具"""
        updates = {"displayName": "更新的工具"}
        
        result = await repository.update("test.tool", updates)
        
        assert result["success"] is True
        assert result["id"] == "test.tool"
        
        # 验证调用了 ES 更新方法
        mock_es_client.update.assert_called_once()
        call_args = mock_es_client.update.call_args
        assert call_args[1]["index"] == repository.index_name
        assert call_args[1]["id"] == "test.tool"
        
        # 验证添加了更新时间
        update_doc = call_args[1]["body"]["doc"]
        assert "updatedTime" in update_doc
    
    @pytest.mark.asyncio
    async def test_soft_delete(self, repository, mock_es_client):
        """测试软删除"""
        result = await repository.delete("test.tool", soft_delete=True)
        
        assert result["success"] is True
        
        # 验证调用了更新方法而不是删除方法
        mock_es_client.update.assert_called_once()
        mock_es_client.delete.assert_not_called()
        
        # 验证设置了删除标记
        call_args = mock_es_client.update.call_args
        update_doc = call_args[1]["body"]["doc"]
        assert update_doc["deleteFlag"] == 1
    
    @pytest.mark.asyncio
    async def test_hard_delete(self, repository, mock_es_client):
        """测试硬删除"""
        result = await repository.delete("test.tool", soft_delete=False)
        
        assert result["success"] is True
        
        # 验证调用了删除方法
        mock_es_client.delete.assert_called_once_with(
            index=repository.index_name,
            id="test.tool"
        )
    
    @pytest.mark.asyncio
    async def test_search_tools(self, repository, mock_es_client):
        """测试搜索工具"""
        result = await repository.search_tools(
            query="测试",
            category="test",
            size=10
        )
        
        assert "hits" in result
        assert "total" in result
        assert len(result["hits"]) == 1
        assert result["hits"][0]["toolId"] == "test.tool"
        
        # 验证调用了搜索方法
        mock_es_client.search.assert_called_once()
        call_args = mock_es_client.search.call_args
        search_body = call_args[1]["body"]
        
        # 验证查询结构
        assert "query" in search_body
        assert "bool" in search_body["query"]
        assert "highlight" in search_body
        assert search_body["size"] == 10
    
    @pytest.mark.asyncio
    async def test_get_categories(self, repository, mock_es_client):
        """测试获取分类"""
        # 模拟聚合响应
        mock_es_client.search.return_value = {
            "aggregations": {
                "categories": {
                    "buckets": [
                        {"key": "test", "doc_count": 5},
                        {"key": "utility", "doc_count": 3}
                    ]
                }
            }
        }
        
        categories = await repository.get_categories()
        
        assert len(categories) == 2
        assert categories[0]["name"] == "test"
        assert categories[0]["count"] == 5
        assert categories[1]["name"] == "utility"
        assert categories[1]["count"] == 3
    
    @pytest.mark.asyncio
    async def test_get_suggestions(self, repository, mock_es_client):
        """测试获取搜索建议"""
        suggestions = await repository.get_suggestions("测试", limit=5)
        
        assert len(suggestions) == 1
        assert suggestions[0]["text"] == "测试工具"
        assert suggestions[0]["type"] == "tool_name"
        assert suggestions[0]["toolId"] == "test.tool"
    
    @pytest.mark.asyncio
    async def test_bulk_create(self, repository, mock_es_client):
        """测试批量创建"""
        # 模拟批量响应
        mock_es_client.bulk.return_value = {
            "items": [
                {"index": {"result": "created"}},
                {"index": {"result": "created"}}
            ]
        }
        
        tools = [
            {"toolId": "tool1", "displayName": "工具1"},
            {"toolId": "tool2", "displayName": "工具2"}
        ]
        
        result = await repository.bulk_create(tools)
        
        assert result["success"] is True
        assert result["created"] == 2
        assert len(result["errors"]) == 0
        
        # 验证调用了批量方法
        mock_es_client.bulk.assert_called_once()

class TestToolQueryBuilder:
    """测试工具查询构建器"""
    
    def test_basic_search_query(self):
        """测试基础搜索查询"""
        builder = ToolQueryBuilder()
        builder.search_text("测试工具")
        
        query = builder.build()
        
        assert "query" in query
        assert "bool" in query["query"]
        assert "multi_match" in query["query"]["bool"]["must"][0]
        
        # 验证默认过滤已删除的工具
        assert {"term": {"deleteFlag": 0}} in query["query"]["bool"]["filter"]
    
    def test_category_filter(self):
        """测试分类筛选"""
        builder = ToolQueryBuilder()
        builder.by_category("test")
        
        query = builder.build()
        
        filters = query["query"]["bool"]["filter"]
        assert {"term": {"category": "test"}} in filters
    
    def test_transport_filter(self):
        """测试传输方式筛选"""
        builder = ToolQueryBuilder()
        builder.by_transport("http")
        
        query = builder.build()
        
        filters = query["query"]["bool"]["filter"]
        assert {"term": {"capabilities": "http"}} in filters
    
    def test_pagination(self):
        """测试分页"""
        builder = ToolQueryBuilder()
        builder.pagination(page=2, page_size=10)
        
        query = builder.build()
        
        assert query["size"] == 10
        assert query["from"] == 10  # (2-1) * 10
    
    def test_sorting(self):
        """测试排序"""
        builder = ToolQueryBuilder()
        builder.sort_by_relevance().sort_by_updated()
        
        query = builder.build()
        
        assert "sort" in query
        assert "_score" in query["sort"]
        assert {"updatedTime": {"order": "desc"}} in query["sort"]
    
    def test_highlights(self):
        """测试高亮"""
        builder = ToolQueryBuilder()
        builder.with_highlights()
        
        query = builder.build()
        
        assert "highlight" in query
        assert "fields" in query["highlight"]
        assert "displayName" in query["highlight"]["fields"]
    
    def test_aggregations(self):
        """测试聚合"""
        builder = ToolQueryBuilder()
        builder.with_category_agg(size=10)
        
        query = builder.build()
        
        assert "aggs" in query
        assert "categories" in query["aggs"]
        assert query["aggs"]["categories"]["terms"]["field"] == "category"
        assert query["aggs"]["categories"]["terms"]["size"] == 10
    
    def test_similar_tools_query(self):
        """测试相似工具查询"""
        tool = {
            "toolId": "original.tool",
            "category": "test",
            "capabilities": ["http", "sync"],
            "content": "测试工具内容"
        }
        
        builder = ToolQueryBuilder()
        builder.similar_to(tool)
        
        query = builder.build()
        
        # 验证有 should 查询
        assert "should" in query["query"]["bool"]
        assert len(query["query"]["bool"]["should"]) > 0
        
        # 验证排除了原工具
        assert "must_not" in query["query"]["bool"]
        assert {"term": {"toolId": "original.tool"}} in query["query"]["bool"]["must_not"]
    
    def test_create_search_query_helper(self):
        """测试快速创建搜索查询辅助函数"""
        query = create_search_query(
            text="测试",
            category="utility",
            transport="http",
            page=2,
            page_size=15
        )
        
        assert "query" in query
        assert query["size"] == 15
        assert query["from"] == 15  # (2-1) * 15
        
        # 验证筛选条件
        filters = query["query"]["bool"]["filter"]
        assert {"term": {"category": "utility"}} in filters
        assert {"term": {"capabilities": "http"}} in filters
        assert {"term": {"deleteFlag": 0}} in filters

if __name__ == "__main__":
    pytest.main([__file__])