"""
Tests for Function Calling API endpoints.
"""

import pytest
import json
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock

from app.models.tool import Tool
from app.models.function_call import FunctionCall


class TestFunctionCallingAPI:
    """Test cases for Function Calling API endpoints."""
    
    def test_get_function_schemas_success(self, client, sample_tool):
        """Test successful retrieval of function schemas."""
        response = client.get("/api/v1/function-calling/function-schema")
        
        assert response.status_code == 200
        schemas = response.json()
        assert len(schemas) == 1
        assert schemas[0]["type"] == "function"
        assert "function" in schemas[0]
        
        function_def = schemas[0]["function"]
        assert function_def["name"] == "test_weather_api"
        assert "description" in function_def
        assert "parameters" in function_def
    
    def test_get_function_schemas_with_filters(self, client, sample_tool, private_tool):
        """Test function schema retrieval with category filter."""
        # Test category filter
        response = client.get("/api/v1/function-calling/function-schema?category=api")
        assert response.status_code == 200
        schemas = response.json()
        assert len(schemas) == 1
        assert schemas[0]["function"]["name"] == "test_weather_api"
        
        # Test non-existent category
        response = client.get("/api/v1/function-calling/function-schema?category=nonexistent")
        assert response.status_code == 200
        schemas = response.json()
        assert len(schemas) == 0
    
    def test_get_single_function_schema_success(self, client, sample_tool):
        """Test successful retrieval of a single function schema."""
        response = client.get(f"/api/v1/function-calling/function-schema/{sample_tool.id}")
        
        assert response.status_code == 200
        schema = response.json()
        assert schema["type"] == "function"
        assert schema["function"]["name"] == "test_weather_api"
    
    def test_get_single_function_schema_not_found(self, client):
        """Test function schema retrieval for non-existent tool."""
        response = client.get("/api/v1/function-calling/function-schema/nonexistent")
        
        assert response.status_code == 404
        assert "Tool not found" in response.json()["detail"]
    
    @patch('app.services.function_calling_service.FunctionCallingService.execute_function_call')
    def test_call_function_success(self, mock_execute, client, sample_tool):
        """Test successful function call execution."""
        # Mock successful execution
        mock_execute.return_value = {
            "success": True,
            "result": {"temperature": 22.5, "description": "Sunny"},
            "execution_time": 0.5,
            "error": None,
            "call_id": "test-call-123"
        }
        
        call_data = {
            "tool_id": sample_tool.id,
            "arguments": {"location": "New York"}
        }
        
        response = client.post("/api/v1/function-calling/call", json=call_data)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert result["result"]["temperature"] == 22.5
        assert "call_id" in result
    
    @patch('app.services.function_calling_service.FunctionCallingService.execute_function_call')
    def test_call_function_failure(self, mock_execute, client, sample_tool):
        """Test function call execution failure."""
        # Mock failed execution
        mock_execute.return_value = {
            "success": False,
            "result": None,
            "execution_time": 0.1,
            "error": "Invalid location parameter",
            "call_id": "test-call-456"
        }
        
        call_data = {
            "tool_id": sample_tool.id,
            "arguments": {"location": ""}  # Invalid empty location
        }
        
        response = client.post("/api/v1/function-calling/call", json=call_data)
        
        assert response.status_code == 200  # API call succeeds, but function execution fails
        result = response.json()
        assert result["success"] is False
        assert result["error"] == "Invalid location parameter"
    
    def test_call_function_tool_not_found(self, client):
        """Test function call for non-existent tool."""
        call_data = {
            "tool_id": "nonexistent",
            "arguments": {"input": "test"}
        }
        
        response = client.post("/api/v1/function-calling/call", json=call_data)
        
        assert response.status_code == 404
        assert "Tool not found" in response.json()["detail"]
    
    def test_call_function_requires_auth(self, client, private_tool):
        """Test function call for tool requiring authentication."""
        call_data = {
            "tool_id": private_tool.id,
            "arguments": {"input": "test"}
        }
        
        response = client.post("/api/v1/function-calling/call", json=call_data)
        
        assert response.status_code == 401
        assert "Authentication required" in response.json()["detail"]
    
    def test_get_function_call_stats_success(self, client, sample_tool, sample_function_call):
        """Test successful retrieval of function call statistics."""
        response = client.get(f"/api/v1/function-calling/stats/{sample_tool.id}")
        
        assert response.status_code == 200
        stats = response.json()
        assert stats["tool_id"] == sample_tool.id
        assert stats["total_calls"] >= 1
        assert "success_rate" in stats
        assert "avg_execution_time" in stats
    
    def test_get_function_call_stats_not_found(self, client):
        """Test function call stats for non-existent tool."""
        response = client.get("/api/v1/function-calling/stats/nonexistent")
        
        assert response.status_code == 404
        assert "Tool not found" in response.json()["detail"]
    
    def test_get_all_function_call_stats(self, client, sample_tool, sample_function_call):
        """Test retrieval of all function call statistics."""
        response = client.get("/api/v1/function-calling/stats")
        
        assert response.status_code == 200
        stats_list = response.json()
        assert isinstance(stats_list, list)
        assert len(stats_list) >= 1
        
        # Check if our sample tool is in the stats
        tool_stats = next((s for s in stats_list if s["tool_id"] == sample_tool.id), None)
        assert tool_stats is not None
        assert tool_stats["total_calls"] >= 1
    
    def test_get_all_function_call_stats_with_pagination(self, client, sample_tool):
        """Test function call stats with pagination parameters."""
        response = client.get("/api/v1/function-calling/stats?limit=10&offset=0")
        
        assert response.status_code == 200
        stats_list = response.json()
        assert isinstance(stats_list, list)
        assert len(stats_list) <= 10
    
    def test_reset_function_call_stats_success(self, client, sample_tool, sample_user):
        """Test successful reset of function call statistics."""
        # This would require authentication in a real scenario
        # For now, we'll test the endpoint structure
        response = client.delete(f"/api/v1/function-calling/stats/{sample_tool.id}")
        
        # Without proper auth, this should fail
        assert response.status_code in [401, 403]
    
    def test_reset_function_call_stats_not_found(self, client):
        """Test reset stats for non-existent tool."""
        response = client.delete("/api/v1/function-calling/stats/nonexistent")
        
        assert response.status_code in [401, 404]  # Auth or not found
    
    def test_function_calling_health_check(self, client):
        """Test function calling service health check."""
        response = client.get("/api/v1/function-calling/health")
        
        assert response.status_code == 200
        health = response.json()
        assert health["status"] == "healthy"
        assert health["service"] == "function_calling"
        assert "version" in health


class TestFunctionSchemaGeneration:
    """Test cases for function schema generation."""
    
    def test_tool_to_function_schema_basic(self, sample_tool):
        """Test basic tool to function schema conversion."""
        from app.services.function_calling_service import FunctionCallingService
        
        schema = FunctionCallingService.tool_to_function_schema(sample_tool)
        
        assert schema["name"] == "test_weather_api"
        assert schema["description"] == sample_tool.description
        assert "parameters" in schema
        assert schema["parameters"]["type"] == "object"
        assert "properties" in schema["parameters"]
        assert "location" in schema["parameters"]["properties"]
    
    def test_tool_to_function_schema_with_metadata(self, sample_tool):
        """Test function schema generation includes metadata."""
        from app.services.function_calling_service import FunctionCallingService
        
        schema = FunctionCallingService.tool_to_function_schema(sample_tool)
        
        assert "metadata" in schema
        metadata = schema["metadata"]
        assert metadata["tool_id"] == sample_tool.id
        assert metadata["category"] == sample_tool.category
        assert metadata["transport"] == sample_tool.transport
        assert metadata["requires_auth"] == sample_tool.requires_auth
    
    def test_tool_to_function_schema_invalid_json(self, db_session, sample_user):
        """Test function schema generation with invalid JSON schema."""
        from app.services.function_calling_service import FunctionCallingService
        
        # Create tool with invalid JSON schema
        tool = Tool(
            id="invalid-tool",
            name="Invalid Tool",
            description="Tool with invalid schema",
            category="test",
            user_id=sample_user.id,
            input_schema="invalid json"
        )
        
        schema = FunctionCallingService.tool_to_function_schema(tool)
        
        # Should return basic schema with error metadata
        assert schema["name"] == "invalid_tool"
        assert "metadata" in schema
        assert "error" in schema["metadata"]


class TestFunctionCallExecution:
    """Test cases for function call execution."""
    
    @patch('app.services.execution_service.ExecutionService.execute_tool')
    async def test_execute_function_call_success(self, mock_execute, sample_tool):
        """Test successful function call execution."""
        from app.services.function_calling_service import FunctionCallingService
        
        # Mock successful execution
        mock_execute.return_value = {
            "success": True,
            "result": {"temperature": 22.5}
        }
        
        result = await FunctionCallingService.execute_function_call(
            tool=sample_tool,
            arguments={"location": "New York"}
        )
        
        assert result["success"] is True
        assert result["result"]["temperature"] == 22.5
        assert "execution_time" in result
        assert "call_id" in result
    
    @patch('app.services.execution_service.ExecutionService.execute_tool')
    async def test_execute_function_call_validation_error(self, mock_execute, sample_tool):
        """Test function call execution with validation error."""
        from app.services.function_calling_service import FunctionCallingService
        
        # Don't need to mock execute since validation should fail first
        result = await FunctionCallingService.execute_function_call(
            tool=sample_tool,
            arguments={}  # Missing required 'location' parameter
        )
        
        assert result["success"] is False
        assert "Invalid arguments" in result["error"]
        assert "Missing required field: location" in result["error"]
    
    async def test_validate_arguments_success(self, sample_tool):
        """Test successful argument validation."""
        from app.services.function_calling_service import FunctionCallingService
        
        result = await FunctionCallingService._validate_arguments(
            sample_tool,
            {"location": "New York"}
        )
        
        assert result["valid"] is True
    
    async def test_validate_arguments_missing_required(self, sample_tool):
        """Test argument validation with missing required field."""
        from app.services.function_calling_service import FunctionCallingService
        
        result = await FunctionCallingService._validate_arguments(
            sample_tool,
            {}  # Missing required 'location'
        )
        
        assert result["valid"] is False
        assert "Missing required field: location" in result["error"]
    
    async def test_validate_arguments_wrong_type(self, sample_tool):
        """Test argument validation with wrong type."""
        from app.services.function_calling_service import FunctionCallingService
        
        result = await FunctionCallingService._validate_arguments(
            sample_tool,
            {"location": 123}  # Should be string, not number
        )
        
        assert result["valid"] is False
        assert "must be a string" in result["error"]
