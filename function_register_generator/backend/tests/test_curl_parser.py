import pytest
import json
from parsers.curl_parser import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CurlSchemaInferrer, HttpMethod, 
    parse_curl, curl_to_schema
)

class TestCurlParser:
    """测试 cURL 解析器"""
    
    def test_simple_get_request(self):
        """测试简单的 GET 请求"""
        curl = "curl https://api.example.com/users"
        
        result = parse_curl(curl)
        
        assert result.url == "https://api.example.com/users"
        assert result.method == HttpMethod.GET
        assert result.headers == {}
        assert result.body is None
    
    def test_get_with_query_params(self):
        """测试带查询参数的 GET 请求"""
        curl = "curl 'https://api.example.com/users?page=1&limit=10'"
        
        result = parse_curl(curl)
        
        assert result.url == "https://api.example.com/users"
        assert result.query_params == {"page": "1", "limit": "10"}
    
    def test_post_with_json_data(self):
        """测试带 JSON 数据的 POST 请求"""
        curl = '''curl -X POST https://api.example.com/users \
                  -H "Content-Type: application/json" \
                  -d '{"name": "<PERSON>", "age": 30}' '''
        
        result = parse_curl(curl)
        
        assert result.method == HttpMethod.POST
        assert result.url == "https://api.example.com/users"
        assert result.headers["Content-Type"] == "application/json"
        assert result.body == {"name": "John", "age": 30}
        assert result.body_type == "json"
    
    def test_post_with_form_data(self):
        """测试带表单数据的 POST 请求"""
        curl = "curl -X POST https://api.example.com/login -d 'username=admin&password=secret'"
        
        result = parse_curl(curl)
        
        assert result.method == HttpMethod.POST
        assert result.body == {"username": "admin", "password": "secret"}
        assert result.body_type == "form"
    
    def test_headers_parsing(self):
        """测试请求头解析"""
        curl = '''curl https://api.example.com/data \
                  -H "Authorization: Bearer token123" \
                  -H "User-Agent: MyApp/1.0" \
                  -H "Accept: application/json" '''
        
        result = parse_curl(curl)
        
        assert result.headers["Authorization"] == "Bearer token123"
        assert result.headers["User-Agent"] == "MyApp/1.0"
        assert result.headers["Accept"] == "application/json"
    
    def test_basic_auth(self):
        """测试基础认证"""
        curl = "curl -u admin:password https://api.example.com/secure"
        
        result = parse_curl(curl)
        
        assert result.auth["type"] == "basic"
        assert result.auth["username"] == "admin"
        assert result.auth["password"] == "password"
    
    def test_cookies(self):
        """测试 Cookie 处理"""
        curl = "curl -b 'session=abc123; user=john' https://api.example.com/profile"
        
        result = parse_curl(curl)
        
        assert result.cookies["session"] == "abc123"
        assert result.cookies["user"] == "john"
    
    def test_long_options(self):
        """测试长选项格式"""
        curl = '''curl --request POST \
                  --url https://api.example.com/data \
                  --header "Content-Type: application/json" \
                  --data '{"test": true}' \
                  --user-agent "TestAgent/1.0" '''
        
        result = parse_curl(curl)
        
        assert result.method == HttpMethod.POST
        assert result.url == "https://api.example.com/data"
        assert result.headers["Content-Type"] == "application/json"
        assert result.body == {"test": True}
        assert result.user_agent == "TestAgent/1.0"
    
    def test_complex_real_world_example(self):
        """测试复杂的真实世界示例"""
        curl = '''curl -X POST 'https://api.github.com/repos/owner/repo/issues' \
                  -H 'Accept: application/vnd.github.v3+json' \
                  -H 'Authorization: token ghp_xxxxxxxxxxxxxxxxxxxx' \
                  -H 'Content-Type: application/json' \
                  -d '{
                    "title": "Bug report",
                    "body": "Something is broken",
                    "labels": ["bug", "urgent"],
                    "assignees": ["username"]
                  }' '''
        
        result = parse_curl(curl)
        
        assert result.method == HttpMethod.POST
        assert result.url == "https://api.github.com/repos/owner/repo/issues"
        assert result.headers["Accept"] == "application/vnd.github.v3+json"
        assert result.headers["Authorization"] == "token ghp_xxxxxxxxxxxxxxxxxxxx"
        assert result.body["title"] == "Bug report"
        assert result.body["labels"] == ["bug", "urgent"]
    
    def test_url_without_protocol(self):
        """测试没有协议的 URL"""
        curl = "curl api.example.com/users"
        
        result = parse_curl(curl)
        
        assert result.url == "https://api.example.com/users"
    
    def test_multiline_curl(self):
        """测试多行 cURL 命令"""
        curl = """curl -X POST \\
                  https://api.example.com/data \\
                  -H "Content-Type: application/json" \\
                  -d '{"key": "value"}'"""
        
        result = parse_curl(curl)
        
        assert result.method == HttpMethod.POST
        assert result.url == "https://api.example.com/data"
        assert result.body == {"key": "value"}
    
    def test_special_options(self):
        """测试特殊选项"""
        curl = "curl -L -k -s https://api.example.com/data"
        
        result = parse_curl(curl)
        
        assert result.follow_redirects is True
        assert result.verify_ssl is False
    
    def test_timeout_options(self):
        """测试超时选项"""
        curl = "curl --connect-timeout 30 --max-time 60 https://api.example.com/data"
        
        result = parse_curl(curl)
        
        assert result.timeout == 60  # max-time 优先级更高

class TestCurlSchemaInferrer:
    """测试 cURL Schema 推断器"""
    
    def test_infer_from_query_params(self):
        """测试从查询参数推断 Schema"""
        curl = "curl 'https://api.example.com/search?q=python&page=1&limit=10'"
        
        result = parse_curl(curl)
        inferrer = CurlSchemaInferrer()
        schema = inferrer.infer_input_schema(result)
        
        assert schema["type"] == "object"
        assert "q" in schema["properties"]
        assert "page" in schema["properties"]
        assert "limit" in schema["properties"]
        
        assert schema["properties"]["q"]["type"] == "string"
        assert schema["properties"]["page"]["type"] == "integer"
        assert schema["properties"]["limit"]["type"] == "integer"
    
    def test_infer_from_json_body(self):
        """测试从 JSON 请求体推断 Schema"""
        curl = '''curl -X POST https://api.example.com/users \
                  -H "Content-Type: application/json" \
                  -d '{"name": "John", "age": 30, "active": true}' '''
        
        result = parse_curl(curl)
        inferrer = CurlSchemaInferrer()
        schema = inferrer.infer_input_schema(result)
        
        assert "name" in schema["properties"]
        assert "age" in schema["properties"]
        assert "active" in schema["properties"]
        
        assert schema["properties"]["name"]["type"] == "string"
        assert schema["properties"]["age"]["type"] == "integer"
        assert schema["properties"]["active"]["type"] == "boolean"
        
        assert "name" in schema["required"]
        assert "age" in schema["required"]
        assert "active" in schema["required"]
    
    def test_infer_from_form_data(self):
        """测试从表单数据推断 Schema"""
        curl = "curl -X POST https://api.example.com/login -d 'username=admin&password=secret&remember=true'"
        
        result = parse_curl(curl)
        inferrer = CurlSchemaInferrer()
        schema = inferrer.infer_input_schema(result)
        
        assert "username" in schema["properties"]
        assert "password" in schema["properties"]
        assert "remember" in schema["properties"]
        
        assert schema["properties"]["remember"]["type"] == "boolean"
    
    def test_infer_path_variables(self):
        """测试路径变量推断"""
        curl = "curl https://api.example.com/users/{userId}/posts/{postId}"
        
        result = parse_curl(curl)
        inferrer = CurlSchemaInferrer()
        schema = inferrer.infer_input_schema(result)
        
        assert "userId" in schema["properties"]
        assert "postId" in schema["properties"]
        assert "userId" in schema["required"]
        assert "postId" in schema["required"]
    
    def test_infer_output_schema_for_user_endpoint(self):
        """测试用户端点的输出 Schema 推断"""
        curl = "curl https://api.example.com/users/123"
        
        result = parse_curl(curl)
        inferrer = CurlSchemaInferrer()
        schema = inferrer.infer_output_schema(result)
        
        assert "user" in schema["properties"]
        assert "status" in schema["properties"]
    
    def test_infer_output_schema_for_list_endpoint(self):
        """测试列表端点的输出 Schema 推断"""
        curl = "curl https://api.example.com/users/search?q=john"
        
        result = parse_curl(curl)
        inferrer = CurlSchemaInferrer()
        schema = inferrer.infer_output_schema(result)
        
        assert "data" in schema["properties"]
        assert "total" in schema["properties"]
        assert schema["properties"]["data"]["type"] == "array"
    
    def test_infer_output_schema_for_post_request(self):
        """测试 POST 请求的输出 Schema 推断"""
        curl = "curl -X POST https://api.example.com/users -d '{\"name\": \"John\"}'"
        
        result = parse_curl(curl)
        inferrer = CurlSchemaInferrer()
        schema = inferrer.infer_output_schema(result)
        
        assert "id" in schema["properties"]
        assert "message" in schema["properties"]

class TestConvenienceFunctions:
    """测试便捷函数"""
    
    def test_curl_to_schema_function(self):
        """测试 curl_to_schema 便捷函数"""
        curl = '''curl -X POST https://api.example.com/users \
                  -H "Content-Type: application/json" \
                  -d '{"name": "John", "email": "<EMAIL>"}' '''
        
        result = curl_to_schema(curl)
        
        assert "parse_result" in result
        assert "input_schema" in result
        assert "output_schema" in result
        
        # 检查解析结果
        parse_result = result["parse_result"]
        assert parse_result["method"] == "POST"
        assert parse_result["url"] == "https://api.example.com/users"
        
        # 检查输入 Schema
        input_schema = result["input_schema"]
        assert "name" in input_schema["properties"]
        assert "email" in input_schema["properties"]
        
        # 检查输出 Schema
        output_schema = result["output_schema"]
        assert "id" in output_schema["properties"]
        assert "message" in output_schema["properties"]

class TestEdgeCases:
    """测试边界情况"""
    
    def test_empty_curl_command(self):
        """测试空的 cURL 命令"""
        with pytest.raises(ValueError):
            parse_curl("")
    
    def test_invalid_curl_command(self):
        """测试无效的 cURL 命令"""
        with pytest.raises(ValueError):
            parse_curl("not a curl command")
    
    def test_curl_with_file_reference(self):
        """测试文件引用"""
        curl = "curl -X POST https://api.example.com/upload -d @data.json"
        
        result = parse_curl(curl)
        
        assert result.body == "@data.json"
        assert result.body_type == "raw"
    
    def test_curl_with_malformed_json(self):
        """测试格式错误的 JSON"""
        curl = "curl -X POST https://api.example.com/data -d '{invalid json}'"
        
        result = parse_curl(curl)
        
        assert result.body == "{invalid json}"
        assert result.body_type == "raw"
    
    def test_curl_with_special_characters(self):
        """测试特殊字符处理"""
        curl = '''curl -X POST https://api.example.com/data \
                  -d '{"message": "Hello, 世界! 🌍"}' '''
        
        result = parse_curl(curl)
        
        assert result.body["message"] == "Hello, 世界! 🌍"
    
    def test_curl_without_curl_prefix(self):
        """测试没有 curl 前缀的命令"""
        curl = "https://api.example.com/users -H 'Accept: application/json'"
        
        result = parse_curl(curl)
        
        assert result.url == "https://api.example.com/users"
        assert result.headers["Accept"] == "application/json"

if __name__ == "__main__":
    pytest.main([__file__])