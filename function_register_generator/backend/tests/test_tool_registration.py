"""
工具注册端点测试
"""
import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch, MagicMock

# 测试数据
SAMPLE_TOOL_REGISTRATION = {
    "displayName": "测试工具",
    "category": "utility",
    "transport": "http",
    "endpoint": "https://api.example.com/test",
    "httpMethod": "GET",
    "description": "这是一个测试工具"
}

SAMPLE_CURL_REGISTRATION = {
    "displayName": "cURL 测试工具",
    "curl": "curl -X GET 'https://api.example.com/data' -H 'Authorization: Bearer token'",
    "description": "通过 cURL 注册的测试工具"
}

SAMPLE_PYTHON_REGISTRATION = {
    "displayName": "Python 测试工具",
    "script_content": """
def add_numbers(a: int, b: int) -> int:
    '''添加两个数字'''
    return a + b
""",
    "entry_function": "add_numbers",
    "description": "Python 加法工具"
}

SAMPLE_MCP_REGISTRATION = {
    "transport": "stdio",
    "command": "uvx",
    "args": ["mcp-server-example"],
    "env": {"DEBUG": "1"}
}

SAMPLE_NATURAL_REGISTRATION = {
    "description": "创建一个可以获取天气信息的工具",
    "test_inputs": {"city": "北京"},
    "test_outputs": {"weather": "晴天", "temperature": "25°C"},
    "category": "weather"
}

class TestToolRegistration:
    """工具注册测试类"""
    
    @pytest.fixture
    def mock_repository(self):
        """模拟工具仓库"""
        repository = AsyncMock()
        repository.create = AsyncMock(return_value=True)
        repository.get_by_id = AsyncMock(return_value=None)
        repository.list_all = AsyncMock(return_value=[])
        repository.search = AsyncMock(return_value=[])
        repository.get_categories = AsyncMock(return_value=["utility", "api", "script"])
        return repository
    
    @pytest.fixture
    def mock_curl_parser(self):
        """模拟 cURL 解析器"""
        return {
            "url": "https://api.example.com/data",
            "method": "GET",
            "headers": {"Authorization": "Bearer token"}
        }
    
    @pytest.fixture
    def mock_python_parser(self):
        """模拟 Python 解析器"""
        return {
            "function_name": "add_numbers",
            "signature": "add_numbers(a: int, b: int) -> int",
            "parameters": [
                {"name": "a", "type": "int", "required": True},
                {"name": "b", "type": "int", "required": True}
            ],
            "return_type": "int",
            "docstring": "添加两个数字"
        }
    
    @pytest.fixture
    def mock_mcp_validation(self):
        """模拟 MCP 验证结果"""
        mock_tool = MagicMock()
        mock_tool.name = "test_tool"
        mock_tool.description = "测试工具"
        mock_tool.inputSchema = {"type": "object", "properties": {}}
        
        mock_result = MagicMock()
        mock_result.success = True
        mock_result.available_tools = [mock_tool]
        mock_result.server_info = MagicMock()
        mock_result.error = None
        
        return mock_result
    
    @pytest.fixture
    def mock_completion_result(self):
        """模拟智能补全结果"""
        mock_result = MagicMock()
        mock_result.success = True
        mock_result.tool_definition = {
            "toolId": "weather.get_weather",
            "displayName": "天气查询工具",
            "category": "weather",
            "transport": "http",
            "runtime": {"transport": "http", "endpoint": "https://api.weather.com"},
            "inputsDeveloperSchema": {"type": "object", "properties": {"city": {"type": "string"}}},
            "outputsSchema": {"type": "object", "properties": {"weather": {"type": "string"}}},
            "descriptionUser": "获取天气信息",
            "descriptionDev": "调用天气 API 获取指定城市的天气信息"
        }
        mock_result.suggestions = ["建议添加错误处理"]
        mock_result.confidence = 0.9
        mock_result.error = None
        
        return mock_result
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_basic_tool_registration(self, mock_get_repo, mock_repository):
        """测试基础工具注册"""
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import register_tool, ToolRegistrationRequest
        
        request = ToolRegistrationRequest(**SAMPLE_TOOL_REGISTRATION)
        result = await register_tool(request)
        
        assert result["success"] is True
        assert result["tool_id"] == "http.测试工具"
        assert "tool_definition" in result
        
        # 验证仓库调用
        mock_repository.create.assert_called_once()
        call_args = mock_repository.create.call_args[0][0]
        assert call_args["displayName"] == "测试工具"
        assert call_args["transport"] == "http"
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.parse_curl')
    async def test_curl_tool_registration(self, mock_parse_curl, mock_get_repo, 
                                        mock_repository, mock_curl_parser):
        """测试 cURL 工具注册"""
        mock_get_repo.return_value = mock_repository
        mock_parse_curl.return_value = mock_curl_parser
        
        from backend.routers.tools import register_curl_tool, CurlRegistrationRequest
        
        request = CurlRegistrationRequest(**SAMPLE_CURL_REGISTRATION)
        result = await register_curl_tool(request)
        
        assert result["success"] is True
        assert result["tool_id"] == "http.curl_测试工具"
        assert "parsed_curl" in result
        
        # 验证 cURL 解析调用
        mock_parse_curl.assert_called_once_with(SAMPLE_CURL_REGISTRATION["curl"])
        
        # 验证仓库调用
        mock_repository.create.assert_called_once()
        call_args = mock_repository.create.call_args[0][0]
        assert call_args["runtime"]["curl_command"] == SAMPLE_CURL_REGISTRATION["curl"]
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.parse_python_function')
    async def test_python_tool_registration(self, mock_parse_python, mock_get_repo,
                                          mock_repository, mock_python_parser):
        """测试 Python 工具注册"""
        mock_get_repo.return_value = mock_repository
        mock_parse_python.return_value = mock_python_parser
        
        from backend.routers.tools import register_python_tool, PythonRegistrationRequest
        
        request = PythonRegistrationRequest(**SAMPLE_PYTHON_REGISTRATION)
        result = await register_python_tool(request)
        
        assert result["success"] is True
        assert result["tool_id"] == "python.python_测试工具"
        assert "function_info" in result
        
        # 验证 Python 解析调用
        mock_parse_python.assert_called_once_with(
            SAMPLE_PYTHON_REGISTRATION["script_content"],
            SAMPLE_PYTHON_REGISTRATION["entry_function"]
        )
        
        # 验证仓库调用
        mock_repository.create.assert_called_once()
        call_args = mock_repository.create.call_args[0][0]
        assert call_args["runtime"]["entry_function"] == "add_numbers"
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.get_mcp_sandbox_service')
    async def test_mcp_tool_registration(self, mock_get_sandbox, mock_get_repo,
                                       mock_repository, mock_mcp_validation):
        """测试 MCP 工具注册"""
        mock_get_repo.return_value = mock_repository
        
        mock_sandbox = AsyncMock()
        mock_sandbox.validate_mcp_server = AsyncMock(return_value=mock_mcp_validation)
        mock_get_sandbox.return_value = mock_sandbox
        
        from backend.routers.tools import register_mcp_tools, MCPImportRequest
        
        request = MCPImportRequest(**SAMPLE_MCP_REGISTRATION)
        result = await register_mcp_tools(request)
        
        assert result["success"] is True
        assert len(result["registered_tools"]) == 1
        assert result["registered_tools"][0]["toolId"] == "mcp.test_tool"
        
        # 验证 MCP 验证调用
        mock_sandbox.validate_mcp_server.assert_called_once()
        
        # 验证仓库调用
        mock_repository.create.assert_called_once()
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.get_intelligent_completion_service')
    async def test_natural_language_registration(self, mock_get_completion, mock_get_repo,
                                                mock_repository, mock_completion_result):
        """测试自然语言工具注册"""
        mock_get_repo.return_value = mock_repository
        
        mock_completion_service = AsyncMock()
        mock_completion_service.complete_tool_definition = AsyncMock(return_value=mock_completion_result)
        mock_get_completion.return_value = mock_completion_service
        
        from backend.routers.tools import register_natural_language_tool, NaturalLanguageToolRequest
        
        request = NaturalLanguageToolRequest(**SAMPLE_NATURAL_REGISTRATION)
        result = await register_natural_language_tool(request)
        
        assert result["success"] is True
        assert result["tool_id"] == "weather.get_weather"
        assert "suggestions" in result
        assert "confidence" in result
        
        # 验证智能补全调用
        mock_completion_service.complete_tool_definition.assert_called_once()
        
        # 验证仓库调用
        mock_repository.create.assert_called_once()
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_list_tools(self, mock_get_repo, mock_repository):
        """测试工具列表获取"""
        mock_tools = [
            {"toolId": "test1", "displayName": "测试工具1"},
            {"toolId": "test2", "displayName": "测试工具2"}
        ]
        mock_repository.list_all = AsyncMock(return_value=mock_tools)
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import list_tools
        
        result = await list_tools()
        
        assert len(result["tools"]) == 2
        assert result["total"] == 2
        
        # 验证仓库调用
        mock_repository.list_all.assert_called_once()
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_get_tool_by_id(self, mock_get_repo, mock_repository):
        """测试根据ID获取工具"""
        mock_tool = {"toolId": "test1", "displayName": "测试工具1"}
        mock_repository.get_by_id = AsyncMock(return_value=mock_tool)
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import get_tool
        
        result = await get_tool("test1")
        
        assert result["toolId"] == "test1"
        assert result["displayName"] == "测试工具1"
        
        # 验证仓库调用
        mock_repository.get_by_id.assert_called_once_with("test1")
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_update_tool(self, mock_get_repo, mock_repository):
        """测试工具更新"""
        mock_existing_tool = {"toolId": "test1", "displayName": "旧名称"}
        mock_updated_tool = {"toolId": "test1", "displayName": "新名称"}
        
        mock_repository.get_by_id = AsyncMock(return_value=mock_existing_tool)
        mock_repository.update = AsyncMock(return_value=mock_updated_tool)
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import update_tool
        
        updates = {"displayName": "新名称"}
        result = await update_tool("test1", updates)
        
        assert result["success"] is True
        assert result["tool"]["displayName"] == "新名称"
        
        # 验证仓库调用
        mock_repository.get_by_id.assert_called_once_with("test1")
        mock_repository.update.assert_called_once()
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_delete_tool(self, mock_get_repo, mock_repository):
        """测试工具删除"""
        mock_existing_tool = {"toolId": "test1", "displayName": "测试工具"}
        
        mock_repository.get_by_id = AsyncMock(return_value=mock_existing_tool)
        mock_repository.delete = AsyncMock(return_value=True)
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import delete_tool
        
        result = await delete_tool("test1")
        
        assert result["success"] is True
        assert result["message"] == "工具删除成功"
        
        # 验证仓库调用
        mock_repository.get_by_id.assert_called_once_with("test1")
        mock_repository.delete.assert_called_once_with("test1")

if __name__ == "__main__":
    pytest.main([__file__])