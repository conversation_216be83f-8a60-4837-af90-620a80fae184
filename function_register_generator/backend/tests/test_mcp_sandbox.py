import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch

from services.mcp_sandbox_service import (
    MCPSandboxService,
    MCPSandboxRequest,
    MCPSandboxConfig,
    MCPValidationResult,
    MCPServerInfo,
    MCPToolInfo,
    get_mcp_sandbox_service
)


class TestMCPSandboxRequest:
    """测试 MCP 沙盒请求模型"""
    
    def test_valid_stdio_request(self):
        """测试有效的 stdio 请求"""
        request = MCPSandboxRequest(
            transport="stdio",
            command="uvx",
            args=["mcp-server-example"],
            timeout=30
        )
        
        assert request.transport == "stdio"
        assert request.command == "uvx"
        assert request.args == ["mcp-server-example"]
        assert request.timeout == 30
    
    def test_valid_http_request(self):
        """测试有效的 http 请求"""
        request = MCPSandboxRequest(
            transport="http",
            url="http://localhost:3000/mcp",
            timeout=30
        )
        
        assert request.transport == "http"
        assert request.url == "http://localhost:3000/mcp"
        assert request.timeout == 30
    
    def test_transport_validation(self):
        """测试传输方式验证"""
        # 有效传输方式
        for transport in ["stdio", "http"]:
            request = MCPSandboxRequest(transport=transport)
            assert request.transport == transport
        
        # 无效传输方式
        with pytest.raises(ValueError, match="不支持的传输方式"):
            MCPSandboxRequest(transport="invalid")
    
    def test_timeout_validation(self):
        """测试超时验证"""
        # 有效超时
        request = MCPSandboxRequest(transport="stdio", timeout=30)
        assert request.timeout == 30
        
        # 无效超时
        with pytest.raises(ValueError, match="超时时间必须在"):
            MCPSandboxRequest(transport="stdio", timeout=0)
        
        with pytest.raises(ValueError, match="超时时间必须在"):
            MCPSandboxRequest(transport="stdio", timeout=200)
    
    def test_command_validation(self):
        """测试命令验证"""
        # 有效命令
        valid_commands = ["uvx", "npx", "python", "/usr/bin/node", "./mcp-server"]
        for cmd in valid_commands:
            request = MCPSandboxRequest(transport="stdio", command=cmd)
            assert request.command == cmd
        
        # 无效命令
        invalid_commands = ["rm", "sudo", "curl"]
        for cmd in invalid_commands:
            with pytest.raises(ValueError):
                MCPSandboxRequest(transport="stdio", command=cmd)
    
    def test_url_validation(self):
        """测试 URL 验证"""
        # 有效 URL
        valid_urls = [
            "http://localhost:3000",
            "https://api.example.com/mcp",
            "http://127.0.0.1:8080/mcp"
        ]
        for url in valid_urls:
            request = MCPSandboxRequest(transport="http", url=url)
            assert request.url == url
        
        # 无效 URL
        invalid_urls = ["not-a-url", "ftp://example.com", ""]
        for url in invalid_urls:
            with pytest.raises(ValueError):
                MCPSandboxRequest(transport="http", url=url)


class TestMCPSandboxService:
    """测试 MCP 沙盒服务"""
    
    @pytest.fixture
    def sandbox_service(self):
        """创建沙盒服务实例"""
        return MCPSandboxService()
    
    def test_security_check_stdio_safe(self, sandbox_service):
        """测试 stdio 安全命令检查"""
        safe_request = MCPSandboxRequest(
            transport="stdio",
            command="uvx",
            args=["mcp-server-example"]
        )
        
        warnings = sandbox_service._security_check(safe_request)
        assert len(warnings) == 0
    
    def test_security_check_stdio_dangerous(self, sandbox_service):
        """测试 stdio 危险命令检查"""
        # 这应该在请求验证阶段就失败
        with pytest.raises(ValueError, match="不允许的命令前缀"):
            MCPSandboxRequest(
                transport="stdio",
                command="rm",
                args=["-rf", "/"]
            )
    
    def test_security_check_http_safe(self, sandbox_service):
        """测试 http 安全 URL 检查"""
        safe_request = MCPSandboxRequest(
            transport="http",
            url="https://api.example.com/mcp"
        )
        
        warnings = sandbox_service._security_check(safe_request)
        # 可能有一些警告，但不应该阻止连接
        assert isinstance(warnings, list)
    
    def test_security_check_http_local(self, sandbox_service):
        """测试 http 本地地址检查"""
        local_request = MCPSandboxRequest(
            transport="http",
            url="http://localhost:3000/mcp"
        )
        
        warnings = sandbox_service._security_check(local_request)
        assert len(warnings) > 0
        assert any("本地地址" in warning for warning in warnings)
    
    def test_parse_server_info(self, sandbox_service):
        """测试服务器信息解析"""
        response = {
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "serverInfo": {
                    "name": "example-server",
                    "version": "1.0.0"
                }
            }
        }
        
        server_info = sandbox_service._parse_server_info(response)
        
        assert server_info.protocol_version == "2024-11-05"
        assert server_info.capabilities == {"tools": {}}
        assert server_info.server_info == {"name": "example-server", "version": "1.0.0"}
    
    def test_parse_tools_list(self, sandbox_service):
        """测试工具列表解析"""
        response = {
            "result": {
                "tools": [
                    {
                        "name": "get_weather",
                        "description": "Get weather information",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "location": {"type": "string"}
                            }
                        }
                    },
                    {
                        "name": "calculate",
                        "description": "Perform calculations",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "expression": {"type": "string"}
                            }
                        }
                    }
                ]
            }
        }
        
        tools = sandbox_service._parse_tools_list(response)
        
        assert len(tools) == 2
        assert tools[0].name == "get_weather"
        assert tools[0].description == "Get weather information"
        assert tools[1].name == "calculate"
        assert tools[1].description == "Perform calculations"
    
    def test_generate_quickstart_stdio(self, sandbox_service):
        """测试 stdio 快速开始代码生成"""
        request = MCPSandboxRequest(
            transport="stdio",
            command="uvx",
            args=["mcp-server-example"]
        )
        
        result = MCPValidationResult(
            success=True,
            transport="stdio"
        )
        
        quickstart = sandbox_service._generate_quickstart(request, result)
        
        assert "command" in quickstart
        assert "uvx mcp-server-example" in quickstart["command"]
        assert "mcp_init" in quickstart
        assert "mcp_tools" in quickstart
        assert "initialize" in quickstart["mcp_init"]
        assert "tools/list" in quickstart["mcp_tools"]
    
    def test_generate_quickstart_http(self, sandbox_service):
        """测试 http 快速开始代码生成"""
        request = MCPSandboxRequest(
            transport="http",
            url="http://localhost:3000/mcp"
        )
        
        result = MCPValidationResult(
            success=True,
            transport="http"
        )
        
        quickstart = sandbox_service._generate_quickstart(request, result)
        
        assert "curl" in quickstart
        assert "python" in quickstart
        assert "http://localhost:3000/mcp" in quickstart["curl"]
        assert "requests.post" in quickstart["python"]
    
    @pytest.mark.asyncio
    async def test_validate_stdio_server_mock(self, sandbox_service):
        """测试 stdio 服务器验证（模拟）"""
        request = MCPSandboxRequest(
            transport="stdio",
            command="uvx",
            args=["mcp-server-example"]
        )
        
        # 模拟进程
        mock_process = AsyncMock()
        mock_process.stdin = AsyncMock()
        mock_process.stdout = AsyncMock()
        
        # 模拟初始化响应
        init_response = {
            "jsonrpc": "2.0",
            "id": 1,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "serverInfo": {"name": "test-server", "version": "1.0.0"}
            }
        }
        
        # 模拟工具列表响应
        tools_response = {
            "jsonrpc": "2.0",
            "id": 2,
            "result": {
                "tools": [
                    {
                        "name": "test_tool",
                        "description": "A test tool",
                        "inputSchema": {"type": "object"}
                    }
                ]
            }
        }
        
        mock_process.stdout.readline.side_effect = [
            (json.dumps(init_response) + '\n').encode('utf-8'),
            (json.dumps(tools_response) + '\n').encode('utf-8')
        ]
        
        with patch('asyncio.create_subprocess_exec', return_value=mock_process):
            result = await sandbox_service._validate_stdio_server(request)
            
            assert result.success
            assert result.transport == "stdio"
            assert result.server_info is not None
            assert len(result.available_tools) == 1
            assert result.available_tools[0].name == "test_tool"
    
    @pytest.mark.asyncio
    async def test_validate_http_server_mock(self, sandbox_service):
        """测试 HTTP 服务器验证（模拟）"""
        request = MCPSandboxRequest(
            transport="http",
            url="http://localhost:3000/mcp"
        )
        
        # 模拟 HTTP 响应
        init_response_data = {
            "jsonrpc": "2.0",
            "id": 1,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "serverInfo": {"name": "http-server", "version": "1.0.0"}
            }
        }
        
        tools_response_data = {
            "jsonrpc": "2.0",
            "id": 2,
            "result": {
                "tools": [
                    {
                        "name": "http_tool",
                        "description": "An HTTP tool",
                        "inputSchema": {"type": "object"}
                    }
                ]
            }
        }
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            
            # 模拟初始化响应
            mock_init_response = MagicMock()
            mock_init_response.json.return_value = init_response_data
            mock_init_response.raise_for_status.return_value = None
            
            # 模拟工具列表响应
            mock_tools_response = MagicMock()
            mock_tools_response.status_code = 200
            mock_tools_response.json.return_value = tools_response_data
            
            mock_client.post.side_effect = [mock_init_response, mock_tools_response]
            
            result = await sandbox_service._validate_http_server(request)
            
            assert result.success
            assert result.transport == "http"
            assert result.server_info is not None
            assert len(result.available_tools) == 1
            assert result.available_tools[0].name == "http_tool"


class TestMCPSandboxConfig:
    """测试 MCP 沙盒配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = MCPSandboxConfig()
        
        assert config.DEFAULT_TIMEOUT == 30
        assert config.MAX_TIMEOUT == 120
        assert config.CONNECT_TIMEOUT == 10
        assert "stdio" in config.SUPPORTED_TRANSPORTS
        assert "http" in config.SUPPORTED_TRANSPORTS
        assert config.PROTOCOL_VERSION == "2024-11-05"
        assert config.CLIENT_INFO["name"] == "tool-registry-sandbox"
    
    def test_allowed_commands(self):
        """测试允许的命令"""
        config = MCPSandboxConfig()
        
        allowed_prefixes = ["uvx", "npx", "python", "/usr/bin/"]
        for prefix in allowed_prefixes:
            assert prefix in config.ALLOWED_COMMAND_PREFIXES
    
    def test_forbidden_patterns(self):
        """测试禁止的模式"""
        config = MCPSandboxConfig()
        
        forbidden_patterns = ["rm", "sudo", "curl", "ssh"]
        for pattern in forbidden_patterns:
            assert pattern in config.FORBIDDEN_COMMAND_PATTERNS


class TestMCPDataClasses:
    """测试 MCP 数据类"""
    
    def test_mcp_tool_info(self):
        """测试 MCP 工具信息"""
        tool = MCPToolInfo(
            name="test_tool",
            description="A test tool",
            input_schema={"type": "object"},
            output_schema={"type": "string"}
        )
        
        assert tool.name == "test_tool"
        assert tool.description == "A test tool"
        assert tool.input_schema == {"type": "object"}
        assert tool.output_schema == {"type": "string"}
    
    def test_mcp_server_info(self):
        """测试 MCP 服务器信息"""
        server = MCPServerInfo(
            name="test-server",
            version="1.0.0",
            protocol_version="2024-11-05",
            capabilities={"tools": {}},
            server_info={"description": "Test server"}
        )
        
        assert server.name == "test-server"
        assert server.version == "1.0.0"
        assert server.protocol_version == "2024-11-05"
        assert server.capabilities == {"tools": {}}
        assert server.server_info == {"description": "Test server"}
    
    def test_mcp_validation_result(self):
        """测试 MCP 验证结果"""
        result = MCPValidationResult(
            success=True,
            transport="stdio",
            connection_time_ms=1500
        )
        
        assert result.success is True
        assert result.transport == "stdio"
        assert result.connection_time_ms == 1500
        assert result.available_tools == []  # 默认空列表
        assert result.security_warnings == []  # 默认空列表


class TestGlobalService:
    """测试全局服务获取"""
    
    @pytest.mark.asyncio
    async def test_get_mcp_sandbox_service(self):
        """测试获取全局服务实例"""
        service1 = await get_mcp_sandbox_service()
        service2 = await get_mcp_sandbox_service()
        
        # 应该返回同一个实例
        assert service1 is service2
        assert isinstance(service1, MCPSandboxService)


class TestComplexScenarios:
    """测试复杂场景"""
    
    @pytest.mark.asyncio
    async def test_full_validation_workflow_mock(self):
        """测试完整验证工作流（模拟）"""
        sandbox = MCPSandboxService()
        
        request = MCPSandboxRequest(
            transport="stdio",
            command="uvx",
            args=["mcp-server-filesystem"]
        )
        
        # 模拟完整的验证过程
        mock_process = AsyncMock()
        mock_process.stdin = AsyncMock()
        mock_process.stdout = AsyncMock()
        
        # 复杂的服务器响应
        init_response = {
            "jsonrpc": "2.0",
            "id": 1,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {},
                    "resources": {},
                    "prompts": {}
                },
                "serverInfo": {
                    "name": "filesystem-server",
                    "version": "2.1.0",
                    "description": "MCP server for filesystem operations"
                }
            }
        }
        
        tools_response = {
            "jsonrpc": "2.0",
            "id": 2,
            "result": {
                "tools": [
                    {
                        "name": "read_file",
                        "description": "Read contents of a file",
                        "inputSchema": {
                            "type": "object",
                            "required": ["path"],
                            "properties": {
                                "path": {
                                    "type": "string",
                                    "description": "Path to the file to read"
                                }
                            }
                        }
                    },
                    {
                        "name": "list_directory",
                        "description": "List contents of a directory",
                        "inputSchema": {
                            "type": "object",
                            "required": ["path"],
                            "properties": {
                                "path": {
                                    "type": "string",
                                    "description": "Path to the directory to list"
                                }
                            }
                        }
                    }
                ]
            }
        }
        
        mock_process.stdout.readline.side_effect = [
            (json.dumps(init_response) + '\n').encode('utf-8'),
            (json.dumps(tools_response) + '\n').encode('utf-8')
        ]
        
        with patch('asyncio.create_subprocess_exec', return_value=mock_process):
            result = await sandbox.validate_mcp_server(request)
            
            assert result.success
            assert result.transport == "stdio"
            assert result.connection_time_ms is not None
            assert result.server_info.server_info["name"] == "filesystem-server"
            assert len(result.available_tools) == 2
            assert result.available_tools[0].name == "read_file"
            assert result.available_tools[1].name == "list_directory"
            assert result.quickstart is not None
            assert "command" in result.quickstart
    
    def test_security_comprehensive_check(self):
        """测试综合安全检查"""
        sandbox = MCPSandboxService()
        
        # 测试各种危险情况
        dangerous_cases = [
            MCPSandboxRequest(transport="stdio", command="uvx", args=["rm", "-rf"]),
            MCPSandboxRequest(transport="http", url="http://localhost:22/mcp"),
        ]
        
        for request in dangerous_cases:
            warnings = sandbox._security_check(request)
            # 应该有安全警告
            assert len(warnings) > 0 or not request  # 或者在验证阶段就失败
    
    @pytest.mark.asyncio
    async def test_error_handling_scenarios(self):
        """测试错误处理场景"""
        sandbox = MCPSandboxService()
        
        # 测试超时情况
        request = MCPSandboxRequest(
            transport="stdio",
            command="uvx",
            args=["non-existent-server"],
            timeout=1
        )
        
        with patch('asyncio.create_subprocess_exec') as mock_exec:
            mock_process = AsyncMock()
            mock_process.stdout.readline.side_effect = asyncio.TimeoutError()
            mock_exec.return_value = mock_process
            
            result = await sandbox.validate_mcp_server(request)
            
            assert not result.success
            assert "超时" in result.error or "timeout" in result.error.lower()


if __name__ == "__main__":
    pytest.main([__file__])