"""
Pytest configuration and shared fixtures for the test suite.
"""

import pytest
import asyncio
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.core.database import Base, get_db
from app.models.user import User
from app.models.tool import Tool
from app.models.function_call import FunctionCall

# Test database URL (use in-memory SQLite for tests)
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
test_engine = create_engine(
    TEST_DATABASE_URL, 
    connect_args={"check_same_thread": False}
)

TestingSessionLocal = sessionmaker(
    autocommit=False, 
    autoflush=False, 
    bind=test_engine
)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Create all tables
    Base.metadata.create_all(bind=test_engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop all tables after test
        Base.metadata.drop_all(bind=test_engine)

@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()

@pytest.fixture(scope="function")
async def async_client(db_session):
    """Create an async test client."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    # Clean up
    app.dependency_overrides.clear()

@pytest.fixture
def sample_user(db_session):
    """Create a sample user for testing."""
    user = User(
        id="test-user-1",
        username="testuser",
        email="<EMAIL>",
        full_name="Test User",
        hashed_password="hashed_password_here",
        is_active=True,
        is_admin=False
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def admin_user(db_session):
    """Create an admin user for testing."""
    user = User(
        id="admin-user-1",
        username="admin",
        email="<EMAIL>",
        full_name="Admin User",
        hashed_password="hashed_password_here",
        is_active=True,
        is_admin=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def sample_tool(db_session, sample_user):
    """Create a sample tool for testing."""
    tool = Tool(
        id="test-tool-1",
        name="Test Weather API",
        description="A test weather API tool",
        version="1.0.0",
        category="api",
        tags='["weather", "api", "test"]',
        visibility="public",
        requires_auth=False,
        transport="http",
        endpoint_url="https://api.weather.com/v1/current",
        status="active",
        user_id=sample_user.id,
        input_schema='{"type": "object", "properties": {"location": {"type": "string", "description": "City name"}}, "required": ["location"]}',
        output_schema='{"type": "object", "properties": {"temperature": {"type": "number"}, "description": {"type": "string"}}}'
    )
    db_session.add(tool)
    db_session.commit()
    db_session.refresh(tool)
    return tool

@pytest.fixture
def private_tool(db_session, sample_user):
    """Create a private tool for testing."""
    tool = Tool(
        id="private-tool-1",
        name="Private Tool",
        description="A private tool for testing",
        version="1.0.0",
        category="utility",
        tags='["private", "test"]',
        visibility="private",
        requires_auth=True,
        transport="python",
        status="active",
        user_id=sample_user.id,
        input_schema='{"type": "object", "properties": {"input": {"type": "string"}}, "required": ["input"]}'
    )
    db_session.add(tool)
    db_session.commit()
    db_session.refresh(tool)
    return tool

@pytest.fixture
def sample_function_call(db_session, sample_tool, sample_user):
    """Create a sample function call for testing."""
    function_call = FunctionCall(
        id="test-call-1",
        tool_id=sample_tool.id,
        user_id=sample_user.id,
        arguments='{"location": "New York"}',
        result='{"temperature": 22.5, "description": "Sunny"}',
        execution_time=0.5,
        success=True
    )
    db_session.add(function_call)
    db_session.commit()
    db_session.refresh(function_call)
    return function_call

@pytest.fixture
def mock_curl_command():
    """Sample cURL command for testing."""
    return """curl -X POST "https://api.example.com/v1/weather" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer token123" \
     -d '{"location": "New York", "units": "metric"}'"""

@pytest.fixture
def mock_python_function():
    """Sample Python function for testing."""
    return '''
def get_weather(location: str, units: str = "metric") -> dict:
    """
    Get weather information for a location.
    
    Args:
        location: The city name or coordinates
        units: Temperature units (metric or imperial)
        
    Returns:
        Weather data including temperature and description
    """
    # Mock implementation
    return {
        "temperature": 22.5,
        "description": "Sunny",
        "units": units,
        "location": location
    }
'''

@pytest.fixture
def mock_openai_function_schema():
    """Sample OpenAI function schema for testing."""
    return {
        "name": "get_weather",
        "description": "Get weather information for a location",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The city name or coordinates"
                },
                "units": {
                    "type": "string",
                    "enum": ["metric", "imperial"],
                    "description": "Temperature units",
                    "default": "metric"
                }
            },
            "required": ["location"]
        }
    }

@pytest.fixture
def mock_mcp_server_config():
    """Sample MCP server configuration for testing."""
    return {
        "name": "test-mcp-server",
        "transport": "stdio",
        "command": "python",
        "args": ["-m", "test_mcp_server"],
        "env": {}
    }

# Test data generators
class TestDataGenerator:
    """Helper class for generating test data."""
    
    @staticmethod
    def create_user_data(username="testuser", email="<EMAIL>"):
        """Generate user creation data."""
        return {
            "username": username,
            "email": email,
            "full_name": f"Test {username.title()}",
            "password": "testpassword123"
        }
    
    @staticmethod
    def create_tool_data(name="Test Tool", category="utility"):
        """Generate tool creation data."""
        return {
            "name": name,
            "description": f"A test {category} tool",
            "category": category,
            "tags": ["test", category],
            "visibility": "public",
            "requires_auth": False,
            "transport": "http",
            "input_schema": {
                "type": "object",
                "properties": {
                    "input": {
                        "type": "string",
                        "description": "Input parameter"
                    }
                },
                "required": ["input"]
            }
        }
    
    @staticmethod
    def create_function_call_data(tool_id, arguments=None):
        """Generate function call data."""
        return {
            "tool_id": tool_id,
            "arguments": arguments or {"input": "test"}
        }

@pytest.fixture
def test_data_generator():
    """Provide test data generator."""
    return TestDataGenerator()

# Async test helpers
@pytest.fixture
async def async_db_session():
    """Create an async database session for testing."""
    # This would be implemented if using async SQLAlchemy
    pass

# Mock external services
@pytest.fixture
def mock_llm_service(monkeypatch):
    """Mock LLM service for testing."""
    class MockLLMService:
        async def generate_tool_from_description(self, description):
            return {
                "name": "Generated Tool",
                "description": description,
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "input": {"type": "string"}
                    }
                }
            }
    
    mock_service = MockLLMService()
    monkeypatch.setattr("app.services.llm_service.LLMService", lambda: mock_service)
    return mock_service

@pytest.fixture
def mock_mcp_client(monkeypatch):
    """Mock MCP client for testing."""
    class MockMCPClient:
        async def connect(self, config):
            return True
        
        async def list_tools(self):
            return [
                {
                    "name": "mock_tool",
                    "description": "A mock tool",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "input": {"type": "string"}
                        }
                    }
                }
            ]
    
    mock_client = MockMCPClient()
    monkeypatch.setattr("app.services.mcp_service.MCPClient", lambda: mock_client)
    return mock_client
