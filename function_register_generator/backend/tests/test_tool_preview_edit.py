"""
工具预览和编辑功能测试
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

# 测试数据
SAMPLE_TOOL_DEFINITION = {
    "toolId": "http.weather_tool.abc123",
    "displayName": "天气查询工具",
    "category": "weather",
    "transport": "http",
    "runtime": {
        "transport": "http",
        "endpoint": "https://api.weather.com/v1/current"
    },
    "inputsDeveloperSchema": {
        "type": "object",
        "properties": {
            "city": {"type": "string", "description": "城市名称"},
            "country": {"type": "string", "description": "国家代码"}
        },
        "required": ["city"]
    },
    "outputsSchema": {
        "type": "object",
        "properties": {
            "temperature": {"type": "number", "description": "温度"},
            "humidity": {"type": "number", "description": "湿度"}
        }
    },
    "descriptionUser": "获取指定城市的天气信息",
    "examples": []
}

SAMPLE_DRAFT_REQUEST = {
    "tool_definition": SAMPLE_TOOL_DEFINITION,
    "draft_name": "天气工具草稿",
    "description": "测试用的天气工具草稿",
    "tags": ["weather", "api", "test"],
    "auto_save": True,
    "expiry_hours": 48
}

SAMPLE_EDIT_REQUEST = {
    "modifications": {
        "displayName": "高级天气查询工具",
        "descriptionUser": "获取指定城市的详细天气信息，包括温度、湿度、风速等",
        "inputsDeveloperSchema": {
            "type": "object",
            "properties": {
                "city": {"type": "string", "description": "城市名称"},
                "country": {"type": "string", "description": "国家代码"},
                "units": {"type": "string", "enum": ["metric", "imperial"], "description": "单位制"}
            },
            "required": ["city"]
        }
    },
    "validation_level": "full",
    "auto_save": True,
    "create_backup": True
}

SAMPLE_PUBLISH_REQUEST = {
    "publish_options": {
        "visibility": "public",
        "category": "weather",
        "tags": ["weather", "api"]
    },
    "final_validation": True,
    "cleanup_draft": True
}

class TestToolPreviewEdit:
    """工具预览和编辑测试类"""
    
    @pytest.fixture
    def mock_repository(self):
        """模拟工具仓库"""
        repository = AsyncMock()
        repository.create = AsyncMock(return_value=True)
        return repository
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_create_tool_draft_success(self, mock_get_repo, mock_repository):
        """测试创建工具草稿成功"""
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import create_tool_draft, ToolDraftRequest
        
        request = ToolDraftRequest(**SAMPLE_DRAFT_REQUEST)
        result = await create_tool_draft(request)
        
        # 验证结果
        assert result.success is True
        assert result.draft_id.startswith("draft_")
        assert result.message == "工具草稿创建成功"
        assert result.draft_data is not None
        assert result.expiry_time is not None
        assert result.edit_url is not None
        
        # 验证草稿数据
        draft_data = result.draft_data
        assert draft_data["draft_name"] == "天气工具草稿"
        assert draft_data["description"] == "测试用的天气工具草稿"
        assert draft_data["tags"] == ["weather", "api", "test"]
        assert draft_data["status"] == "draft"
        assert draft_data["version"] == 1
        assert draft_data["auto_save"] is True
    
    async def test_get_tool_draft_success(self):
        """测试获取工具草稿成功"""
        from backend.routers.tools import get_tool_draft
        
        # 使用有效的草稿ID
        draft_id = "draft_test123"
        result = await get_tool_draft(draft_id)
        
        assert result["success"] is True
        assert result["draft_data"] is not None
        assert result["draft_data"]["draft_id"] == draft_id
    
    async def test_get_tool_draft_not_found(self):
        """测试获取不存在的草稿"""
        from backend.routers.tools import get_tool_draft
        from fastapi import HTTPException
        
        # 使用无效的草稿ID
        draft_id = "invalid_draft"
        
        with pytest.raises(HTTPException) as exc_info:
            await get_tool_draft(draft_id)
        
        assert exc_info.value.status_code == 404
        assert "草稿不存在" in str(exc_info.value.detail)
    
    async def test_edit_tool_draft_success(self):
        """测试编辑工具草稿成功"""
        from backend.routers.tools import edit_tool_draft, ToolEditRequest
        
        draft_id = "draft_test123"
        request = ToolEditRequest(**SAMPLE_EDIT_REQUEST)
        result = await edit_tool_draft(draft_id, request)
        
        # 验证结果
        assert result.success is True
        assert result.message == "工具编辑成功"
        assert result.updated_definition is not None
        assert result.backup_id is not None
        
        # 验证修改应用
        updated_def = result.updated_definition
        assert updated_def["displayName"] == "高级天气查询工具"
        assert "units" in updated_def["inputsDeveloperSchema"]["properties"]
        
        # 验证验证结果
        assert result.validation_result is not None
    
    async def test_publish_tool_draft_success(self):
        """测试发布工具草稿成功"""
        from backend.routers.tools import publish_tool_draft, ToolPublishRequest
        
        with patch('backend.routers.tools.get_tool_repository') as mock_get_repo:
            mock_repository = AsyncMock()
            mock_repository.create = AsyncMock(return_value=True)
            mock_get_repo.return_value = mock_repository
            
            draft_id = "draft_test123"
            request = ToolPublishRequest(**SAMPLE_PUBLISH_REQUEST)
            result = await publish_tool_draft(draft_id, request)
            
            # 验证结果
            assert result.success is True
            assert result.message == "工具发布成功"
            assert result.tool_id is not None
            assert result.published_tool is not None
            
            # 验证发布状态
            published_tool = result.published_tool
            assert published_tool["status"] == "published"
            assert "publishedTime" in published_tool
            
            # 验证仓库调用
            mock_repository.create.assert_called_once()
    
    async def test_validate_tool_realtime_success(self):
        """测试实时验证成功"""
        from backend.routers.tools import validate_tool_realtime, ValidationRequest
        
        request = ValidationRequest(
            tool_definition=SAMPLE_TOOL_DEFINITION,
            validation_type="full"
        )
        result = await validate_tool_realtime(request)
        
        # 验证结果
        assert result.success is True
        assert isinstance(result.field_errors, dict)
        assert isinstance(result.global_errors, list)
        assert isinstance(result.warnings, list)
        assert isinstance(result.suggestions, list)
        assert result.validation_score is not None
        assert 0 <= result.validation_score <= 1
    
    async def test_validate_tool_realtime_single_field(self):
        """测试单字段实时验证"""
        from backend.routers.tools import validate_tool_realtime, ValidationRequest
        
        request = ValidationRequest(
            tool_definition=SAMPLE_TOOL_DEFINITION,
            validation_type="basic",
            field_name="displayName"
        )
        result = await validate_tool_realtime(request)
        
        # 验证结果
        assert result.success is True
        assert len(result.global_errors) == 0
        # 单字段验证应该只返回该字段的错误
        if result.field_errors:
            assert "displayName" in result.field_errors or len(result.field_errors) == 0
    
    async def test_validate_tool_with_errors(self):
        """测试验证包含错误的工具"""
        from backend.routers.tools import validate_tool_realtime, ValidationRequest
        
        # 创建有错误的工具定义
        invalid_tool = SAMPLE_TOOL_DEFINITION.copy()
        invalid_tool["toolId"] = ""  # 空的工具ID
        invalid_tool["transport"] = "invalid"  # 无效的传输方式
        
        request = ValidationRequest(
            tool_definition=invalid_tool,
            validation_type="full"
        )
        result = await validate_tool_realtime(request)
        
        # 验证结果
        assert result.success is False
        assert len(result.field_errors) > 0 or len(result.global_errors) > 0
        assert result.validation_score < 1.0
    
    async def test_list_tool_drafts(self):
        """测试获取草稿列表"""
        from backend.routers.tools import list_tool_drafts
        
        result = await list_tool_drafts(skip=0, limit=5)
        
        # 验证结果
        assert result["success"] is True
        assert "drafts" in result
        assert "total" in result
        assert result["skip"] == 0
        assert result["limit"] == 5
        assert isinstance(result["drafts"], list)
    
    async def test_list_tool_drafts_with_filters(self):
        """测试带过滤条件的草稿列表"""
        from backend.routers.tools import list_tool_drafts
        
        result = await list_tool_drafts(
            skip=0, 
            limit=10, 
            status="draft", 
            tags="test,demo"
        )
        
        # 验证结果
        assert result["success"] is True
        assert isinstance(result["drafts"], list)
        
        # 验证过滤效果（基于模拟数据）
        for draft in result["drafts"]:
            assert draft["status"] == "draft"
    
    async def test_delete_tool_draft_success(self):
        """测试删除草稿成功"""
        from backend.routers.tools import delete_tool_draft
        
        draft_id = "draft_test123"
        result = await delete_tool_draft(draft_id)
        
        # 验证结果
        assert result["success"] is True
        assert result["message"] == "草稿删除成功"
    
    async def test_delete_tool_draft_not_found(self):
        """测试删除不存在的草稿"""
        from backend.routers.tools import delete_tool_draft
        from fastapi import HTTPException
        
        # 模拟删除失败
        with patch('backend.routers.tools._delete_draft_from_storage', return_value=False):
            draft_id = "nonexistent_draft"
            
            with pytest.raises(HTTPException) as exc_info:
                await delete_tool_draft(draft_id)
            
            assert exc_info.value.status_code == 404
            assert "草稿不存在" in str(exc_info.value.detail)
    
    def test_calculate_validation_score(self):
        """测试验证分数计算"""
        from backend.routers.tools import _calculate_validation_score
        
        # 无错误情况
        score = _calculate_validation_score({}, [], [])
        assert score == 1.0
        
        # 少量错误
        score = _calculate_validation_score(
            {"field1": ["error1"]}, 
            ["global_error"], 
            ["warning1"]
        )
        assert 0.6 <= score <= 0.8
        
        # 大量错误
        score = _calculate_validation_score(
            {"field1": ["error1", "error2"], "field2": ["error3"]},
            ["global_error1", "global_error2"],
            ["warning1", "warning2", "warning3"]
        )
        assert score <= 0.6
    
    async def test_validate_single_field_toolid(self):
        """测试单字段验证 - toolId"""
        from backend.routers.tools import _validate_single_field
        
        # 有效的 toolId
        field_errors, warnings, suggestions = await _validate_single_field(
            {"toolId": "valid.tool.id"}, 
            "toolId"
        )
        assert len(field_errors) == 0
        
        # 无效的 toolId
        field_errors, warnings, suggestions = await _validate_single_field(
            {"toolId": ""}, 
            "toolId"
        )
        assert "toolId" in field_errors
        assert len(field_errors["toolId"]) > 0
    
    async def test_validate_single_field_displayname(self):
        """测试单字段验证 - displayName"""
        from backend.routers.tools import _validate_single_field
        
        # 有效的 displayName
        field_errors, warnings, suggestions = await _validate_single_field(
            {"displayName": "有效的工具名称"}, 
            "displayName"
        )
        assert len(field_errors) == 0
        
        # 空的 displayName
        field_errors, warnings, suggestions = await _validate_single_field(
            {"displayName": ""}, 
            "displayName"
        )
        assert "displayName" in field_errors
        
        # 过长的 displayName
        field_errors, warnings, suggestions = await _validate_single_field(
            {"displayName": "x" * 150}, 
            "displayName"
        )
        assert len(warnings) > 0
    
    async def test_validate_single_field_transport(self):
        """测试单字段验证 - transport"""
        from backend.routers.tools import _validate_single_field
        
        # 有效的 transport
        for transport in ["http", "python", "stdio"]:
            field_errors, warnings, suggestions = await _validate_single_field(
                {"transport": transport}, 
                "transport"
            )
            assert len(field_errors) == 0
        
        # 无效的 transport
        field_errors, warnings, suggestions = await _validate_single_field(
            {"transport": "invalid"}, 
            "transport"
        )
        assert "transport" in field_errors
    
    async def test_validate_tool_definition_basic(self):
        """测试基础工具定义验证"""
        from backend.routers.tools import _validate_tool_definition
        
        # 完整的工具定义
        result = await _validate_tool_definition(SAMPLE_TOOL_DEFINITION, "basic")
        assert result["success"] is True
        assert len(result["field_errors"]) == 0
        assert len(result["global_errors"]) == 0
        
        # 缺少必需字段的工具定义
        incomplete_tool = {"displayName": "测试工具"}
        result = await _validate_tool_definition(incomplete_tool, "basic")
        assert result["success"] is False
        assert len(result["field_errors"]) > 0
    
    async def test_validate_tool_definition_full(self):
        """测试完整工具定义验证"""
        from backend.routers.tools import _validate_tool_definition
        
        # HTTP 工具缺少端点
        http_tool = SAMPLE_TOOL_DEFINITION.copy()
        http_tool["runtime"] = {"transport": "http"}  # 缺少 endpoint
        
        result = await _validate_tool_definition(http_tool, "full")
        assert "runtime.endpoint" in result["field_errors"]
        
        # Python 工具缺少代码
        python_tool = SAMPLE_TOOL_DEFINITION.copy()
        python_tool["transport"] = "python"
        python_tool["runtime"] = {"transport": "python"}  # 缺少 code
        
        result = await _validate_tool_definition(python_tool, "full")
        assert "runtime.code" in result["field_errors"]

if __name__ == "__main__":
    pytest.main([__file__])