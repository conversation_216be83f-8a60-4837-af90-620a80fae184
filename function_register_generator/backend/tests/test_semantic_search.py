"""
语义搜索功能测试
"""
import pytest
import asyncio
import numpy as np
from unittest.mock import AsyncMock, patch, MagicMock

# 测试数据
SAMPLE_TOOLS = [
    {
        "toolId": "weather.api.v1",
        "displayName": "天气查询API",
        "category": "weather",
        "transport": "http",
        "descriptionUser": "获取实时天气信息和预报数据",
        "aliases": ["weather", "天气"],
        "tags": ["weather", "api", "data"]
    },
    {
        "toolId": "weather.parser.v1", 
        "displayName": "天气数据解析器",
        "category": "data",
        "transport": "python",
        "descriptionUser": "解析和处理天气数据格式",
        "aliases": ["parser", "解析器"],
        "tags": ["weather", "parser", "data"]
    },
    {
        "toolId": "file.manager.v1",
        "displayName": "文件管理工具",
        "category": "file",
        "transport": "python",
        "descriptionUser": "管理和操作文件系统",
        "aliases": ["file", "文件"],
        "tags": ["file", "system", "management"]
    }
]

class TestVectorSearchService:
    """向量搜索服务测试"""
    
    @pytest.fixture
    async def vector_service(self):
        """创建向量搜索服务实例"""
        from backend.services.vector_search_service import VectorSearchService
        
        service = VectorSearchService()
        await service.initialize()
        return service
    
    async def test_service_initialization(self, vector_service):
        """测试服务初始化"""
        assert vector_service.embedding_model is not None
        assert vector_service.dimension == 384
        assert isinstance(vector_service.embeddings_cache, dict)
        assert isinstance(vector_service.vector_index, dict)
    
    async def test_generate_embedding(self, vector_service):
        """测试嵌入向量生成"""
        text = "这是一个测试文本"
        embedding = await vector_service.generate_embedding(text)
        
        # 验证向量属性
        assert isinstance(embedding, np.ndarray)
        assert embedding.dtype == np.float32
        assert len(embedding) == vector_service.dimension
        
        # 验证向量已归一化
        norm = np.linalg.norm(embedding)
        assert abs(norm - 1.0) < 0.01  # 允许小的浮点误差
        
        # 测试缓存功能
        embedding2 = await vector_service.generate_embedding(text)
        assert np.array_equal(embedding, embedding2)
    
    async def test_build_tool_text(self, vector_service):
        """测试工具文本构建"""
        tool_data = SAMPLE_TOOLS[0]
        text = vector_service._build_tool_text(tool_data)
        
        # 验证文本包含关键信息
        assert "天气查询API" in text
        assert "获取实时天气信息" in text
        assert "weather" in text
        assert "分类:weather" in text
        assert "传输方式:http" in text
    
    async def test_index_tool(self, vector_service):
        """测试工具索引"""
        tool_data = SAMPLE_TOOLS[0]
        tool_id = tool_data["toolId"]
        
        # 创建索引
        success = await vector_service.index_tool(tool_id, tool_data)
        assert success is True
        
        # 验证索引存在
        assert tool_id in vector_service.vector_index
        
        # 验证索引内容
        index_data = vector_service.vector_index[tool_id]
        assert "embedding" in index_data
        assert "tool_data" in index_data
        assert "text_content" in index_data
        assert "indexed_time" in index_data
        
        # 验证嵌入向量
        embedding = index_data["embedding"]
        assert isinstance(embedding, np.ndarray)
        assert len(embedding) == vector_service.dimension
    
    async def test_cosine_similarity(self, vector_service):
        """测试余弦相似度计算"""
        # 相同向量
        vec1 = np.array([1, 0, 0], dtype=np.float32)
        vec2 = np.array([1, 0, 0], dtype=np.float32)
        similarity = vector_service._cosine_similarity(vec1, vec2)
        assert abs(similarity - 1.0) < 0.01
        
        # 正交向量
        vec3 = np.array([0, 1, 0], dtype=np.float32)
        similarity = vector_service._cosine_similarity(vec1, vec3)
        assert abs(similarity - 0.0) < 0.01
        
        # 相反向量
        vec4 = np.array([-1, 0, 0], dtype=np.float32)
        similarity = vector_service._cosine_similarity(vec1, vec4)
        assert abs(similarity - (-1.0)) < 0.01
    
    async def test_semantic_search(self, vector_service):
        """测试语义搜索"""
        # 先索引一些工具
        for tool in SAMPLE_TOOLS:
            await vector_service.index_tool(tool["toolId"], tool)
        
        # 执行语义搜索
        results = await vector_service.semantic_search("天气信息查询", top_k=5)
        
        # 验证结果
        assert isinstance(results, list)
        assert len(results) <= 5
        
        if results:
            # 验证结果结构
            result = results[0]
            assert "tool_id" in result
            assert "similarity" in result
            assert "tool_data" in result
            assert isinstance(result["similarity"], float)
            assert 0 <= result["similarity"] <= 1
            
            # 验证结果按相似度排序
            for i in range(1, len(results)):
                assert results[i-1]["similarity"] >= results[i]["similarity"]
    
    async def test_get_similar_tools(self, vector_service):
        """测试相似工具获取"""
        # 先索引一些工具
        for tool in SAMPLE_TOOLS:
            await vector_service.index_tool(tool["toolId"], tool)
        
        # 获取相似工具
        target_tool_id = SAMPLE_TOOLS[0]["toolId"]
        similar_tools = await vector_service.get_similar_tools(target_tool_id, top_k=3)
        
        # 验证结果
        assert isinstance(similar_tools, list)
        assert len(similar_tools) <= 3
        
        # 验证不包含目标工具本身
        tool_ids = [tool["tool_id"] for tool in similar_tools]
        assert target_tool_id not in tool_ids
        
        if similar_tools:
            # 验证结果结构
            similar_tool = similar_tools[0]
            assert "tool_id" in similar_tool
            assert "similarity" in similar_tool
            assert "tool_data" in similar_tool
    
    async def test_hybrid_search(self, vector_service):
        """测试混合搜索"""
        # 先索引一些工具
        for tool in SAMPLE_TOOLS:
            await vector_service.index_tool(tool["toolId"], tool)
        
        # 模拟关键词搜索结果
        keyword_results = [
            {**SAMPLE_TOOLS[0], "_score": 8.5},
            {**SAMPLE_TOOLS[1], "_score": 6.2}
        ]
        
        # 执行混合搜索
        hybrid_results = await vector_service.hybrid_search(
            query="天气数据处理",
            keyword_results=keyword_results,
            semantic_weight=0.4,
            keyword_weight=0.6,
            top_k=5
        )
        
        # 验证结果
        assert isinstance(hybrid_results, list)
        assert len(hybrid_results) <= 5
        
        if hybrid_results:
            # 验证结果包含混合分数
            result = hybrid_results[0]
            assert "_hybrid_score" in result
            assert "_keyword_score" in result
            assert "_semantic_score" in result
    
    async def test_update_and_remove_index(self, vector_service):
        """测试索引更新和删除"""
        tool_data = SAMPLE_TOOLS[0]
        tool_id = tool_data["toolId"]
        
        # 创建索引
        await vector_service.index_tool(tool_id, tool_data)
        assert tool_id in vector_service.vector_index
        
        # 更新索引
        updated_tool_data = tool_data.copy()
        updated_tool_data["displayName"] = "更新的天气API"
        
        success = await vector_service.update_tool_index(tool_id, updated_tool_data)
        assert success is True
        
        # 验证更新
        index_data = vector_service.vector_index[tool_id]
        assert index_data["tool_data"]["displayName"] == "更新的天气API"
        
        # 删除索引
        success = await vector_service.remove_tool_index(tool_id)
        assert success is True
        assert tool_id not in vector_service.vector_index
        
        # 删除不存在的索引
        success = await vector_service.remove_tool_index("nonexistent_id")
        assert success is False
    
    async def test_get_index_stats(self, vector_service):
        """测试索引统计信息"""
        # 添加一些工具
        for tool in SAMPLE_TOOLS:
            await vector_service.index_tool(tool["toolId"], tool)
        
        # 获取统计信息
        stats = await vector_service.get_index_stats()
        
        # 验证统计信息
        assert isinstance(stats, dict)
        assert "total_tools" in stats
        assert "cache_size" in stats
        assert "dimension" in stats
        assert "model" in stats
        assert "memory_usage" in stats
        
        assert stats["total_tools"] == len(SAMPLE_TOOLS)
        assert stats["dimension"] == 384
    
    async def test_clear_cache(self, vector_service):
        """测试缓存清除"""
        # 生成一些嵌入向量以填充缓存
        await vector_service.generate_embedding("测试文本1")
        await vector_service.generate_embedding("测试文本2")
        
        # 验证缓存不为空
        assert len(vector_service.embeddings_cache) > 0
        
        # 清除缓存
        success = await vector_service.clear_cache()
        assert success is True
        assert len(vector_service.embeddings_cache) == 0

class TestSemanticSearchAPI:
    """语义搜索API测试"""
    
    @pytest.fixture
    def mock_vector_service(self):
        """模拟向量搜索服务"""
        service = AsyncMock()
        service.semantic_search = AsyncMock(return_value=[
            {
                "tool_id": "test.tool.1",
                "similarity": 0.85,
                "tool_data": SAMPLE_TOOLS[0]
            }
        ])
        service.hybrid_search = AsyncMock(return_value=[SAMPLE_TOOLS[0]])
        service.get_similar_tools = AsyncMock(return_value=[
            {
                "tool_id": "test.tool.2",
                "similarity": 0.75,
                "tool_data": SAMPLE_TOOLS[1]
            }
        ])
        return service
    
    @patch('backend.routers.search.get_vector_search_service')
    async def test_semantic_search_endpoint(self, mock_get_service, mock_vector_service):
        """测试语义搜索端点"""
        mock_get_service.return_value = mock_vector_service
        
        from backend.routers.search import semantic_search_tools
        
        result = await semantic_search_tools(
            q="天气查询",
            top_k=10,
            similarity_threshold=0.1
        )
        
        # 验证结果
        assert result.success is True
        assert result.query == "天气查询"
        assert len(result.results) > 0
        
        # 验证服务调用
        mock_vector_service.semantic_search.assert_called_once_with(
            query="天气查询",
            top_k=10,
            similarity_threshold=0.1
        )
    
    @patch('backend.routers.search.get_vector_search_service')
    @patch('backend.routers.search._search_mock_data')
    async def test_hybrid_search_endpoint(self, mock_search_data, mock_get_service, mock_vector_service):
        """测试混合搜索端点"""
        mock_get_service.return_value = mock_vector_service
        mock_search_data.return_value = (SAMPLE_TOOLS, len(SAMPLE_TOOLS), 100)
        
        from backend.routers.search import hybrid_search_tools
        
        result = await hybrid_search_tools(
            q="API工具",
            semantic_weight=0.4,
            keyword_weight=0.6,
            top_k=15
        )
        
        # 验证结果
        assert result.success is True
        assert result.query == "API工具"
        assert len(result.results) > 0
        
        # 验证权重归一化
        filters = result.filters
        total_weight = filters["semantic_weight"] + filters["keyword_weight"]
        assert abs(total_weight - 1.0) < 0.01
    
    @patch('backend.routers.search.get_vector_search_service')
    async def test_get_similar_tools_endpoint(self, mock_get_service, mock_vector_service):
        """测试相似工具端点"""
        mock_get_service.return_value = mock_vector_service
        
        from backend.routers.search import get_similar_tools
        
        result = await get_similar_tools(
            tool_id="test.tool.1",
            top_k=5,
            similarity_threshold=0.2
        )
        
        # 验证结果
        assert result["success"] is True
        assert result["tool_id"] == "test.tool.1"
        assert "similar_tools" in result
        assert "total" in result
        
        # 验证服务调用
        mock_vector_service.get_similar_tools.assert_called_once_with(
            tool_id="test.tool.1",
            top_k=5
        )
    
    @patch('backend.routers.search.get_vector_search_service')
    async def test_index_tool_endpoint(self, mock_get_service, mock_vector_service):
        """测试工具索引端点"""
        mock_vector_service.index_tool = AsyncMock(return_value=True)
        mock_get_service.return_value = mock_vector_service
        
        from backend.routers.search import index_tool_for_search
        
        result = await index_tool_for_search(
            tool_id="test.tool.1",
            tool_data=SAMPLE_TOOLS[0]
        )
        
        # 验证结果
        assert result["success"] is True
        assert "索引创建成功" in result["message"]
        
        # 验证服务调用
        mock_vector_service.index_tool.assert_called_once_with(
            "test.tool.1",
            SAMPLE_TOOLS[0]
        )
    
    @patch('backend.routers.search.get_vector_search_service')
    async def test_vector_stats_endpoint(self, mock_get_service, mock_vector_service):
        """测试向量统计端点"""
        mock_stats = {
            "total_tools": 100,
            "cache_size": 50,
            "dimension": 384,
            "model": "mock_model",
            "memory_usage": "2.5 MB"
        }
        mock_vector_service.get_index_stats = AsyncMock(return_value=mock_stats)
        mock_get_service.return_value = mock_vector_service
        
        from backend.routers.search import get_vector_search_stats
        
        result = await get_vector_search_stats()
        
        # 验证结果
        assert result["success"] is True
        assert result["stats"] == mock_stats

if __name__ == "__main__":
    pytest.main([__file__])