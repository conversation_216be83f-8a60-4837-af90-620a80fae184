"""
智能工具注册端点测试
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

# 测试数据
SAMPLE_SMART_REGISTRATION = {
    "display_name": "GitHub API 用户信息",
    "description": "获取 GitHub 用户的详细信息",
    "category": "api",
    "curl_command": "curl -H 'Accept: application/vnd.github.v3+json' https://api.github.com/users/octocat",
    "auto_complete": True,
    "auto_validate": True,
    "generate_examples": True,
    "expected_inputs": {"username": "octocat"},
    "expected_outputs": {"login": "octocat", "id": 583231, "name": "The Octocat"}
}

SAMPLE_TOOL_MODIFICATION = {
    "tool_id": "http.github_api_用户信息.abc123",
    "modifications": {
        "displayName": "GitHub 用户信息 API",
        "descriptionUser": "获取指定用户名的 GitHub 用户详细信息，包括头像、仓库数量等"
    },
    "regenerate_examples": True,
    "revalidate": True
}

class TestSmartRegistration:
    """智能注册测试类"""
    
    @pytest.fixture
    def mock_repository(self):
        """模拟工具仓库"""
        repository = AsyncMock()
        repository.create = AsyncMock(return_value=True)
        repository.get_by_id = AsyncMock(return_value={
            "toolId": "http.github_api_用户信息.abc123",
            "displayName": "GitHub API 用户信息",
            "category": "api",
            "transport": "http"
        })
        repository.update = AsyncMock(return_value={
            "toolId": "http.github_api_用户信息.abc123",
            "displayName": "GitHub 用户信息 API",
            "category": "api",
            "transport": "http"
        })
        return repository
    
    @pytest.fixture
    def mock_curl_parser(self):
        """模拟 cURL 解析器"""
        return {
            "url": "https://api.github.com/users/octocat",
            "method": "GET",
            "headers": {"Accept": "application/vnd.github.v3+json"},
            "params": {},
            "data": None
        }
    
    @pytest.fixture
    def mock_completion_service(self):
        """模拟智能补全服务"""
        mock_service = AsyncMock()
        mock_result = MagicMock()
        mock_result.success = True
        mock_result.tool_definition = {
            "toolId": "http.github_api.abc123",
            "displayName": "GitHub 用户信息 API",
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {
                    "username": {"type": "string", "description": "GitHub 用户名"}
                },
                "required": ["username"]
            },
            "outputsSchema": {
                "type": "object",
                "properties": {
                    "login": {"type": "string"},
                    "id": {"type": "integer"},
                    "name": {"type": "string"}
                }
            },
            "descriptionUser": "获取 GitHub 用户的详细信息",
            "examples": []
        }
        mock_result.suggestions = ["建议添加错误处理", "建议添加速率限制说明"]
        mock_result.confidence = 0.95
        mock_result.error = None
        
        mock_service.complete_tool_definition = AsyncMock(return_value=mock_result)
        return mock_service
    
    @pytest.fixture
    def mock_http_sandbox(self):
        """模拟 HTTP 沙盒"""
        mock_sandbox = AsyncMock()
        mock_result = MagicMock()
        mock_result.success = True
        mock_result.status_code = 200
        mock_result.latency_ms = 150
        mock_result.content_type = "application/json"
        mock_result.error = None
        
        mock_sandbox.validate_http_tool = AsyncMock(return_value=mock_result)
        mock_sandbox.__aenter__ = AsyncMock(return_value=mock_sandbox)
        mock_sandbox.__aexit__ = AsyncMock(return_value=None)
        
        return mock_sandbox
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.parse_curl')
    @patch('backend.routers.tools.get_intelligent_completion_service')
    @patch('backend.routers.tools.get_http_sandbox_service')
    async def test_smart_register_tool_success(self, mock_get_sandbox, mock_get_completion, 
                                             mock_parse_curl, mock_get_repo,
                                             mock_repository, mock_curl_parser, 
                                             mock_completion_service, mock_http_sandbox):
        """测试智能工具注册成功"""
        # 设置 mocks
        mock_get_repo.return_value = mock_repository
        mock_parse_curl.return_value = mock_curl_parser
        mock_get_completion.return_value = mock_completion_service
        mock_get_sandbox.return_value = mock_http_sandbox
        
        from backend.routers.tools import smart_register_tool, SmartRegistrationRequest
        
        request = SmartRegistrationRequest(**SAMPLE_SMART_REGISTRATION)
        result = await smart_register_tool(request)
        
        # 验证结果
        assert result.success is True
        assert result.tool_id is not None
        assert "智能工具注册成功" in result.message
        assert result.tool_definition is not None
        assert len(result.completion_suggestions) > 0
        assert result.confidence_score is not None
        assert result.confidence_score > 0.9
        
        # 验证服务调用
        mock_parse_curl.assert_called_once()
        mock_completion_service.complete_tool_definition.assert_called_once()
        mock_http_sandbox.validate_http_tool.assert_called_once()
        mock_repository.create.assert_called_once()
    
    @patch('backend.routers.tools.get_tool_repository')
    @patch('backend.routers.tools.parse_curl')
    async def test_smart_register_without_completion(self, mock_parse_curl, mock_get_repo,
                                                   mock_repository, mock_curl_parser):
        """测试不使用智能补全的注册"""
        mock_get_repo.return_value = mock_repository
        mock_parse_curl.return_value = mock_curl_parser
        
        from backend.routers.tools import smart_register_tool, SmartRegistrationRequest
        
        request_data = SAMPLE_SMART_REGISTRATION.copy()
        request_data["auto_complete"] = False
        request_data["auto_validate"] = False
        
        request = SmartRegistrationRequest(**request_data)
        result = await smart_register_tool(request)
        
        assert result.success is True
        assert result.tool_id is not None
        assert result.confidence_score is None  # 没有使用智能补全
        
        mock_repository.create.assert_called_once()
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_modify_tool_success(self, mock_get_repo, mock_repository):
        """测试工具修改成功"""
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import modify_tool, ToolModificationRequest
        
        request = ToolModificationRequest(**SAMPLE_TOOL_MODIFICATION)
        result = await modify_tool(request)
        
        assert result.success is True
        assert result.tool_id == SAMPLE_TOOL_MODIFICATION["tool_id"]
        assert "工具修改成功" in result.message
        
        # 验证仓库调用
        mock_repository.get_by_id.assert_called_once_with(SAMPLE_TOOL_MODIFICATION["tool_id"])
        mock_repository.update.assert_called_once()
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_modify_nonexistent_tool(self, mock_get_repo):
        """测试修改不存在的工具"""
        mock_repository = AsyncMock()
        mock_repository.get_by_id = AsyncMock(return_value=None)
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import modify_tool, ToolModificationRequest
        
        request = ToolModificationRequest(**SAMPLE_TOOL_MODIFICATION)
        
        with pytest.raises(Exception):  # 应该抛出 HTTPException
            await modify_tool(request)
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_suggest_tool_improvements(self, mock_get_repo, mock_repository):
        """测试工具改进建议"""
        # 模拟一个质量较低的工具
        low_quality_tool = {
            "toolId": "test.tool",
            "displayName": "测试工具",
            "category": "utility",
            "descriptionUser": "简短描述",  # 描述太短
            "inputsDeveloperSchema": {"type": "object", "properties": {}},  # 没有属性
            "examples": [],  # 没有示例
            "aliases": []  # 没有别名
        }
        
        mock_repository.get_by_id = AsyncMock(return_value=low_quality_tool)
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import suggest_tool_improvements
        
        result = await suggest_tool_improvements("test.tool")
        
        assert result["tool_id"] == "test.tool"
        assert result["quality_score"] < 50  # 质量分数应该较低
        assert len(result["improvement_suggestions"]) > 0
        assert "建议添加更详细的工具描述" in result["improvement_suggestions"]
        assert "建议完善输入参数定义" in result["improvement_suggestions"]
        assert "建议添加使用示例" in result["improvement_suggestions"]
    
    def test_calculate_tool_quality_score(self):
        """测试工具质量分数计算"""
        from backend.routers.tools import _calculate_tool_quality_score
        
        # 高质量工具
        high_quality_tool = {
            "descriptionUser": "这是一个详细的工具描述，包含了工具的用途、功能和使用场景",
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {
                    "param1": {"type": "string", "description": "参数1"},
                    "param2": {"type": "integer", "description": "参数2"},
                    "param3": {"type": "boolean", "description": "参数3"}
                }
            },
            "examples": [{"name": "示例1"}, {"name": "示例2"}],
            "aliases": ["alias1", "alias2"],
            "category": "api",
            "runtime": {"endpoint": "https://api.example.com"}
        }
        
        score = _calculate_tool_quality_score(high_quality_tool)
        assert score >= 90  # 高质量工具应该得分很高
        
        # 低质量工具
        low_quality_tool = {
            "descriptionUser": "简短",
            "inputsDeveloperSchema": {"type": "object", "properties": {}},
            "examples": [],
            "aliases": [],
            "category": "utility",
            "runtime": {}
        }
        
        score = _calculate_tool_quality_score(low_quality_tool)
        assert score < 30  # 低质量工具应该得分较低
    
    def test_infer_schema_from_data(self):
        """测试从数据推断 schema"""
        from backend.routers.tools import _infer_schema_from_data
        
        test_data = {
            "name": "John Doe",
            "age": 30,
            "height": 175.5,
            "is_active": True,
            "tags": ["developer", "python"],
            "profile": {"bio": "Software developer"}
        }
        
        schema = _infer_schema_from_data(test_data, "input")
        
        assert schema["type"] == "object"
        assert schema["properties"]["name"]["type"] == "string"
        assert schema["properties"]["age"]["type"] == "integer"
        assert schema["properties"]["height"]["type"] == "number"
        assert schema["properties"]["is_active"]["type"] == "boolean"
        assert schema["properties"]["tags"]["type"] == "array"
        assert schema["properties"]["profile"]["type"] == "object"
    
    @patch('backend.routers.tools.get_tool_repository')
    async def test_batch_register_tools(self, mock_get_repo, mock_repository):
        """测试批量注册工具"""
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.tools import batch_register_tools, SmartRegistrationRequest
        
        # 准备批量请求
        requests = [
            SmartRegistrationRequest(
                display_name=f"测试工具{i}",
                description=f"这是测试工具{i}",
                category="test"
            ) for i in range(3)
        ]
        
        with patch('backend.routers.tools.smart_register_tool') as mock_smart_register:
            # 模拟成功和失败的情况
            mock_responses = [
                MagicMock(success=True, tool_id="tool1", message="成功"),
                MagicMock(success=False, tool_id=None, message="失败"),
                MagicMock(success=True, tool_id="tool3", message="成功")
            ]
            mock_smart_register.side_effect = mock_responses
            
            result = await batch_register_tools(requests)
            
            assert result["total"] == 3
            assert result["success_count"] == 2
            assert result["failure_count"] == 1
            assert len(result["results"]) == 3

if __name__ == "__main__":
    pytest.main([__file__])