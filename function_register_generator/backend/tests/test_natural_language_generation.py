"""
自然语言工具生成测试
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock

# 测试数据
SAMPLE_NL_GENERATION_REQUEST = {
    "description": "创建一个可以获取指定城市天气信息的工具，支持温度、湿度、风速等详细信息",
    "use_cases": ["查询当前天气", "获取天气预报", "监控天气变化"],
    "target_users": ["普通用户", "开发者"],
    "domain": "weather",
    "example_inputs": {"city": "北京", "country": "CN"},
    "example_outputs": {"temperature": 25, "humidity": 60, "wind_speed": 10},
    "preferred_transport": "http",
    "complexity_level": "medium",
    "include_validation": True,
    "generate_examples": True,
    "tool_name_hint": "天气查询工具",
    "category_hint": "weather",
    "auto_validate": True
}

SAMPLE_TOOL_PREVIEW_REQUEST = {
    "tool_definition": {
        "toolId": "http.weather_tool.abc123",
        "displayName": "天气查询工具",
        "category": "weather",
        "transport": "http",
        "descriptionUser": "获取指定城市的天气信息",
        "inputsDeveloperSchema": {
            "type": "object",
            "properties": {
                "city": {"type": "string", "description": "城市名称"},
                "country": {"type": "string", "description": "国家代码"}
            },
            "required": ["city"]
        },
        "outputsSchema": {
            "type": "object",
            "properties": {
                "temperature": {"type": "number", "description": "温度"},
                "humidity": {"type": "number", "description": "湿度"}
            }
        },
        "examples": [
            {
                "name": "查询北京天气",
                "input": {"city": "北京", "country": "CN"},
                "expected_output": {"temperature": 25, "humidity": 60}
            }
        ]
    },
    "preview_type": "interactive",
    "include_examples": True,
    "include_schema_docs": True
}

class TestNaturalLanguageGeneration:
    """自然语言工具生成测试类"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟 LLM 服务"""
        mock_service = AsyncMock()
        mock_service.analyze_text = AsyncMock(return_value={
            "success": True,
            "content": "这是一个天气查询工具，推荐使用 HTTP API 实现"
        })
        return mock_service
    
    @patch('backend.routers.tools.get_llm_service')
    async def test_generate_tool_from_natural_language_success(self, mock_get_llm, mock_llm_service):
        """测试自然语言工具生成成功"""
        mock_get_llm.return_value = mock_llm_service
        
        from backend.routers.tools import generate_tool_from_natural_language, NaturalLanguageGenerationRequest
        
        request = NaturalLanguageGenerationRequest(**SAMPLE_NL_GENERATION_REQUEST)
        result = await generate_tool_from_natural_language(request)
        
        # 验证结果
        assert result.success is True
        assert result.generation_id is not None
        assert result.tool_definition is not None
        assert result.confidence_score is not None
        assert result.confidence_score > 0.5
        
        # 验证工具定义
        tool_def = result.tool_definition
        assert tool_def["displayName"] == "天气查询工具"
        assert tool_def["category"] == "weather"
        assert tool_def["transport"] == "http"
        assert "city" in tool_def["inputsDeveloperSchema"]["properties"]
        
        # 验证分析结果
        assert result.analysis is not None
        assert result.reasoning is not None
        
        # 验证替代方案
        assert len(result.alternatives) > 0
        
        # 验证建议
        assert isinstance(result.suggestions, list)
    
    async def test_analyze_user_description(self):
        """测试用户描述分析"""
        from backend.routers.tools import _analyze_user_description, NaturalLanguageGenerationRequest
        
        request = NaturalLanguageGenerationRequest(**SAMPLE_NL_GENERATION_REQUEST)
        
        with patch('backend.routers.tools.get_llm_service') as mock_get_llm:
            mock_llm_service = AsyncMock()
            mock_llm_service.analyze_text = AsyncMock(return_value={
                "success": True,
                "content": "天气查询分析结果"
            })
            mock_get_llm.return_value = mock_llm_service
            
            analysis = await _analyze_user_description(request)
            
            # 验证分析结果
            assert analysis["core_function"] is not None
            assert analysis["recommended_transport"] == "http"
            assert analysis["category"] == "weather"
            assert analysis["confidence"] > 0.5
            assert analysis["reasoning"] is not None
    
    def test_extract_core_function(self):
        """测试核心功能提取"""
        from backend.routers.tools import _extract_core_function
        
        test_cases = [
            ("生成随机密码", "生成相关功能"),
            ("查询用户信息", "查询相关功能"),
            ("计算BMI指数", "计算相关功能"),
            ("发送邮件通知", "发送相关功能"),
            ("其他功能描述", "通用工具功能")
        ]
        
        for description, expected in test_cases:
            result = _extract_core_function(description)
            assert result == expected
    
    def test_infer_transport_type(self):
        """测试传输方式推断"""
        from backend.routers.tools import _infer_transport_type, NaturalLanguageGenerationRequest
        
        test_cases = [
            ("调用天气API获取数据", None, "http"),
            ("计算数学公式", None, "python"),
            ("处理文本内容", None, "python"),
            ("任意描述", "stdio", "stdio"),  # 有首选传输方式
        ]
        
        for description, preferred, expected in test_cases:
            request = NaturalLanguageGenerationRequest(
                description=description,
                preferred_transport=preferred
            )
            result = _infer_transport_type(request)
            assert result == expected
    
    def test_infer_input_structure(self):
        """测试输入结构推断"""
        from backend.routers.tools import _infer_input_structure, NaturalLanguageGenerationRequest
        
        # 基于示例数据推断
        request_with_examples = NaturalLanguageGenerationRequest(
            description="测试工具",
            example_inputs={"name": "张三", "age": 25}
        )
        
        result = _infer_input_structure(request_with_examples)
        assert result["type"] == "object"
        assert "name" in result["properties"]
        assert "age" in result["properties"]
        assert result["properties"]["name"]["type"] == "string"
        assert result["properties"]["age"]["type"] == "integer"
        
        # 基于描述推断
        request_with_description = NaturalLanguageGenerationRequest(
            description="查询用户文件信息"
        )
        
        result = _infer_input_structure(request_with_description)
        assert result["type"] == "object"
        assert len(result["properties"]) > 0
    
    def test_infer_category(self):
        """测试分类推断"""
        from backend.routers.tools import _infer_category, NaturalLanguageGenerationRequest
        
        test_cases = [
            ("调用API接口", None, "api"),
            ("处理文件数据", None, "file"),
            ("发送邮件", None, "communication"),
            ("加密密码", None, "security"),
            ("分析数据", None, "analytics"),
            ("任意描述", "custom", "custom")  # 有分类提示
        ]
        
        for description, hint, expected in test_cases:
            request = NaturalLanguageGenerationRequest(
                description=description,
                category_hint=hint
            )
            result = _infer_category(request)
            assert result == expected
    
    def test_generate_tool_name(self):
        """测试工具名称生成"""
        from backend.routers.tools import _generate_tool_name
        
        test_cases = [
            ("生成随机密码的工具", "生成 随机"),
            ("查询天气信息", "查询 天气"),
            ("的一个可以用于计算", "计算"),
            ("", "AI生成工具")
        ]
        
        for description, expected in test_cases:
            result = _generate_tool_name(description)
            if expected == "AI生成工具":
                assert result == expected
            else:
                assert any(word in result for word in expected.split())
    
    def test_generate_python_code(self):
        """测试 Python 代码生成"""
        from backend.routers.tools import _generate_python_code, NaturalLanguageGenerationRequest
        
        request = NaturalLanguageGenerationRequest(
            description="计算两个数字的和"
        )
        analysis = {"recommended_transport": "python"}
        
        code = _generate_python_code(request, analysis)
        
        assert "def main(" in code
        assert "计算两个数字的和" in code
        assert "return" in code
        assert "try:" in code
        assert "except" in code
    
    async def test_generate_usage_examples(self):
        """测试使用示例生成"""
        from backend.routers.tools import _generate_usage_examples, NaturalLanguageGenerationRequest
        
        tool_definition = {
            "displayName": "测试工具",
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "count": {"type": "integer"}
                }
            }
        }
        
        request = NaturalLanguageGenerationRequest(
            description="测试工具",
            example_scenarios=[
                {
                    "description": "场景1",
                    "input": {"name": "test", "count": 5},
                    "output": {"result": "success"}
                }
            ]
        )
        
        examples = await _generate_usage_examples(tool_definition, request)
        
        assert len(examples) >= 1
        assert examples[0]["name"] == "基础使用示例"
        assert "name" in examples[0]["input"]
        assert "count" in examples[0]["input"]
        
        # 如果有场景示例，应该包含场景示例
        if len(examples) > 1:
            assert "场景示例" in examples[1]["name"]
    
    async def test_validate_generated_tool(self):
        """测试生成工具验证"""
        from backend.routers.tools import _validate_generated_tool, NaturalLanguageGenerationRequest
        
        # 完整的工具定义
        complete_tool = {
            "toolId": "test.tool.123",
            "displayName": "测试工具",
            "transport": "http",
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {"param1": {"type": "string"}}
            },
            "outputsSchema": {
                "type": "object",
                "properties": {"result": {"type": "string"}}
            },
            "descriptionUser": "这是一个详细的工具描述",
            "runtime": {"endpoint": "https://api.example.com"}
        }
        
        request = NaturalLanguageGenerationRequest(description="测试")
        result = await _validate_generated_tool(complete_tool, request)
        
        assert result["success"] is True
        assert len(result["checks"]) > 0
        assert len(result["errors"]) == 0
        
        # 不完整的工具定义
        incomplete_tool = {"displayName": "测试"}
        result = await _validate_generated_tool(incomplete_tool, request)
        
        assert result["success"] is False
        assert len(result["errors"]) > 0
    
    async def test_preview_generated_tool(self):
        """测试工具预览"""
        from backend.routers.tools import preview_generated_tool, ToolPreviewRequest
        
        request = ToolPreviewRequest(**SAMPLE_TOOL_PREVIEW_REQUEST)
        result = await preview_generated_tool(request)
        
        # 验证预览结果
        assert result.success is True
        assert result.preview_data is not None
        assert result.preview_data["tool_id"] == "http.weather_tool.abc123"
        assert result.preview_data["display_name"] == "天气查询工具"
        
        # 验证参数提取
        assert len(result.preview_data["input_parameters"]) == 2
        city_param = next(p for p in result.preview_data["input_parameters"] if p["name"] == "city")
        assert city_param["required"] is True
        assert city_param["type"] == "string"
        
        # 验证 Schema 文档
        assert result.schema_documentation is not None
        assert "天气查询工具" in result.schema_documentation
        assert "city" in result.schema_documentation
        
        # 验证交互式演示
        assert result.interactive_demo is not None
        assert "form_config" in result.interactive_demo
        assert len(result.interactive_demo["form_config"]) == 2
    
    def test_extract_input_parameters(self):
        """测试输入参数提取"""
        from backend.routers.tools import _extract_input_parameters
        
        tool_def = {
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "用户名称"
                    },
                    "age": {
                        "type": "integer",
                        "description": "用户年龄",
                        "default": 18
                    },
                    "active": {
                        "type": "boolean",
                        "description": "是否激活"
                    }
                },
                "required": ["name"]
            }
        }
        
        parameters = _extract_input_parameters(tool_def)
        
        assert len(parameters) == 3
        
        name_param = next(p for p in parameters if p["name"] == "name")
        assert name_param["type"] == "string"
        assert name_param["required"] is True
        assert name_param["description"] == "用户名称"
        
        age_param = next(p for p in parameters if p["name"] == "age")
        assert age_param["type"] == "integer"
        assert age_param["required"] is False
        assert age_param["default"] == 18
    
    def test_generate_schema_documentation(self):
        """测试 Schema 文档生成"""
        from backend.routers.tools import _generate_schema_documentation
        
        tool_def = {
            "displayName": "测试工具",
            "descriptionUser": "这是一个测试工具",
            "transport": "http",
            "category": "test",
            "inputsDeveloperSchema": {
                "type": "object",
                "properties": {
                    "param1": {"type": "string", "description": "参数1"},
                    "param2": {"type": "integer", "description": "参数2"}
                },
                "required": ["param1"]
            },
            "outputsSchema": {
                "type": "object",
                "properties": {
                    "result": {"type": "string", "description": "结果"}
                }
            }
        }
        
        docs = _generate_schema_documentation(tool_def)
        
        assert "测试工具" in docs
        assert "这是一个测试工具" in docs
        assert "param1" in docs
        assert "必需" in docs
        assert "可选" in docs
        assert "result" in docs
    
    def test_map_type_to_form_field(self):
        """测试类型到表单字段的映射"""
        from backend.routers.tools import _map_type_to_form_field
        
        type_mappings = [
            ("string", "text"),
            ("integer", "number"),
            ("number", "number"),
            ("boolean", "checkbox"),
            ("array", "textarea"),
            ("object", "textarea"),
            ("unknown", "text")
        ]
        
        for param_type, expected_field in type_mappings:
            result = _map_type_to_form_field(param_type)
            assert result == expected_field

if __name__ == "__main__":
    pytest.main([__file__])