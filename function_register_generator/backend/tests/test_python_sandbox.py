import pytest
import asyncio
import json
import tempfile
import os
from unittest.mock import AsyncMock, MagicMock, patch

from services.python_sandbox_service import (
    PythonSandboxService,
    PythonSandboxRequest,
    PythonSandboxConfig,
    PythonExecutionResult,
    get_python_sandbox_service
)


class TestPythonSandboxRequest:
    """测试 Python 沙盒请求模型"""
    
    def test_valid_request(self):
        """测试有效请求"""
        script = '''
def hello(name):
    return f"Hello, {name}!"
'''
        
        request = PythonSandboxRequest(
            script_content=script,
            entry_function="hello",
            function_args={"name": "World"},
            timeout=30
        )
        
        assert request.script_content == script
        assert request.entry_function == "hello"
        assert request.function_args["name"] == "World"
        assert request.timeout == 30
    
    def test_timeout_validation(self):
        """测试超时验证"""
        script = "def test(): pass"
        
        # 有效超时
        request = PythonSandboxRequest(
            script_content=script,
            entry_function="test",
            timeout=30
        )
        assert request.timeout == 30
        
        # 无效超时
        with pytest.raises(ValueError, match="超时时间必须在"):
            PythonSandboxRequest(
                script_content=script,
                entry_function="test",
                timeout=0
            )
        
        with pytest.raises(ValueError, match="超时时间必须在"):
            PythonSandboxRequest(
                script_content=script,
                entry_function="test",
                timeout=400
            )
    
    def test_script_content_validation(self):
        """测试脚本内容验证"""
        # 空脚本
        with pytest.raises(ValueError, match="脚本内容不能为空"):
            PythonSandboxRequest(
                script_content="",
                entry_function="test"
            )
        
        # 过大脚本
        large_script = "# " + "x" * 50000
        with pytest.raises(ValueError, match="脚本内容过大"):
            PythonSandboxRequest(
                script_content=large_script,
                entry_function="test"
            )
    
    def test_entry_function_validation(self):
        """测试入口函数验证"""
        script = "def test(): pass"
        
        # 有效函数名
        valid_names = ["test", "hello_world", "_private", "func123"]
        for name in valid_names:
            request = PythonSandboxRequest(
                script_content=script,
                entry_function=name
            )
            assert request.entry_function == name
        
        # 无效函数名
        invalid_names = ["", "123func", "func-name", "func.name"]
        for name in invalid_names:
            with pytest.raises(ValueError):
                PythonSandboxRequest(
                    script_content=script,
                    entry_function=name
                )


class TestPythonSandboxService:
    """测试 Python 沙盒服务"""
    
    @pytest.fixture
    def sandbox_service(self):
        """创建沙盒服务实例"""
        return PythonSandboxService()
    
    def test_static_security_check_safe_code(self, sandbox_service):
        """测试安全代码的静态检查"""
        safe_code = '''
import json
import math

def calculate(x, y):
    """安全的计算函数"""
    result = math.sqrt(x * x + y * y)
    return {"result": result, "type": "distance"}
'''
        
        warnings = sandbox_service._static_security_check(safe_code)
        assert len(warnings) == 0
    
    def test_static_security_check_dangerous_code(self, sandbox_service):
        """测试危险代码的静态检查"""
        dangerous_code = '''
import os
import subprocess

def dangerous_function():
    os.system("rm -rf /")
    subprocess.call(["curl", "http://evil.com"])
    eval("malicious_code")
    return "done"
'''
        
        warnings = sandbox_service._static_security_check(dangerous_code)
        assert len(warnings) > 0
        
        # 检查是否检测到特定的危险模式
        warning_text = " ".join(warnings)
        assert "os" in warning_text.lower() or "不允许" in warning_text
    
    def test_static_security_check_forbidden_imports(self, sandbox_service):
        """测试禁止导入的检查"""
        forbidden_code = '''
import socket
import threading
from ctypes import *

def network_function():
    return "network access"
'''
        
        warnings = sandbox_service._static_security_check(forbidden_code)
        assert len(warnings) > 0
        
        # 检查是否检测到禁止的导入
        warning_text = " ".join(warnings)
        assert "socket" in warning_text or "threading" in warning_text or "ctypes" in warning_text
    
    def test_check_ast_security(self, sandbox_service):
        """测试 AST 安全检查"""
        import ast
        
        # 危险代码的 AST
        dangerous_code = '''
import os
eval("print('hello')")
os.system("ls")
'''
        
        tree = ast.parse(dangerous_code)
        warnings = sandbox_service._check_ast_security(tree)
        
        assert len(warnings) > 0
        warning_text = " ".join(warnings)
        assert "os" in warning_text or "eval" in warning_text
    
    def test_check_imports(self, sandbox_service):
        """测试导入检查"""
        # 允许的导入
        allowed_code = '''
import json
import math
from datetime import datetime
'''
        warnings = sandbox_service._check_imports(allowed_code)
        assert len(warnings) == 0
        
        # 禁止的导入
        forbidden_code = '''
import socket
import subprocess
from os import system
'''
        warnings = sandbox_service._check_imports(forbidden_code)
        assert len(warnings) > 0
    
    def test_check_dangerous_strings(self, sandbox_service):
        """测试危险字符串检查"""
        # 安全代码
        safe_code = '''
def hello():
    return "Hello World"
'''
        warnings = sandbox_service._check_dangerous_strings(safe_code)
        assert len(warnings) == 0
        
        # 危险代码
        dangerous_code = '''
def dangerous():
    command = "rm -rf /"
    return command
'''
        warnings = sandbox_service._check_dangerous_strings(dangerous_code)
        assert len(warnings) > 0
    
    @pytest.mark.asyncio
    async def test_prepare_script(self, sandbox_service):
        """测试脚本准备"""
        request = PythonSandboxRequest(
            script_content="def hello(): return 'Hello'",
            entry_function="hello"
        )
        
        script_path = await sandbox_service._prepare_script(request)
        
        try:
            assert os.path.exists(script_path)
            
            with open(script_path, 'r') as f:
                content = f.read()
            
            assert "def hello(): return 'Hello'" in content
            assert "SANDBOX_RESULT_START" in content
            assert "SANDBOX_RESULT_END" in content
            assert "hello()" in content
            
        finally:
            if os.path.exists(script_path):
                os.unlink(script_path)
    
    @pytest.mark.asyncio
    async def test_prepare_script_with_args(self, sandbox_service):
        """测试带参数的脚本准备"""
        request = PythonSandboxRequest(
            script_content="def greet(name, age): return f'Hello {name}, age {age}'",
            entry_function="greet",
            function_args={"name": "Alice", "age": 30}
        )
        
        script_path = await sandbox_service._prepare_script(request)
        
        try:
            with open(script_path, 'r') as f:
                content = f.read()
            
            assert "greet(name='Alice', age=30)" in content
            
        finally:
            if os.path.exists(script_path):
                os.unlink(script_path)
    
    def test_parse_execution_output_success(self, sandbox_service):
        """测试成功执行输出解析"""
        stdout = '''
Some debug output
SANDBOX_RESULT_START
{"success": true, "result": "Hello World"}
SANDBOX_RESULT_END
More output
'''
        
        result = sandbox_service._parse_execution_output(stdout)
        assert result["success"] is True
        assert result["result"] == "Hello World"
    
    def test_parse_execution_output_error(self, sandbox_service):
        """测试错误执行输出解析"""
        stdout = '''
SANDBOX_RESULT_START
{"success": false, "error": "NameError: name 'undefined' is not defined"}
SANDBOX_RESULT_END
'''
        
        result = sandbox_service._parse_execution_output(stdout)
        assert result["success"] is False
        assert "NameError" in result["error"]
    
    def test_parse_execution_output_no_markers(self, sandbox_service):
        """测试无标记的输出解析"""
        stdout = "Just some regular output"
        
        result = sandbox_service._parse_execution_output(stdout)
        assert "raw_output" in result
        assert result["raw_output"] == stdout
    
    def test_get_resource_usage(self, sandbox_service):
        """测试资源使用统计"""
        stdout = "Some output"
        
        usage = sandbox_service._get_resource_usage(stdout)
        assert "output_size" in usage
        assert usage["output_size"] == len(stdout)
        assert usage["container_used"] is True
    
    @pytest.mark.asyncio
    async def test_validate_python_function_valid(self, sandbox_service):
        """测试有效 Python 函数验证"""
        script = '''
def calculate_area(length, width):
    """计算矩形面积"""
    return length * width

def helper_function():
    return "helper"
'''
        
        result = await sandbox_service.validate_python_function(script, "calculate_area")
        
        assert result["success"] is True
        assert result["function_found"] is True
        assert result["syntax_valid"] is True
        assert result["function_info"]["name"] == "calculate_area"
        assert "length" in result["function_info"]["args"]
        assert "width" in result["function_info"]["args"]
        assert result["function_info"]["docstring"] == "计算矩形面积"
    
    @pytest.mark.asyncio
    async def test_validate_python_function_not_found(self, sandbox_service):
        """测试函数不存在的验证"""
        script = '''
def existing_function():
    return "exists"
'''
        
        result = await sandbox_service.validate_python_function(script, "nonexistent_function")
        
        assert result["success"] is True
        assert result["function_found"] is False
        assert result["syntax_valid"] is True
    
    @pytest.mark.asyncio
    async def test_validate_python_function_syntax_error(self, sandbox_service):
        """测试语法错误的验证"""
        script = '''
def invalid_function(
    # 缺少参数和冒号
    return "invalid"
'''
        
        result = await sandbox_service.validate_python_function(script, "invalid_function")
        
        assert result["success"] is False
        assert result["syntax_valid"] is False
        assert "syntax_error" in result
    
    def test_generate_test_script(self, sandbox_service):
        """测试测试脚本生成"""
        function_info = {
            "name": "calculate_bmi",
            "args": ["weight", "height"]
        }
        
        test_script = sandbox_service.generate_test_script(function_info)
        
        assert "def test_calculate_bmi():" in test_script
        assert "calculate_bmi(" in test_script
        assert "weight=" in test_script
        assert "height=" in test_script
        assert "测试 calculate_bmi 函数" in test_script
    
    def test_generate_test_script_no_args(self, sandbox_service):
        """测试无参数函数的测试脚本生成"""
        function_info = {
            "name": "get_current_time",
            "args": []
        }
        
        test_script = sandbox_service.generate_test_script(function_info)
        
        assert "def test_get_current_time():" in test_script
        assert "get_current_time()" in test_script
        assert "测试 get_current_time 函数" in test_script


class TestPythonSandboxConfig:
    """测试 Python 沙盒配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = PythonSandboxConfig()
        
        assert config.DEFAULT_TIMEOUT == 30
        assert config.MAX_TIMEOUT == 300
        assert config.MEMORY_LIMIT == "128m"
        assert config.CPU_LIMIT == "0.5"
        assert "json" in config.ALLOWED_MODULES
        assert "math" in config.ALLOWED_MODULES
        assert "requests" in config.ALLOWED_MODULES
        assert len(config.FORBIDDEN_PATTERNS) > 0
        assert config.DOCKER_IMAGE == "python:3.11-slim"
    
    def test_allowed_modules(self):
        """测试允许的模块"""
        config = PythonSandboxConfig()
        
        # 标准库模块应该被允许
        standard_modules = ["json", "re", "math", "datetime", "time"]
        for module in standard_modules:
            assert module in config.ALLOWED_MODULES
        
        # 危险模块不应该被允许
        dangerous_modules = ["os", "sys", "subprocess", "socket"]
        for module in dangerous_modules:
            assert module not in config.ALLOWED_MODULES
    
    def test_forbidden_patterns(self):
        """测试禁止的模式"""
        config = PythonSandboxConfig()
        
        # 应该包含危险函数的模式
        patterns_text = " ".join(config.FORBIDDEN_PATTERNS)
        assert "eval" in patterns_text
        assert "exec" in patterns_text
        assert "__import__" in patterns_text
        assert "os\\." in patterns_text


class TestGlobalService:
    """测试全局服务获取"""
    
    @pytest.mark.asyncio
    async def test_get_python_sandbox_service(self):
        """测试获取全局服务实例"""
        service1 = await get_python_sandbox_service()
        service2 = await get_python_sandbox_service()
        
        # 应该返回同一个实例
        assert service1 is service2
        assert isinstance(service1, PythonSandboxService)


class TestComplexScenarios:
    """测试复杂场景"""
    
    @pytest.mark.asyncio
    async def test_complex_function_validation(self):
        """测试复杂函数验证"""
        complex_script = '''
import json
import math
from datetime import datetime

def process_data(items, config=None):
    """
    处理数据列表
    
    Args:
        items: 数据项列表
        config: 可选配置字典
    
    Returns:
        处理结果字典
    """
    if config is None:
        config = {"mode": "default"}
    
    results = []
    for item in items:
        if isinstance(item, (int, float)):
            processed = math.sqrt(abs(item))
        else:
            processed = str(item).upper()
        results.append(processed)
    
    return {
        "processed_items": results,
        "count": len(results),
        "timestamp": datetime.now().isoformat(),
        "config": config
    }

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, name):
        self.name = name
    
    def process(self, data):
        return f"{self.name}: {data}"
'''
        
        sandbox = PythonSandboxService()
        result = await sandbox.validate_python_function(complex_script, "process_data")
        
        assert result["success"] is True
        assert result["function_found"] is True
        assert result["syntax_valid"] is True
        
        func_info = result["function_info"]
        assert func_info["name"] == "process_data"
        assert "items" in func_info["args"]
        assert "config" in func_info["args"]
        assert "处理数据列表" in func_info["docstring"]
    
    def test_security_check_comprehensive(self):
        """测试综合安全检查"""
        malicious_script = '''
import os
import subprocess
import socket
from ctypes import *

def malicious_function():
    # 尝试执行系统命令
    os.system("rm -rf /")
    subprocess.call(["curl", "http://evil.com"])
    
    # 尝试网络访问
    s = socket.socket()
    s.connect(("evil.com", 80))
    
    # 尝试动态执行
    eval("__import__('os').system('ls')")
    exec("print('malicious')")
    
    # 尝试访问敏感文件
    with open("/etc/passwd", "r") as f:
        data = f.read()
    
    return "done"
'''
        
        sandbox = PythonSandboxService()
        warnings = sandbox._static_security_check(malicious_script)
        
        # 应该检测到多个安全问题
        assert len(warnings) > 5
        
        warning_text = " ".join(warnings).lower()
        assert "os" in warning_text or "不允许" in warning_text
        assert "subprocess" in warning_text or "不允许" in warning_text
        assert "socket" in warning_text or "不允许" in warning_text
    
    @pytest.mark.asyncio
    async def test_script_preparation_edge_cases(self):
        """测试脚本准备的边界情况"""
        sandbox = PythonSandboxService()
        
        # 测试特殊字符
        request = PythonSandboxRequest(
            script_content='def test(): return "Hello, 世界! 🌍"',
            entry_function="test"
        )
        
        script_path = await sandbox._prepare_script(request)
        
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            assert "世界" in content
            assert "🌍" in content
            
        finally:
            if os.path.exists(script_path):
                os.unlink(script_path)
    
    def test_output_parsing_edge_cases(self):
        """测试输出解析的边界情况"""
        sandbox = PythonSandboxService()
        
        # 测试包含特殊字符的输出
        stdout_with_unicode = '''
SANDBOX_RESULT_START
{"success": true, "result": "Hello, 世界! 🌍"}
SANDBOX_RESULT_END
'''
        
        result = sandbox._parse_execution_output(stdout_with_unicode)
        assert result["success"] is True
        assert "世界" in result["result"]
        assert "🌍" in result["result"]
        
        # 测试包含换行的输出
        stdout_with_newlines = '''
SANDBOX_RESULT_START
{"success": true, "result": "Line 1\\nLine 2\\nLine 3"}
SANDBOX_RESULT_END
'''
        
        result = sandbox._parse_execution_output(stdout_with_newlines)
        assert result["success"] is True
        assert "Line 1" in result["result"]


if __name__ == "__main__":
    pytest.main([__file__])