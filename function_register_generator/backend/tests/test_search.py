"""
搜索功能测试
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

# 测试数据
SAMPLE_SEARCH_PARAMS = {
    "q": "天气",
    "category": "weather",
    "transport": "http",
    "visibility": "public",
    "page": 1,
    "page_size": 10
}

SAMPLE_ADVANCED_SEARCH = {
    "query": "API工具",
    "categories": ["api", "weather"],
    "transports": ["http"],
    "visibility": "public",
    "tags": ["weather", "data"],
    "search_fields": ["displayName", "descriptionUser"],
    "fuzzy": True,
    "boost_exact_match": True,
    "sort_by": "relevance",
    "sort_order": "desc",
    "page": 1,
    "page_size": 20
}

MOCK_SEARCH_RESULTS = [
    {
        "toolId": "http.weather_api.v1",
        "displayName": "天气查询API",
        "category": "weather",
        "transport": "http",
        "visibility": "public",
        "descriptionUser": "获取实时天气信息",
        "tags": ["weather", "api"],
        "aliases": ["weather", "天气"],
        "_score": 8.5
    },
    {
        "toolId": "python.weather_parser.v1",
        "displayName": "天气数据解析器",
        "category": "data",
        "transport": "python",
        "visibility": "public",
        "descriptionUser": "解析天气数据格式",
        "tags": ["weather", "parser"],
        "aliases": ["parser"],
        "_score": 6.2
    }
]

class TestSearch:
    """搜索功能测试类"""
    
    @pytest.fixture
    def mock_repository(self):
        """模拟工具仓库"""
        repository = AsyncMock()
        repository.search = AsyncMock(return_value=MOCK_SEARCH_RESULTS)
        return repository
    
    @patch('backend.routers.search.get_tool_repository')
    async def test_basic_search_success(self, mock_get_repo, mock_repository):
        """测试基础搜索成功"""
        mock_get_repo.return_value = mock_repository
        
        from backend.routers.search import search_tools
        
        result = await search_tools(**SAMPLE_SEARCH_PARAMS)
        
        # 验证结果
        assert result.success is True
        assert result.query == "天气"
        assert len(result.results) > 0
        assert result.total > 0
        assert result.page == 1
        assert result.page_size == 10
        
        # 验证筛选条件
        assert result.filters["categories"] == ["weather"]
        assert result.filters["transports"] == ["http"]
        assert result.filters["visibility"] == "public"
    
    async def test_advanced_search_success(self):
        """测试高级搜索成功"""
        from backend.routers.search import advanced_search_tools, AdvancedSearchRequest
        
        request = AdvancedSearchRequest(**SAMPLE_ADVANCED_SEARCH)
        result = await advanced_search_tools(request)
        
        # 验证结果
        assert result.success is True
        assert result.query == "API工具"
        assert isinstance(result.results, list)
        assert result.page == 1
        assert result.page_size == 20
        
        # 验证筛选条件
        assert result.filters["categories"] == ["api", "weather"]
        assert result.filters["transports"] == ["http"]
    
    async def test_search_with_mock_data(self):
        """测试使用 mock 数据搜索"""
        from backend.routers.search import _search_mock_data, AdvancedSearchRequest
        
        request = AdvancedSearchRequest(
            query="天气",
            page=1,
            page_size=5
        )
        
        results, total, took = await _search_mock_data(request)
        
        # 验证结果
        assert isinstance(results, list)
        assert isinstance(total, int)
        assert isinstance(took, int)
        assert took >= 0
        
        # 验证分页
        assert len(results) <= 5
    
    def test_calculate_relevance_score(self):
        """测试相关性分数计算"""
        from backend.routers.search import _calculate_relevance_score
        
        tool = {
            "displayName": "天气查询工具",
            "descriptionUser": "获取实时天气信息",
            "aliases": ["weather", "天气"],
            "tags": ["weather", "api"],
            "category": "weather"
        }
        
        # 精确匹配工具名称
        score1 = _calculate_relevance_score(tool, "天气查询工具")
        assert score1 >= 10.0
        
        # 部分匹配工具名称
        score2 = _calculate_relevance_score(tool, "天气")
        assert score2 > 0
        assert score2 < score1
        
        # 别名匹配
        score3 = _calculate_relevance_score(tool, "weather")
        assert score3 > 0
        
        # 无匹配
        score4 = _calculate_relevance_score(tool, "无关内容")
        assert score4 == 0.0
    
    def test_get_field_boost(self):
        """测试字段权重获取"""
        from backend.routers.search import _get_field_boost
        
        # 测试不同字段的权重
        assert _get_field_boost("displayName") == 3.0
        assert _get_field_boost("aliases") == 2.5
        assert _get_field_boost("descriptionUser") == 2.0
        assert _get_field_boost("category") == 1.5
        assert _get_field_boost("tags") == 1.2
        assert _get_field_boost("content") == 1.0
        assert _get_field_boost("unknown_field") == 1.0
    
    def test_build_sort_config(self):
        """测试排序配置构建"""
        from backend.routers.search import _build_sort_config
        
        # 相关性排序
        relevance_sort = _build_sort_config("relevance", "desc")
        assert relevance_sort == [{"_score": {"order": "desc"}}]
        
        # 名称排序
        name_sort = _build_sort_config("name", "asc")
        assert name_sort == [{"displayName.keyword": {"order": "asc"}}, "_score"]
        
        # 日期排序
        date_sort = _build_sort_config("date", "desc")
        assert date_sort == [{"updatedTime": {"order": "desc"}}, "_score"]
        
        # 未知排序类型，应该返回默认相关性排序
        unknown_sort = _build_sort_config("unknown", "desc")
        assert unknown_sort == [{"_score": {"order": "desc"}}]
    
    async def test_build_search_query(self):
        """测试搜索查询构建"""
        from backend.routers.search import _build_search_query, AdvancedSearchRequest
        
        request = AdvancedSearchRequest(
            query="测试查询",
            categories=["test"],
            transports=["http"],
            visibility="public",
            tags=["demo"],
            fuzzy=True,
            boost_exact_match=True,
            sort_by="relevance",
            sort_order="desc",
            page=1,
            page_size=10
        )
        
        query_body = await _build_search_query(request)
        
        # 验证查询结构
        assert "query" in query_body
        assert "bool" in query_body["query"]
        assert "should" in query_body["query"]["bool"]
        assert "filter" in query_body["query"]["bool"]
        
        # 验证分页
        assert query_body["from"] == 0
        assert query_body["size"] == 10
        
        # 验证筛选条件
        filters = query_body["query"]["bool"]["filter"]
        assert any(f.get("terms", {}).get("category") == ["test"] for f in filters)
        assert any(f.get("terms", {}).get("transport") == ["http"] for f in filters)
        assert any(f.get("term", {}).get("visibility") == "public" for f in filters)
    
    def test_build_aggregations(self):
        """测试聚合查询构建"""
        from backend.routers.search import _build_aggregations
        
        aggs = _build_aggregations()
        
        # 验证聚合结构
        assert "categories" in aggs
        assert "transports" in aggs
        assert "visibility" in aggs
        assert "tags" in aggs
        assert "created_date_histogram" in aggs
        
        # 验证聚合配置
        assert aggs["categories"]["terms"]["field"] == "category"
        assert aggs["transports"]["terms"]["field"] == "transport"
        assert aggs["visibility"]["terms"]["field"] == "visibility"
    
    async def test_get_search_suggestions(self):
        """测试搜索建议获取"""
        from backend.routers.search import get_search_suggestions
        
        result = await get_search_suggestions(q="天", limit=5)
        
        # 验证结果
        assert isinstance(result, list)
        assert len(result) <= 5
        
        # 验证建议结构
        if result:
            suggestion = result[0]
            assert hasattr(suggestion, 'text')
            assert hasattr(suggestion, 'type')
            assert hasattr(suggestion, 'score')
    
    async def test_generate_search_suggestions(self):
        """测试搜索建议生成"""
        from backend.routers.search import _generate_search_suggestions
        
        suggestions = await _generate_search_suggestions("天", 10, ["tool_name", "category"])
        
        # 验证结果
        assert isinstance(suggestions, list)
        assert len(suggestions) <= 10
        
        # 验证建议类型
        if suggestions:
            types = [s.type for s in suggestions]
            assert all(t in ["tool_name", "category", "tag", "alias"] for t in types)
    
    def test_calculate_suggestion_score(self):
        """测试建议分数计算"""
        from backend.routers.search import _calculate_suggestion_score
        
        # 精确匹配
        score1 = _calculate_suggestion_score("天气", "天气")
        assert score1 == 10.0
        
        # 前缀匹配
        score2 = _calculate_suggestion_score("天气查询", "天气")
        assert score2 == 8.0
        
        # 包含匹配
        score3 = _calculate_suggestion_score("实时天气信息", "天气")
        assert score3 == 5.0
        
        # 无匹配
        score4 = _calculate_suggestion_score("无关内容", "天气")
        assert score4 == 1.0
    
    def test_generate_cache_key(self):
        """测试缓存键生成"""
        from backend.routers.search import _generate_cache_key
        
        filters1 = {"category": "test", "transport": "http"}
        filters2 = {"transport": "http", "category": "test"}  # 顺序不同
        
        key1 = _generate_cache_key("query", filters1)
        key2 = _generate_cache_key("query", filters2)
        
        # 相同内容应该生成相同的键
        assert key1 == key2
        assert key1.startswith("search:")
        
        # 不同查询应该生成不同的键
        key3 = _generate_cache_key("different", filters1)
        assert key1 != key3
    
    async def test_get_search_filters(self):
        """测试搜索筛选选项获取"""
        from backend.routers.search import get_search_filters
        
        result = await get_search_filters()
        
        # 验证结果
        assert result["success"] is True
        assert "filters" in result
        
        filters = result["filters"]
        assert "categories" in filters
        assert "transports" in filters
        assert "visibilities" in filters
        assert "tags" in filters
        
        # 验证筛选选项结构
        if filters["categories"]:
            category = filters["categories"][0]
            assert "name" in category
            assert "count" in category
    
    async def test_get_popular_searches(self):
        """测试热门搜索获取"""
        from backend.routers.search import get_popular_searches
        
        result = await get_popular_searches(limit=5)
        
        # 验证结果
        assert result["success"] is True
        assert "popular_searches" in result
        assert len(result["popular_searches"]) <= 5
        
        # 验证搜索项结构
        if result["popular_searches"]:
            search_item = result["popular_searches"][0]
            assert "query" in search_item
            assert "count" in search_item
    
    async def test_get_recent_searches(self):
        """测试最近搜索获取"""
        from backend.routers.search import get_recent_searches
        
        result = await get_recent_searches(limit=3)
        
        # 验证结果
        assert result["success"] is True
        assert "recent_searches" in result
        assert len(result["recent_searches"]) <= 3
        
        # 验证搜索项结构
        if result["recent_searches"]:
            search_item = result["recent_searches"][0]
            assert "query" in search_item
            assert "timestamp" in search_item
    
    async def test_clear_search_cache(self):
        """测试清除搜索缓存"""
        from backend.routers.search import clear_search_cache
        
        result = await clear_search_cache()
        
        # 验证结果
        assert result["success"] is True
        assert "message" in result
    
    async def test_search_with_date_filters(self):
        """测试带日期筛选的搜索"""
        from backend.routers.search import _build_search_query, AdvancedSearchRequest
        
        now = datetime.now()
        yesterday = now - timedelta(days=1)
        
        request = AdvancedSearchRequest(
            query="测试",
            created_after=yesterday,
            created_before=now,
            page=1,
            page_size=10
        )
        
        query_body = await _build_search_query(request)
        
        # 验证日期筛选
        filters = query_body["query"]["bool"]["filter"]
        date_filters = [f for f in filters if "range" in f]
        assert len(date_filters) > 0
        
        # 验证日期范围
        created_filter = next((f for f in date_filters if "createdTime" in f["range"]), None)
        assert created_filter is not None
        assert "gte" in created_filter["range"]["createdTime"]
        assert "lte" in created_filter["range"]["createdTime"]
    
    async def test_search_pagination(self):
        """测试搜索分页"""
        from backend.routers.search import _search_mock_data, AdvancedSearchRequest
        
        # 第一页
        request1 = AdvancedSearchRequest(query="test", page=1, page_size=2)
        results1, total1, _ = await _search_mock_data(request1)
        
        # 第二页
        request2 = AdvancedSearchRequest(query="test", page=2, page_size=2)
        results2, total2, _ = await _search_mock_data(request2)
        
        # 验证分页
        assert total1 == total2  # 总数应该相同
        assert len(results1) <= 2
        assert len(results2) <= 2
        
        # 验证不同页的结果不同（如果有足够的数据）
        if len(results1) == 2 and len(results2) > 0:
            assert results1[0]["toolId"] != results2[0]["toolId"]

if __name__ == "__main__":
    pytest.main([__file__])