from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

from config import settings
from database import init_elasticsearch
from routers import tools, search, sandbox
from mock_data import create_mock_tools

load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_elasticsearch()
    # Create mock data for demonstration
    await create_mock_tools()
    yield
    # Shutdown
    pass

app = FastAPI(
    title="工具注册平台 API",
    description="企业内部工具注册和发现平台",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(tools.router, prefix="/api/v1/tools", tags=["tools"])
app.include_router(search.router, prefix="/api/v1/search", tags=["search"])
app.include_router(sandbox.router, prefix="/api/v1/sandbox", tags=["sandbox"])

@app.get("/")
async def root():
    return {
        "message": "工具注册平台 API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)