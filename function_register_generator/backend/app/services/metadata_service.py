import json
import re
from typing import Dict, Any, List, Optional, Set
from datetime import datetime

from app.models.tool import Tool
from app.core.logging import logger

class MetadataService:
    """Service for optimizing and enhancing tool metadata."""
    
    @staticmethod
    def enhance_schema(schema: Dict[str, Any], tool: Tool) -> Dict[str, Any]:
        """
        Enhance a JSON schema with better descriptions, examples, and constraints.
        
        Args:
            schema: The original JSON schema
            tool: The tool this schema belongs to
            
        Returns:
            Enhanced schema with improved metadata
        """
        try:
            enhanced = schema.copy()
            
            # Add schema metadata
            enhanced["$schema"] = "http://json-schema.org/draft-07/schema#"
            enhanced["title"] = f"{tool.name} Input Schema"
            enhanced["description"] = tool.description or f"Input parameters for {tool.name}"
            
            # Enhance properties
            if "properties" in enhanced:
                enhanced["properties"] = MetadataService._enhance_properties(
                    enhanced["properties"], 
                    tool.category,
                    tool.tags_list
                )
            
            # Add examples at schema level
            enhanced["examples"] = MetadataService._generate_schema_examples(enhanced, tool)
            
            # Add metadata section
            enhanced["metadata"] = {
                "tool_id": tool.id,
                "tool_name": tool.name,
                "category": tool.category,
                "tags": tool.tags_list,
                "version": tool.version,
                "last_updated": tool.updated_at.isoformat() if tool.updated_at else None,
                "usage_hints": MetadataService._generate_usage_hints(enhanced, tool),
                "validation_rules": MetadataService._extract_validation_rules(enhanced)
            }
            
            return enhanced
            
        except Exception as e:
            logger.error(f"Failed to enhance schema for tool {tool.id}: {str(e)}")
            return schema
    
    @staticmethod
    def _enhance_properties(
        properties: Dict[str, Any], 
        category: str, 
        tags: List[str]
    ) -> Dict[str, Any]:
        """Enhance individual properties with better descriptions and examples."""
        enhanced_props = {}
        
        for prop_name, prop_def in properties.items():
            enhanced_prop = prop_def.copy()
            
            # Enhance description
            if "description" not in enhanced_prop or not enhanced_prop["description"]:
                enhanced_prop["description"] = MetadataService._generate_property_description(
                    prop_name, prop_def, category, tags
                )
            
            # Add examples
            if "examples" not in enhanced_prop:
                enhanced_prop["examples"] = MetadataService._generate_property_examples(
                    prop_name, prop_def, category, tags
                )
            
            # Add format hints
            enhanced_prop["format_hints"] = MetadataService._generate_format_hints(
                prop_name, prop_def
            )
            
            # Enhance enum descriptions
            if "enum" in enhanced_prop:
                enhanced_prop["enum_descriptions"] = MetadataService._generate_enum_descriptions(
                    prop_name, enhanced_prop["enum"]
                )
            
            # Add validation messages
            enhanced_prop["validation_messages"] = MetadataService._generate_validation_messages(
                prop_name, enhanced_prop
            )
            
            enhanced_props[prop_name] = enhanced_prop
        
        return enhanced_props
    
    @staticmethod
    def _generate_property_description(
        prop_name: str, 
        prop_def: Dict[str, Any], 
        category: str, 
        tags: List[str]
    ) -> str:
        """Generate a descriptive description for a property."""
        prop_type = prop_def.get("type", "unknown")
        
        # Common property name patterns
        name_patterns = {
            r".*url.*": "URL or web address",
            r".*email.*": "Email address",
            r".*phone.*": "Phone number",
            r".*date.*": "Date in ISO format (YYYY-MM-DD)",
            r".*time.*": "Time in ISO format",
            r".*id.*": "Unique identifier",
            r".*name.*": "Name or title",
            r".*description.*": "Detailed description",
            r".*count.*": "Number or count",
            r".*limit.*": "Maximum limit or threshold",
            r".*offset.*": "Starting position or offset",
            r".*page.*": "Page number for pagination",
            r".*size.*": "Size or dimension",
            r".*format.*": "Output format specification",
            r".*type.*": "Type or category specification",
            r".*filter.*": "Filter criteria",
            r".*query.*": "Search query or criteria",
            r".*sort.*": "Sorting specification",
            r".*order.*": "Ordering specification",
        }
        
        # Check for pattern matches
        for pattern, description in name_patterns.items():
            if re.match(pattern, prop_name.lower()):
                return f"{description} ({prop_type})"
        
        # Category-specific descriptions
        if category == "api":
            if "endpoint" in prop_name.lower():
                return f"API endpoint path ({prop_type})"
            elif "method" in prop_name.lower():
                return f"HTTP method (GET, POST, PUT, DELETE)"
        elif category == "data":
            if "table" in prop_name.lower():
                return f"Database table name ({prop_type})"
            elif "column" in prop_name.lower():
                return f"Database column name ({prop_type})"
        
        # Fallback description
        return f"{prop_name.replace('_', ' ').title()} ({prop_type})"
    
    @staticmethod
    def _generate_property_examples(
        prop_name: str, 
        prop_def: Dict[str, Any], 
        category: str, 
        tags: List[str]
    ) -> List[Any]:
        """Generate example values for a property."""
        prop_type = prop_def.get("type", "string")
        
        # Type-specific examples
        if prop_type == "string":
            if "email" in prop_name.lower():
                return ["<EMAIL>", "<EMAIL>"]
            elif "url" in prop_name.lower():
                return ["https://api.example.com", "https://www.example.org"]
            elif "name" in prop_name.lower():
                return ["John Doe", "Example Tool"]
            elif "id" in prop_name.lower():
                return ["abc123", "tool_456"]
            elif "format" in prop_name.lower():
                return ["json", "xml", "csv"]
            else:
                return ["example_value", "sample_text"]
        
        elif prop_type == "integer":
            if "count" in prop_name.lower() or "limit" in prop_name.lower():
                return [10, 50, 100]
            elif "page" in prop_name.lower():
                return [1, 2, 5]
            elif "offset" in prop_name.lower():
                return [0, 10, 20]
            else:
                return [1, 42, 100]
        
        elif prop_type == "number":
            return [1.0, 3.14, 100.5]
        
        elif prop_type == "boolean":
            return [True, False]
        
        elif prop_type == "array":
            item_type = prop_def.get("items", {}).get("type", "string")
            if item_type == "string":
                return [["item1", "item2"], ["tag1", "tag2", "tag3"]]
            elif item_type == "integer":
                return [[1, 2, 3], [10, 20, 30]]
            else:
                return [["value1", "value2"]]
        
        return ["example"]
    
    @staticmethod
    def _generate_format_hints(prop_name: str, prop_def: Dict[str, Any]) -> Dict[str, str]:
        """Generate format hints for better user experience."""
        hints = {}
        prop_type = prop_def.get("type", "string")
        
        if prop_type == "string":
            if "email" in prop_name.lower():
                hints["pattern"] = "Valid email format (<EMAIL>)"
                hints["input_type"] = "email"
            elif "url" in prop_name.lower():
                hints["pattern"] = "Valid URL format (https://example.com)"
                hints["input_type"] = "url"
            elif "date" in prop_name.lower():
                hints["pattern"] = "ISO date format (YYYY-MM-DD)"
                hints["input_type"] = "date"
            elif "time" in prop_name.lower():
                hints["pattern"] = "ISO time format (HH:MM:SS)"
                hints["input_type"] = "time"
            elif "phone" in prop_name.lower():
                hints["pattern"] = "Phone number format"
                hints["input_type"] = "tel"
            
            # Add length hints
            if "minLength" in prop_def:
                hints["min_length"] = f"Minimum {prop_def['minLength']} characters"
            if "maxLength" in prop_def:
                hints["max_length"] = f"Maximum {prop_def['maxLength']} characters"
        
        elif prop_type in ["integer", "number"]:
            if "minimum" in prop_def:
                hints["min_value"] = f"Minimum value: {prop_def['minimum']}"
            if "maximum" in prop_def:
                hints["max_value"] = f"Maximum value: {prop_def['maximum']}"
            
            hints["input_type"] = "number"
        
        return hints
    
    @staticmethod
    def _generate_enum_descriptions(prop_name: str, enum_values: List[Any]) -> Dict[str, str]:
        """Generate descriptions for enum values."""
        descriptions = {}
        
        for value in enum_values:
            if isinstance(value, str):
                # Generate description based on value
                if value.lower() in ["get", "post", "put", "delete", "patch"]:
                    descriptions[value] = f"HTTP {value.upper()} method"
                elif value.lower() in ["json", "xml", "csv", "yaml"]:
                    descriptions[value] = f"{value.upper()} format"
                elif value.lower() in ["asc", "desc", "ascending", "descending"]:
                    descriptions[value] = f"{value.title()} order"
                else:
                    descriptions[value] = value.replace("_", " ").title()
            else:
                descriptions[str(value)] = str(value)
        
        return descriptions
    
    @staticmethod
    def _generate_validation_messages(prop_name: str, prop_def: Dict[str, Any]) -> Dict[str, str]:
        """Generate user-friendly validation error messages."""
        messages = {}
        
        if "required" in prop_def:
            messages["required"] = f"{prop_name.replace('_', ' ').title()} is required"
        
        if prop_def.get("type") == "string":
            if "minLength" in prop_def:
                messages["minLength"] = f"Must be at least {prop_def['minLength']} characters long"
            if "maxLength" in prop_def:
                messages["maxLength"] = f"Must be no more than {prop_def['maxLength']} characters long"
            if "pattern" in prop_def:
                messages["pattern"] = f"Must match the required format"
        
        elif prop_def.get("type") in ["integer", "number"]:
            if "minimum" in prop_def:
                messages["minimum"] = f"Must be at least {prop_def['minimum']}"
            if "maximum" in prop_def:
                messages["maximum"] = f"Must be no more than {prop_def['maximum']}"
        
        if "enum" in prop_def:
            messages["enum"] = f"Must be one of: {', '.join(map(str, prop_def['enum']))}"
        
        return messages
    
    @staticmethod
    def _generate_schema_examples(schema: Dict[str, Any], tool: Tool) -> List[Dict[str, Any]]:
        """Generate complete examples for the entire schema."""
        examples = []
        
        if "properties" not in schema:
            return examples
        
        # Generate a basic example
        basic_example = {}
        required_fields = schema.get("required", [])
        
        for prop_name, prop_def in schema["properties"].items():
            if prop_name in required_fields or len(examples) == 0:
                prop_examples = prop_def.get("examples", [])
                if prop_examples:
                    basic_example[prop_name] = prop_examples[0]
                else:
                    # Generate a simple example based on type
                    prop_type = prop_def.get("type", "string")
                    if prop_type == "string":
                        basic_example[prop_name] = "example"
                    elif prop_type == "integer":
                        basic_example[prop_name] = 1
                    elif prop_type == "number":
                        basic_example[prop_name] = 1.0
                    elif prop_type == "boolean":
                        basic_example[prop_name] = True
                    elif prop_type == "array":
                        basic_example[prop_name] = []
                    elif prop_type == "object":
                        basic_example[prop_name] = {}
        
        if basic_example:
            examples.append(basic_example)
        
        return examples
    
    @staticmethod
    def _generate_usage_hints(schema: Dict[str, Any], tool: Tool) -> List[str]:
        """Generate usage hints for the tool."""
        hints = []
        
        # Category-specific hints
        if tool.category == "api":
            hints.append("This tool makes HTTP API calls - ensure you have proper network access")
        elif tool.category == "data":
            hints.append("This tool processes data - verify input data format and size limits")
        elif tool.category == "ai":
            hints.append("This tool uses AI models - results may vary and should be validated")
        
        # Schema-specific hints
        if "properties" in schema:
            required_count = len(schema.get("required", []))
            total_count = len(schema["properties"])
            
            if required_count == total_count:
                hints.append("All parameters are required for this tool")
            elif required_count > 0:
                hints.append(f"{required_count} out of {total_count} parameters are required")
            else:
                hints.append("All parameters are optional")
        
        # Authentication hint
        if tool.requires_auth:
            hints.append("Authentication is required to use this tool")
        
        return hints
    
    @staticmethod
    def _extract_validation_rules(schema: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract validation rules for documentation."""
        rules = []
        
        if "properties" not in schema:
            return rules
        
        for prop_name, prop_def in schema["properties"].items():
            prop_rules = {
                "property": prop_name,
                "type": prop_def.get("type", "unknown"),
                "required": prop_name in schema.get("required", []),
                "constraints": []
            }
            
            # Extract constraints
            if "minLength" in prop_def:
                prop_rules["constraints"].append(f"Minimum length: {prop_def['minLength']}")
            if "maxLength" in prop_def:
                prop_rules["constraints"].append(f"Maximum length: {prop_def['maxLength']}")
            if "minimum" in prop_def:
                prop_rules["constraints"].append(f"Minimum value: {prop_def['minimum']}")
            if "maximum" in prop_def:
                prop_rules["constraints"].append(f"Maximum value: {prop_def['maximum']}")
            if "pattern" in prop_def:
                prop_rules["constraints"].append(f"Pattern: {prop_def['pattern']}")
            if "enum" in prop_def:
                prop_rules["constraints"].append(f"Allowed values: {', '.join(map(str, prop_def['enum']))}")
            
            rules.append(prop_rules)
        
        return rules
    
    @staticmethod
    def generate_tool_dependencies(tool: Tool) -> Dict[str, Any]:
        """Analyze and generate tool dependency information."""
        dependencies = {
            "runtime_dependencies": [],
            "data_dependencies": [],
            "service_dependencies": [],
            "suggested_combinations": []
        }
        
        # Analyze based on category and transport
        if tool.transport == "http":
            dependencies["service_dependencies"].append({
                "type": "http_service",
                "description": "Requires HTTP connectivity",
                "critical": True
            })
        
        if tool.category == "data":
            dependencies["data_dependencies"].append({
                "type": "data_source",
                "description": "Requires access to data sources",
                "critical": True
            })
        
        # Analyze input schema for dependencies
        schema = tool.input_schema_dict
        if schema and "properties" in schema:
            for prop_name, prop_def in schema["properties"].items():
                if "url" in prop_name.lower():
                    dependencies["service_dependencies"].append({
                        "type": "external_url",
                        "description": f"May require access to external URLs via {prop_name}",
                        "critical": False
                    })
        
        return dependencies
