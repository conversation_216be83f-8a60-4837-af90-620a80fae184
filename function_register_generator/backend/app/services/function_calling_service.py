import json
import time
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models.tool import Tool
from app.models.function_call import FunctionCall
from app.core.logging import logger
from app.services.execution_service import ExecutionService

class FunctionCallingService:
    """Service for handling OpenAI Function Calling integration."""
    
    @staticmethod
    def tool_to_function_schema(tool: Tool) -> Dict[str, Any]:
        """
        Convert a Tool model to OpenAI Function Calling schema format.
        
        Args:
            tool: The tool to convert
            
        Returns:
            Dict containing the function schema in OpenAI format
        """
        try:
            # Parse the input schema
            input_schema = json.loads(tool.input_schema) if isinstance(tool.input_schema, str) else tool.input_schema
            
            # Build the function schema
            function_schema = {
                "name": tool.name.replace(" ", "_").replace("-", "_").lower(),
                "description": tool.description or f"Execute {tool.name} tool",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
            
            # Add properties from input schema
            if input_schema and "properties" in input_schema:
                function_schema["parameters"]["properties"] = input_schema["properties"]
                
                # Add required fields
                if "required" in input_schema:
                    function_schema["parameters"]["required"] = input_schema["required"]
                
                # Enhance descriptions for better AI understanding
                for prop_name, prop_def in input_schema["properties"].items():
                    if isinstance(prop_def, dict):
                        # Add examples if available
                        if "examples" in prop_def:
                            prop_def["description"] = f"{prop_def.get('description', '')} Examples: {', '.join(map(str, prop_def['examples']))}"
                        
                        # Add enum descriptions
                        if "enum" in prop_def:
                            prop_def["description"] = f"{prop_def.get('description', '')} Allowed values: {', '.join(map(str, prop_def['enum']))}"
            
            # Add metadata for better context
            function_schema["metadata"] = {
                "tool_id": tool.id,
                "category": tool.category,
                "tags": tool.tags or [],
                "version": tool.version or "1.0.0",
                "requires_auth": tool.requires_auth,
                "transport": tool.transport
            }
            
            return function_schema
            
        except Exception as e:
            logger.error(f"Failed to convert tool {tool.id} to function schema: {str(e)}")
            # Return a basic schema as fallback
            return {
                "name": tool.name.replace(" ", "_").replace("-", "_").lower(),
                "description": tool.description or f"Execute {tool.name} tool",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                },
                "metadata": {
                    "tool_id": tool.id,
                    "error": "Failed to parse input schema"
                }
            }
    
    @staticmethod
    async def execute_function_call(
        tool: Tool,
        arguments: Dict[str, Any],
        user_id: Optional[str] = None,
        db: Session = None
    ) -> Dict[str, Any]:
        """
        Execute a function call for a tool.
        
        Args:
            tool: The tool to execute
            arguments: The arguments to pass to the tool
            user_id: The ID of the user making the call
            db: Database session for logging
            
        Returns:
            Dict containing the execution result
        """
        call_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Validate arguments against schema
            validation_result = await FunctionCallingService._validate_arguments(tool, arguments)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "result": None,
                    "execution_time": time.time() - start_time,
                    "error": f"Invalid arguments: {validation_result['error']}",
                    "call_id": call_id
                }
            
            # Execute the tool
            execution_result = await ExecutionService.execute_tool(
                tool=tool,
                parameters=arguments,
                user_id=user_id
            )
            
            execution_time = time.time() - start_time
            
            # Log the function call
            if db:
                await FunctionCallingService._log_function_call(
                    db=db,
                    tool_id=tool.id,
                    user_id=user_id,
                    arguments=arguments,
                    result=execution_result,
                    execution_time=execution_time,
                    call_id=call_id,
                    success=execution_result.get("success", False)
                )
            
            return {
                "success": execution_result.get("success", False),
                "result": execution_result.get("result"),
                "execution_time": execution_time,
                "error": execution_result.get("error"),
                "call_id": call_id
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)
            
            logger.error(f"Function call failed for tool {tool.id}: {error_msg}")
            
            # Log the failed call
            if db:
                await FunctionCallingService._log_function_call(
                    db=db,
                    tool_id=tool.id,
                    user_id=user_id,
                    arguments=arguments,
                    result=None,
                    execution_time=execution_time,
                    call_id=call_id,
                    success=False,
                    error=error_msg
                )
            
            return {
                "success": False,
                "result": None,
                "execution_time": execution_time,
                "error": error_msg,
                "call_id": call_id
            }
    
    @staticmethod
    async def _validate_arguments(tool: Tool, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Validate arguments against tool's input schema."""
        try:
            input_schema = json.loads(tool.input_schema) if isinstance(tool.input_schema, str) else tool.input_schema
            
            if not input_schema or "properties" not in input_schema:
                return {"valid": True}  # No schema to validate against
            
            # Check required fields
            required_fields = input_schema.get("required", [])
            for field in required_fields:
                if field not in arguments:
                    return {
                        "valid": False,
                        "error": f"Missing required field: {field}"
                    }
            
            # Validate field types and constraints
            properties = input_schema["properties"]
            for field_name, field_value in arguments.items():
                if field_name not in properties:
                    continue  # Allow extra fields
                
                field_schema = properties[field_name]
                validation_error = FunctionCallingService._validate_field(
                    field_name, field_value, field_schema
                )
                if validation_error:
                    return {
                        "valid": False,
                        "error": validation_error
                    }
            
            return {"valid": True}
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Schema validation error: {str(e)}"
            }
    
    @staticmethod
    def _validate_field(field_name: str, value: Any, schema: Dict[str, Any]) -> Optional[str]:
        """Validate a single field against its schema."""
        field_type = schema.get("type")
        
        # Type validation
        if field_type == "string" and not isinstance(value, str):
            return f"Field {field_name} must be a string"
        elif field_type == "number" and not isinstance(value, (int, float)):
            return f"Field {field_name} must be a number"
        elif field_type == "integer" and not isinstance(value, int):
            return f"Field {field_name} must be an integer"
        elif field_type == "boolean" and not isinstance(value, bool):
            return f"Field {field_name} must be a boolean"
        elif field_type == "array" and not isinstance(value, list):
            return f"Field {field_name} must be an array"
        elif field_type == "object" and not isinstance(value, dict):
            return f"Field {field_name} must be an object"
        
        # Enum validation
        if "enum" in schema and value not in schema["enum"]:
            return f"Field {field_name} must be one of: {', '.join(map(str, schema['enum']))}"
        
        # String length validation
        if field_type == "string":
            if "minLength" in schema and len(value) < schema["minLength"]:
                return f"Field {field_name} must be at least {schema['minLength']} characters"
            if "maxLength" in schema and len(value) > schema["maxLength"]:
                return f"Field {field_name} must be at most {schema['maxLength']} characters"
        
        # Number range validation
        if field_type in ["number", "integer"]:
            if "minimum" in schema and value < schema["minimum"]:
                return f"Field {field_name} must be at least {schema['minimum']}"
            if "maximum" in schema and value > schema["maximum"]:
                return f"Field {field_name} must be at most {schema['maximum']}"
        
        return None
    
    @staticmethod
    async def _log_function_call(
        db: Session,
        tool_id: str,
        user_id: Optional[str],
        arguments: Dict[str, Any],
        result: Any,
        execution_time: float,
        call_id: str,
        success: bool,
        error: Optional[str] = None
    ):
        """Log a function call to the database."""
        try:
            function_call = FunctionCall(
                id=call_id,
                tool_id=tool_id,
                user_id=user_id,
                arguments=json.dumps(arguments),
                result=json.dumps(result) if result is not None else None,
                execution_time=execution_time,
                success=success,
                error=error,
                created_at=datetime.utcnow()
            )
            
            db.add(function_call)
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to log function call: {str(e)}")
            db.rollback()
    
    @staticmethod
    async def get_call_stats(db: Session, tool_id: str) -> Dict[str, Any]:
        """Get call statistics for a specific tool."""
        try:
            # Get basic stats
            total_calls = db.query(func.count(FunctionCall.id)).filter(
                FunctionCall.tool_id == tool_id
            ).scalar() or 0
            
            successful_calls = db.query(func.count(FunctionCall.id)).filter(
                and_(FunctionCall.tool_id == tool_id, FunctionCall.success == True)
            ).scalar() or 0
            
            avg_execution_time = db.query(func.avg(FunctionCall.execution_time)).filter(
                FunctionCall.tool_id == tool_id
            ).scalar() or 0.0
            
            last_call = db.query(FunctionCall.created_at).filter(
                FunctionCall.tool_id == tool_id
            ).order_by(FunctionCall.created_at.desc()).first()
            
            success_rate = (successful_calls / total_calls) if total_calls > 0 else 0.0
            
            return {
                "tool_id": tool_id,
                "total_calls": total_calls,
                "success_rate": success_rate,
                "avg_execution_time": float(avg_execution_time),
                "last_called": last_call[0].isoformat() if last_call else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get call stats for tool {tool_id}: {str(e)}")
            return {
                "tool_id": tool_id,
                "total_calls": 0,
                "success_rate": 0.0,
                "avg_execution_time": 0.0,
                "last_called": None
            }
    
    @staticmethod
    async def get_all_call_stats(
        db: Session,
        user_id: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get call statistics for all tools."""
        try:
            # Build query
            query = db.query(
                FunctionCall.tool_id,
                func.count(FunctionCall.id).label('total_calls'),
                func.avg(FunctionCall.execution_time).label('avg_execution_time'),
                func.sum(func.cast(FunctionCall.success, db.Integer)).label('successful_calls'),
                func.max(FunctionCall.created_at).label('last_called')
            ).group_by(FunctionCall.tool_id)
            
            if user_id:
                query = query.filter(FunctionCall.user_id == user_id)
            
            results = query.offset(offset).limit(limit).all()
            
            stats = []
            for result in results:
                success_rate = (result.successful_calls / result.total_calls) if result.total_calls > 0 else 0.0
                stats.append({
                    "tool_id": result.tool_id,
                    "total_calls": result.total_calls,
                    "success_rate": success_rate,
                    "avg_execution_time": float(result.avg_execution_time or 0.0),
                    "last_called": result.last_called.isoformat() if result.last_called else None
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get all call stats: {str(e)}")
            return []
    
    @staticmethod
    async def reset_call_stats(db: Session, tool_id: str):
        """Reset call statistics for a tool."""
        try:
            db.query(FunctionCall).filter(FunctionCall.tool_id == tool_id).delete()
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to reset call stats for tool {tool_id}: {str(e)}")
            db.rollback()
            raise
