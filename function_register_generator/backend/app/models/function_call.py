from sqlalchemy import Column, String, Text, Float, <PERSON>olean, DateTime, ForeignKey, Index
from sqlalchemy.orm import relationship
from datetime import datetime

from app.core.database import Base

class FunctionCall(Base):
    """Model for tracking function calls and their statistics."""
    
    __tablename__ = "function_calls"
    
    # Primary key
    id = Column(String, primary_key=True)
    
    # Foreign keys
    tool_id = Column(String, ForeignKey("tools.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=True)
    
    # Call data
    arguments = Column(Text, nullable=True)  # JSON string of arguments
    result = Column(Text, nullable=True)     # JSON string of result
    
    # Execution metadata
    execution_time = Column(Float, nullable=False)  # Execution time in seconds
    success = Column(Boolean, nullable=False, default=False)
    error = Column(Text, nullable=True)  # Error message if failed
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    tool = relationship("Tool", back_populates="function_calls")
    user = relationship("User", back_populates="function_calls")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_function_calls_tool_id', 'tool_id'),
        Index('idx_function_calls_user_id', 'user_id'),
        Index('idx_function_calls_created_at', 'created_at'),
        Index('idx_function_calls_success', 'success'),
        Index('idx_function_calls_tool_user', 'tool_id', 'user_id'),
        Index('idx_function_calls_tool_created', 'tool_id', 'created_at'),
    )
    
    def __repr__(self):
        return f"<FunctionCall(id={self.id}, tool_id={self.tool_id}, success={self.success})>"
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "tool_id": self.tool_id,
            "user_id": self.user_id,
            "arguments": self.arguments,
            "result": self.result,
            "execution_time": self.execution_time,
            "success": self.success,
            "error": self.error,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

class FunctionCallSummary(Base):
    """Model for storing aggregated function call statistics."""
    
    __tablename__ = "function_call_summaries"
    
    # Composite primary key
    tool_id = Column(String, ForeignKey("tools.id"), primary_key=True)
    date = Column(DateTime, primary_key=True)  # Date for daily aggregation
    
    # Aggregated statistics
    total_calls = Column(Integer, default=0, nullable=False)
    successful_calls = Column(Integer, default=0, nullable=False)
    failed_calls = Column(Integer, default=0, nullable=False)
    avg_execution_time = Column(Float, default=0.0, nullable=False)
    min_execution_time = Column(Float, nullable=True)
    max_execution_time = Column(Float, nullable=True)
    
    # Unique users
    unique_users = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    tool = relationship("Tool", back_populates="call_summaries")
    
    # Indexes
    __table_args__ = (
        Index('idx_function_call_summaries_tool_date', 'tool_id', 'date'),
        Index('idx_function_call_summaries_date', 'date'),
    )
    
    def __repr__(self):
        return f"<FunctionCallSummary(tool_id={self.tool_id}, date={self.date}, total_calls={self.total_calls})>"
    
    @property
    def success_rate(self):
        """Calculate success rate."""
        if self.total_calls == 0:
            return 0.0
        return self.successful_calls / self.total_calls
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "tool_id": self.tool_id,
            "date": self.date.isoformat() if self.date else None,
            "total_calls": self.total_calls,
            "successful_calls": self.successful_calls,
            "failed_calls": self.failed_calls,
            "success_rate": self.success_rate,
            "avg_execution_time": self.avg_execution_time,
            "min_execution_time": self.min_execution_time,
            "max_execution_time": self.max_execution_time,
            "unique_users": self.unique_users,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
