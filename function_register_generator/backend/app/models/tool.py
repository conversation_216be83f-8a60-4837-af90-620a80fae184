from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, Index, Integer, Float
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import List, Dict, Any
import json

from app.core.database import Base

class Tool(Base):
    """Model for storing tool definitions and metadata."""
    
    __tablename__ = "tools"
    
    # Primary key
    id = Column(String, primary_key=True)
    
    # Basic information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    version = Column(String(50), default="1.0.0", nullable=False)
    
    # Schema and configuration
    input_schema = Column(Text, nullable=True)  # JSON string
    output_schema = Column(Text, nullable=True)  # JSON string
    
    # Categorization
    category = Column(String(100), nullable=False, default="utility")
    tags = Column(Text, nullable=True)  # JSON array as string
    
    # Access control
    visibility = Column(String(20), default="public", nullable=False)  # public, private, organization
    requires_auth = Column(Boolean, default=False, nullable=False)
    
    # Transport and execution
    transport = Column(String(50), default="http", nullable=False)  # http, python, stdio
    endpoint_url = Column(String(500), nullable=True)
    execution_config = Column(Text, nullable=True)  # JSON string
    
    # Status and metadata
    status = Column(String(20), default="active", nullable=False)  # active, deprecated, disabled
    documentation = Column(Text, nullable=True)
    
    # Statistics
    usage_count = Column(Integer, default=0, nullable=False)
    rating = Column(Float, default=0.0, nullable=False)
    favorite_count = Column(Integer, default=0, nullable=False)
    
    # Ownership
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="tools")
    function_calls = relationship("FunctionCall", back_populates="tool", cascade="all, delete-orphan")
    call_summaries = relationship("FunctionCallSummary", back_populates="tool", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_tools_user_id', 'user_id'),
        Index('idx_tools_category', 'category'),
        Index('idx_tools_status', 'status'),
        Index('idx_tools_visibility', 'visibility'),
        Index('idx_tools_created_at', 'created_at'),
        Index('idx_tools_usage_count', 'usage_count'),
        Index('idx_tools_rating', 'rating'),
        Index('idx_tools_name', 'name'),
        Index('idx_tools_category_status', 'category', 'status'),
        Index('idx_tools_user_status', 'user_id', 'status'),
    )
    
    def __repr__(self):
        return f"<Tool(id={self.id}, name={self.name}, category={self.category})>"
    
    @property
    def tags_list(self) -> List[str]:
        """Get tags as a list."""
        if not self.tags:
            return []
        try:
            return json.loads(self.tags)
        except (json.JSONDecodeError, TypeError):
            return []
    
    @tags_list.setter
    def tags_list(self, value: List[str]):
        """Set tags from a list."""
        self.tags = json.dumps(value) if value else None
    
    @property
    def input_schema_dict(self) -> Dict[str, Any]:
        """Get input schema as a dictionary."""
        if not self.input_schema:
            return {}
        try:
            return json.loads(self.input_schema)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    @input_schema_dict.setter
    def input_schema_dict(self, value: Dict[str, Any]):
        """Set input schema from a dictionary."""
        self.input_schema = json.dumps(value) if value else None
    
    @property
    def output_schema_dict(self) -> Dict[str, Any]:
        """Get output schema as a dictionary."""
        if not self.output_schema:
            return {}
        try:
            return json.loads(self.output_schema)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    @output_schema_dict.setter
    def output_schema_dict(self, value: Dict[str, Any]):
        """Set output schema from a dictionary."""
        self.output_schema = json.dumps(value) if value else None
    
    @property
    def execution_config_dict(self) -> Dict[str, Any]:
        """Get execution config as a dictionary."""
        if not self.execution_config:
            return {}
        try:
            return json.loads(self.execution_config)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    @execution_config_dict.setter
    def execution_config_dict(self, value: Dict[str, Any]):
        """Set execution config from a dictionary."""
        self.execution_config = json.dumps(value) if value else None
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "category": self.category,
            "tags": self.tags_list,
            "visibility": self.visibility,
            "requires_auth": self.requires_auth,
            "transport": self.transport,
            "status": self.status,
            "documentation": self.documentation,
            "usage_count": self.usage_count,
            "rating": self.rating,
            "favorite_count": self.favorite_count,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "input_schema": self.input_schema_dict,
            "output_schema": self.output_schema_dict,
        }
        
        # Include sensitive data only if requested
        if include_sensitive:
            data.update({
                "endpoint_url": self.endpoint_url,
                "execution_config": self.execution_config_dict,
            })
        
        return data
    
    def is_accessible_by(self, user_id: str = None, is_admin: bool = False) -> bool:
        """Check if the tool is accessible by a user."""
        # Admin can access everything
        if is_admin:
            return True
        
        # Owner can access their own tools
        if user_id and self.user_id == user_id:
            return True
        
        # Public tools are accessible to everyone
        if self.visibility == "public" and self.status == "active":
            return True
        
        # Private tools are only accessible to owner
        if self.visibility == "private":
            return user_id == self.user_id
        
        # Organization tools would require organization membership check
        # For now, treat as private
        if self.visibility == "organization":
            return user_id == self.user_id
        
        return False
    
    def can_be_called_by(self, user_id: str = None) -> bool:
        """Check if the tool can be called by a user."""
        # Must be accessible first
        if not self.is_accessible_by(user_id):
            return False
        
        # Must be active
        if self.status != "active":
            return False
        
        # Check authentication requirement
        if self.requires_auth and not user_id:
            return False
        
        return True
    
    def increment_usage(self):
        """Increment the usage count."""
        self.usage_count += 1
    
    def update_rating(self, new_rating: float, rating_count: int):
        """Update the average rating."""
        if rating_count > 0:
            current_total = self.rating * (rating_count - 1)
            self.rating = (current_total + new_rating) / rating_count
    
    def add_favorite(self):
        """Increment favorite count."""
        self.favorite_count += 1
    
    def remove_favorite(self):
        """Decrement favorite count."""
        if self.favorite_count > 0:
            self.favorite_count -= 1
