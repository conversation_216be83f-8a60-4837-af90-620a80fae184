from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.models.tool import Tool
from app.services.function_calling_service import FunctionCallingService
from app.services.tool_service import ToolService
from app.core.auth import get_current_user_optional

router = APIRouter()

class FunctionSchema(BaseModel):
    type: str = "function"
    function: Dict[str, Any]

class FunctionCallRequest(BaseModel):
    tool_id: str
    arguments: Dict[str, Any]
    user_id: Optional[str] = None

class FunctionCallResponse(BaseModel):
    success: bool
    result: Any
    execution_time: float
    error: Optional[str] = None
    call_id: str

class FunctionCallStats(BaseModel):
    tool_id: str
    total_calls: int
    success_rate: float
    avg_execution_time: float
    last_called: Optional[str] = None

@router.get("/function-schema", response_model=List[FunctionSchema])
async def get_function_schemas(
    category: Optional[str] = Query(None, description="Filter by tool category"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    include_private: bool = Query(False, description="Include private tools"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """
    Get OpenAI Function Calling compatible schemas for all available tools.
    
    This endpoint returns tool definitions in the format expected by OpenAI's
    Function Calling API, allowing AI models to understand and call these tools.
    """
    try:
        # Get tools based on filters
        tools = await ToolService.get_tools(
            db=db,
            category=category,
            tags=tags,
            include_private=include_private,
            user_id=current_user.id if current_user else None
        )
        
        # Convert to function schemas
        schemas = []
        for tool in tools:
            schema = FunctionCallingService.tool_to_function_schema(tool)
            schemas.append(FunctionSchema(function=schema))
        
        return schemas
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get function schemas: {str(e)}")

@router.get("/function-schema/{tool_id}", response_model=FunctionSchema)
async def get_function_schema(
    tool_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """
    Get OpenAI Function Calling compatible schema for a specific tool.
    """
    try:
        tool = await ToolService.get_tool(db, tool_id, current_user.id if current_user else None)
        if not tool:
            raise HTTPException(status_code=404, detail="Tool not found")
        
        schema = FunctionCallingService.tool_to_function_schema(tool)
        return FunctionSchema(function=schema)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get function schema: {str(e)}")

@router.post("/call", response_model=FunctionCallResponse)
async def call_function(
    request: FunctionCallRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """
    Execute a tool function call with the provided arguments.
    
    This endpoint acts as a proxy for tool execution, handling authentication,
    validation, and result formatting.
    """
    try:
        # Get the tool
        tool = await ToolService.get_tool(
            db, 
            request.tool_id, 
            current_user.id if current_user else None
        )
        if not tool:
            raise HTTPException(status_code=404, detail="Tool not found")
        
        # Check if user has permission to call this tool
        if tool.requires_auth and not current_user:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        # Execute the function call
        result = await FunctionCallingService.execute_function_call(
            tool=tool,
            arguments=request.arguments,
            user_id=current_user.id if current_user else None,
            db=db
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Function call failed: {str(e)}")

@router.get("/stats/{tool_id}", response_model=FunctionCallStats)
async def get_function_call_stats(
    tool_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """
    Get statistics for function calls of a specific tool.
    """
    try:
        tool = await ToolService.get_tool(
            db, 
            tool_id, 
            current_user.id if current_user else None
        )
        if not tool:
            raise HTTPException(status_code=404, detail="Tool not found")
        
        stats = await FunctionCallingService.get_call_stats(db, tool_id)
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

@router.get("/stats", response_model=List[FunctionCallStats])
async def get_all_function_call_stats(
    limit: int = Query(50, description="Maximum number of tools to return"),
    offset: int = Query(0, description="Number of tools to skip"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """
    Get function call statistics for all tools.
    """
    try:
        stats = await FunctionCallingService.get_all_call_stats(
            db=db,
            user_id=current_user.id if current_user else None,
            limit=limit,
            offset=offset
        )
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

@router.delete("/stats/{tool_id}")
async def reset_function_call_stats(
    tool_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """
    Reset function call statistics for a specific tool.
    Requires tool ownership or admin privileges.
    """
    try:
        tool = await ToolService.get_tool(
            db, 
            tool_id, 
            current_user.id if current_user else None
        )
        if not tool:
            raise HTTPException(status_code=404, detail="Tool not found")
        
        # Check ownership
        if tool.user_id != current_user.id and not current_user.is_admin:
            raise HTTPException(status_code=403, detail="Permission denied")
        
        await FunctionCallingService.reset_call_stats(db, tool_id)
        return {"message": "Statistics reset successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset stats: {str(e)}")

# Health check endpoint for function calling service
@router.get("/health")
async def function_calling_health():
    """
    Health check endpoint for the function calling service.
    """
    return {
        "status": "healthy",
        "service": "function_calling",
        "version": "1.0.0"
    }
