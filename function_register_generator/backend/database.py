from elasticsearch import AsyncElasticsearch
import redis.asyncio as redis
from config import settings
import json
import logging

logger = logging.getLogger(__name__)

# Global connections
es_client: AsyncElasticsearch = None
redis_client: redis.Redis = None

async def init_elasticsearch():
    """Initialize Elasticsearch connection and create index if not exists"""
    global es_client
    
    es_client = AsyncElasticsearch(
        [settings.es_host],
        basic_auth=(settings.es_user, settings.es_pass) if settings.es_user else None,
        verify_certs=False
    )
    
    # Create index with mapping if not exists
    index_exists = await es_client.indices.exists(index=settings.es_index)
    if not index_exists:
        await create_index_mapping()
    
    logger.info(f"Elasticsearch initialized: {settings.es_host}")

async def init_redis():
    """Initialize Redis connection"""
    global redis_client
    redis_client = redis.from_url(settings.redis_url)
    logger.info(f"Redis initialized: {settings.redis_url}")

async def create_index_mapping():
    """Create Elasticsearch index with proper mapping"""
    mapping = {
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 1,
            "analysis": {
                "normalizer": {
                    "lc_keyword": {
                        "type": "custom",
                        "filter": ["lowercase"]
                    }
                }
            }
        },
        "mappings": {
            "properties": {
                "toolId": {"type": "keyword", "normalizer": "lc_keyword"},
                "displayName": {"type": "search_as_you_type"},
                "aliases": {"type": "keyword"},
                "version": {"type": "keyword"},
                "versionSeq": {"type": "integer"},
                "visibility": {"type": "keyword"},
                "category": {"type": "keyword"},
                "capabilities": {"type": "keyword"},
                "locales": {"type": "keyword"},
                
                "owner": {
                    "properties": {
                        "org": {"type": "keyword"},
                        "contact": {"type": "keyword"},
                        "license": {"type": "keyword"},
                        "userId": {"type": "keyword"},
                        "apiKeyId": {"type": "keyword"}
                    }
                },
                
                "runtime": {
                    "properties": {
                        "transport": {"type": "keyword"},
                        "endpoint": {"type": "keyword"},
                        "httpMethod": {"type": "keyword"},
                        "auth": {
                            "properties": {
                                "type": {"type": "keyword"},
                                "env_keys": {"type": "keyword"}
                            }
                        },
                        "rate_limits": {
                            "properties": {
                                "rpm": {"type": "integer"},
                                "burst": {"type": "integer"}
                            }
                        },
                        "cost_hints": {
                            "properties": {
                                "per_call_usd": {"type": "scaled_float", "scaling_factor": 10000}
                            }
                        },
                        "latency_hints_ms": {
                            "properties": {
                                "p50": {"type": "integer"},
                                "p95": {"type": "integer"}
                            }
                        },
                        "fault_tolerance": {"type": "keyword"}
                    }
                },
                
                "metadata": {
                    "properties": {
                        "has_variables_dependencies": {"type": "boolean"},
                        "user_facing": {"type": "boolean"}
                    }
                },
                
                "descriptionDev": {
                    "type": "text",
                    "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}
                },
                "descriptionUser": {
                    "type": "text", 
                    "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}
                },
                
                "inputsDeveloperSchema": {"type": "flattened"},
                "inputsDeveloperValidation": {"type": "flattened"},
                "inputsDeveloperDependencies": {"type": "flattened"},
                "outputsSchema": {"type": "flattened"},
                
                "examples": {
                    "type": "nested",
                    "properties": {
                        "userQuery": {"type": "text"},
                        "parsedInputs": {"type": "flattened"},
                        "resolvedInputs": {"type": "flattened"}
                    }
                },
                
                "errors": {
                    "type": "nested",
                    "properties": {
                        "code": {"type": "keyword"},
                        "http": {"type": "integer"},
                        "message": {"type": "text"},
                        "recovery": {"type": "text"}
                    }
                },
                
                "quickstarts": {
                    "type": "nested",
                    "properties": {
                        "kind": {"type": "keyword"},
                        "title": {"type": "text"},
                        "content": {"type": "text"}
                    }
                },
                
                "content": {"type": "text"},
                "contentEmb": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True,
                    "similarity": "cosine"
                },
                "descriptionEmb": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True,
                    "similarity": "cosine"
                },
                
                "createdBy": {"type": "keyword"},
                "createdTime": {"type": "date", "format": "strict_date_time"},
                "updatedBy": {"type": "keyword"},
                "updatedTime": {"type": "date", "format": "strict_date_time"},
                "deleteFlag": {"type": "integer"}
            }
        }
    }
    
    await es_client.indices.create(index=settings.es_index, body=mapping)
    logger.info(f"Created index: {settings.es_index}")

def get_es_client() -> AsyncElasticsearch:
    """Get Elasticsearch client"""
    return es_client

def get_redis_client() -> redis.Redis:
    """Get Redis client"""
    return redis_client