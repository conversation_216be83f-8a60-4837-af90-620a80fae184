from datetime import datetime
from database import get_es_client
from config import settings
import json
import logging

logger = logging.getLogger(__name__)

# Mock 工具数据 - 展示各种类型的工具
MOCK_TOOLS = [
    {
        "toolId": "weather.openmeteo.forecast",
        "displayName": "天气预报查询",
        "aliases": ["weather", "forecast", "天气", "气象"],
        "version": "1.0.0",
        "versionSeq": 1,
        "visibility": "public",
        "category": "weather",
        "capabilities": ["http", "sync"],
        "locales": ["zh-CN", "en-US"],
        
        "owner": {
            "org": "DevOps Team",
            "contact": "<EMAIL>",
            "license": "MIT",
            "userId": "user001",
            "apiKeyId": "key001"
        },
        
        "runtime": {
            "transport": "http",
            "endpoint": "https://api.open-meteo.com/v1/forecast",
            "httpMethod": "GET",
            "auth": {"type": "none"},
            "rate_limits": {"rpm": 100, "burst": 10},
            "cost_hints": {"per_call_usd": 0.0},
            "latency_hints_ms": {"p50": 200, "p95": 500},
            "fault_tolerance": "high"
        },
        
        "metadata": {
            "has_variables_dependencies": False,
            "user_facing": True
        },
        
        "descriptionDev": "调用 Open-Meteo API 获取指定位置的天气预报数据，支持多种气象参数",
        "descriptionUser": "获取任意地点的详细天气预报，包括温度、湿度、风速等信息",
        
        "inputsDeveloperSchema": {
            "type": "object",
            "required": ["latitude", "longitude"],
            "properties": {
                "latitude": {
                    "type": "number",
                    "description": "纬度 (-90 到 90)",
                    "minimum": -90,
                    "maximum": 90
                },
                "longitude": {
                    "type": "number", 
                    "description": "经度 (-180 到 180)",
                    "minimum": -180,
                    "maximum": 180
                },
                "hourly": {
                    "type": "string",
                    "description": "小时级数据类型",
                    "enum": ["temperature_2m", "relative_humidity_2m", "wind_speed_10m"],
                    "default": "temperature_2m"
                },
                "days": {
                    "type": "integer",
                    "description": "预报天数",
                    "minimum": 1,
                    "maximum": 7,
                    "default": 3
                }
            }
        },
        
        "outputsSchema": {
            "type": "object",
            "properties": {
                "latitude": {"type": "number"},
                "longitude": {"type": "number"},
                "hourly": {"type": "object"},
                "hourly_units": {"type": "object"}
            }
        },
        
        "examples": [
            {
                "userQuery": "查询北京的天气",
                "parsedInputs": {
                    "latitude": 39.9042,
                    "longitude": 116.4074,
                    "hourly": "temperature_2m",
                    "days": 3
                },
                "resolvedInputs": {
                    "latitude": 39.9042,
                    "longitude": 116.4074,
                    "hourly": "temperature_2m",
                    "forecast_days": 3
                }
            },
            {
                "userQuery": "上海未来一周的风速",
                "parsedInputs": {
                    "latitude": 31.2304,
                    "longitude": 121.4737,
                    "hourly": "wind_speed_10m",
                    "days": 7
                }
            }
        ],
        
        "errors": [
            {
                "code": "INVALID_COORDINATES",
                "http": 400,
                "message": "纬度或经度超出有效范围",
                "recovery": "请检查坐标值是否在有效范围内"
            },
            {
                "code": "API_TIMEOUT",
                "http": 504,
                "message": "天气服务响应超时",
                "recovery": "请稍后重试或联系管理员"
            }
        ],
        
        "quickstarts": [
            {
                "kind": "curl",
                "title": "cURL 示例",
                "content": "curl 'https://api.open-meteo.com/v1/forecast?latitude=39.9042&longitude=116.4074&hourly=temperature_2m&forecast_days=3'"
            },
            {
                "kind": "python",
                "title": "Python 示例",
                "content": "import requests\n\nresponse = requests.get(\n    'https://api.open-meteo.com/v1/forecast',\n    params={\n        'latitude': 39.9042,\n        'longitude': 116.4074,\n        'hourly': 'temperature_2m',\n        'forecast_days': 3\n    }\n)\ndata = response.json()"
            }
        ],
        
        "content": "天气预报查询 weather forecast 获取指定位置的天气预报数据 气象信息 温度 湿度 风速",
        "createdBy": "system",
        "createdTime": datetime.now().isoformat(),
        "updatedBy": "system", 
        "updatedTime": datetime.now().isoformat(),
        "deleteFlag": 0
    },
    
    {
        "toolId": "time.current.get",
        "displayName": "获取当前时间",
        "aliases": ["time", "now", "datetime", "时间"],
        "version": "1.2.0",
        "versionSeq": 2,
        "visibility": "public",
        "category": "utility",
        "capabilities": ["python", "sync", "local"],
        "locales": ["zh-CN", "en-US"],
        
        "owner": {
            "org": "Platform Team",
            "contact": "<EMAIL>",
            "license": "Apache-2.0",
            "userId": "user002"
        },
        
        "runtime": {
            "transport": "python",
            "script_content": "aW1wb3J0IGRhdGV0aW1lCmltcG9ydCB6b25laW5mbwoKZGVmIG5vdyh0ejogc3RyID0gIkFzaWEvU2hhbmdoYWkiKToKICAgICJ7ImdldCBjdXJyZW50IHRpbWUgaW4gc3BlY2lmaWVkIHRpbWV6b25lIgogICAgdHJ5OgogICAgICAgIHpvbmUgPSB6b25laW5mby5ab25lSW5mbyh0eikKICAgICAgICBjdXJyZW50X3RpbWUgPSBkYXRldGltZS5kYXRldGltZS5ub3coem9uZSkKICAgICAgICByZXR1cm4gewogICAgICAgICAgICAibm93IjogY3VycmVudF90aW1lLmlzb2Zvcm1hdCgpLAogICAgICAgICAgICAidGltZXpvbmUiOiB0eiwKICAgICAgICAgICAgInVuaXhfdGltZXN0YW1wIjogaW50KGN1cnJlbnRfdGltZS50aW1lc3RhbXAoKSkKICAgICAgICB9CiAgICBleGNlcHQgRXhjZXB0aW9uIGFzIGU6CiAgICAgICAgcmV0dXJuIHsiZXJyb3IiOiBzdHIoZSl9",
            "entry_function": "now",
            "auth": {"type": "none"},
            "rate_limits": {"rpm": 1000, "burst": 50},
            "cost_hints": {"per_call_usd": 0.0001},
            "latency_hints_ms": {"p50": 10, "p95": 50},
            "fault_tolerance": "high"
        },
        
        "metadata": {
            "has_variables_dependencies": False,
            "user_facing": True
        },
        
        "descriptionDev": "Python 脚本获取指定时区的当前时间，支持所有 IANA 时区",
        "descriptionUser": "获取当前时间，可以指定不同的时区",
        
        "inputsDeveloperSchema": {
            "type": "object",
            "properties": {
                "tz": {
                    "type": "string",
                    "description": "时区名称 (IANA 格式)",
                    "default": "Asia/Shanghai",
                    "examples": ["Asia/Shanghai", "America/New_York", "Europe/London", "UTC"]
                }
            }
        },
        
        "outputsSchema": {
            "type": "object",
            "required": ["now", "timezone"],
            "properties": {
                "now": {"type": "string", "description": "ISO 格式的当前时间"},
                "timezone": {"type": "string", "description": "时区名称"},
                "unix_timestamp": {"type": "integer", "description": "Unix 时间戳"}
            }
        },
        
        "examples": [
            {
                "userQuery": "现在几点了",
                "parsedInputs": {"tz": "Asia/Shanghai"},
                "resolvedInputs": {"tz": "Asia/Shanghai"}
            },
            {
                "userQuery": "纽约现在的时间",
                "parsedInputs": {"tz": "America/New_York"}
            }
        ],
        
        "quickstarts": [
            {
                "kind": "python",
                "title": "Python 调用示例",
                "content": "result = now('Asia/Shanghai')\nprint(f\"当前时间: {result['now']}\")"
            }
        ],
        
        "content": "获取当前时间 time now datetime 时区 timestamp 本地时间",
        "createdBy": "system",
        "createdTime": datetime.now().isoformat(),
        "updatedBy": "system",
        "updatedTime": datetime.now().isoformat(),
        "deleteFlag": 0
    },
    
    {
        "toolId": "translate.text.google",
        "displayName": "文本翻译服务",
        "aliases": ["translate", "translation", "翻译", "转换"],
        "version": "2.1.0",
        "versionSeq": 3,
        "visibility": "internal",
        "category": "nlp",
        "capabilities": ["http", "async", "batch"],
        "locales": ["zh-CN", "en-US", "ja-JP", "ko-KR"],
        
        "owner": {
            "org": "AI Team",
            "contact": "<EMAIL>",
            "license": "Commercial",
            "userId": "user003",
            "apiKeyId": "key003"
        },
        
        "runtime": {
            "transport": "http",
            "endpoint": "https://translation.googleapis.com/language/translate/v2",
            "httpMethod": "POST",
            "auth": {
                "type": "api_key",
                "env_keys": ["GOOGLE_TRANSLATE_API_KEY"]
            },
            "rate_limits": {"rpm": 60, "burst": 5},
            "cost_hints": {"per_call_usd": 0.02},
            "latency_hints_ms": {"p50": 800, "p95": 2000},
            "fault_tolerance": "medium"
        },
        
        "metadata": {
            "has_variables_dependencies": True,
            "user_facing": True
        },
        
        "descriptionDev": "调用 Google Translate API 进行文本翻译，支持 100+ 种语言互译",
        "descriptionUser": "将文本翻译成目标语言，支持自动语言检测",
        
        "inputsDeveloperSchema": {
            "type": "object",
            "required": ["text", "target"],
            "properties": {
                "text": {
                    "type": "string",
                    "description": "要翻译的文本",
                    "maxLength": 5000
                },
                "target": {
                    "type": "string",
                    "description": "目标语言代码",
                    "enum": ["zh", "en", "ja", "ko", "fr", "de", "es", "ru"]
                },
                "source": {
                    "type": "string",
                    "description": "源语言代码 (可选，自动检测)",
                    "enum": ["zh", "en", "ja", "ko", "fr", "de", "es", "ru"]
                }
            }
        },
        
        "outputsSchema": {
            "type": "object",
            "properties": {
                "translatedText": {"type": "string"},
                "detectedSourceLanguage": {"type": "string"},
                "confidence": {"type": "number"}
            }
        },
        
        "examples": [
            {
                "userQuery": "把这句话翻译成英文：你好世界",
                "parsedInputs": {
                    "text": "你好世界",
                    "target": "en"
                }
            },
            {
                "userQuery": "Translate 'Hello World' to Chinese",
                "parsedInputs": {
                    "text": "Hello World",
                    "target": "zh",
                    "source": "en"
                }
            }
        ],
        
        "errors": [
            {
                "code": "QUOTA_EXCEEDED",
                "http": 429,
                "message": "API 配额已用完",
                "recovery": "请等待配额重置或联系管理员增加配额"
            },
            {
                "code": "UNSUPPORTED_LANGUAGE",
                "http": 400,
                "message": "不支持的语言代码",
                "recovery": "请使用支持的语言代码列表"
            }
        ],
        
        "quickstarts": [
            {
                "kind": "curl",
                "title": "cURL 示例",
                "content": "curl -X POST 'https://translation.googleapis.com/language/translate/v2' \\\n  -H 'Authorization: Bearer $GOOGLE_TRANSLATE_API_KEY' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\"q\": \"你好世界\", \"target\": \"en\"}'"
            },
            {
                "kind": "python",
                "title": "Python 示例", 
                "content": "import requests\n\nresponse = requests.post(\n    'https://translation.googleapis.com/language/translate/v2',\n    headers={'Authorization': f'Bearer {api_key}'},\n    json={'q': '你好世界', 'target': 'en'}\n)\nresult = response.json()"
            }
        ],
        
        "content": "文本翻译服务 translate translation 多语言 语言转换 Google API",
        "createdBy": "system",
        "createdTime": datetime.now().isoformat(),
        "updatedBy": "system",
        "updatedTime": datetime.now().isoformat(),
        "deleteFlag": 0
    },
    
    {
        "toolId": "database.query.sql",
        "displayName": "数据库查询工具",
        "aliases": ["sql", "query", "database", "数据库", "查询"],
        "version": "1.0.0",
        "versionSeq": 1,
        "visibility": "internal",
        "category": "database",
        "capabilities": ["sql", "sync", "secure"],
        "locales": ["zh-CN", "en-US"],
        
        "owner": {
            "org": "Data Team",
            "contact": "<EMAIL>",
            "license": "Internal",
            "userId": "user004"
        },
        
        "runtime": {
            "transport": "python",
            "script_content": "aW1wb3J0IHNxbGl0ZTMKaW1wb3J0IHBhbmRhcyBhcyBwZAoKZGVmIHF1ZXJ5X2RhdGFiYXNlKHNxbDogc3RyLCBkYl9wYXRoOiBzdHIgPSAiL3RtcC9zYW1wbGUuZGIiKToKICAgICIiIkV4ZWN1dGUgU1FMIHF1ZXJ5IGFnYWluc3QgU1FMaXRlIGRhdGFiYXNlIiIiCiAgICB0cnk6CiAgICAgICAgY29ubiA9IHNxbGl0ZTMuY29ubmVjdChkYl9wYXRoKQogICAgICAgIGRmID0gcGQucmVhZF9zcWwoc3FsLCBjb25uKQogICAgICAgIGNvbm4uY2xvc2UoKQogICAgICAgIAogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICJzdWNjZXNzIjogVHJ1ZSwKICAgICAgICAgICAgInJvd3MiOiBsZW4oZGYpLAogICAgICAgICAgICAiY29sdW1ucyI6IGxpc3QoZGYuY29sdW1ucyksCiAgICAgICAgICAgICJkYXRhIjogZGYudG9fZGljdCgicmVjb3JkcyIpCiAgICAgICAgfQogICAgZXhjZXB0IEV4Y2VwdGlvbiBhcyBlOgogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICJzdWNjZXNzIjogRmFsc2UsCiAgICAgICAgICAgICJlcnJvciI6IHN0cihlKQogICAgICAgIH0=",
            "entry_function": "query_database",
            "auth": {"type": "internal"},
            "rate_limits": {"rpm": 30, "burst": 3},
            "cost_hints": {"per_call_usd": 0.001},
            "latency_hints_ms": {"p50": 100, "p95": 1000},
            "fault_tolerance": "medium"
        },
        
        "metadata": {
            "has_variables_dependencies": True,
            "user_facing": False
        },
        
        "descriptionDev": "在沙盒环境中执行 SQL 查询，支持 SQLite 数据库操作",
        "descriptionUser": "执行数据库查询并返回结构化结果",
        
        "inputsDeveloperSchema": {
            "type": "object",
            "required": ["sql"],
            "properties": {
                "sql": {
                    "type": "string",
                    "description": "要执行的 SQL 查询语句",
                    "maxLength": 1000
                },
                "db_path": {
                    "type": "string",
                    "description": "数据库文件路径",
                    "default": "/tmp/sample.db"
                }
            }
        },
        
        "outputsSchema": {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "rows": {"type": "integer"},
                "columns": {"type": "array", "items": {"type": "string"}},
                "data": {"type": "array"},
                "error": {"type": "string"}
            }
        },
        
        "examples": [
            {
                "userQuery": "查询用户表中的所有记录",
                "parsedInputs": {
                    "sql": "SELECT * FROM users LIMIT 10"
                }
            }
        ],
        
        "errors": [
            {
                "code": "SQL_SYNTAX_ERROR",
                "http": 400,
                "message": "SQL 语法错误",
                "recovery": "请检查 SQL 语句语法"
            },
            {
                "code": "DATABASE_NOT_FOUND",
                "http": 404,
                "message": "数据库文件不存在",
                "recovery": "请确认数据库路径正确"
            }
        ],
        
        "quickstarts": [
            {
                "kind": "python",
                "title": "Python 示例",
                "content": "result = query_database(\n    sql=\"SELECT * FROM users WHERE active = 1 LIMIT 5\",\n    db_path=\"/tmp/sample.db\"\n)\nprint(f\"查询到 {result['rows']} 条记录\")"
            }
        ],
        
        "content": "数据库查询工具 sql query database 数据查询 SQLite",
        "createdBy": "system",
        "createdTime": datetime.now().isoformat(),
        "updatedBy": "system",
        "updatedTime": datetime.now().isoformat(),
        "deleteFlag": 0
    },
    
    {
        "toolId": "mcp.filesystem.read",
        "displayName": "文件系统读取 (MCP)",
        "aliases": ["file", "read", "filesystem", "文件", "读取"],
        "version": "1.0.0",
        "versionSeq": 1,
        "visibility": "public",
        "category": "filesystem",
        "capabilities": ["mcp", "stdio", "local"],
        "locales": ["zh-CN", "en-US"],
        
        "owner": {
            "org": "MCP Team",
            "contact": "<EMAIL>",
            "license": "MIT",
            "userId": "user005"
        },
        
        "runtime": {
            "transport": "stdio",
            "command": "uvx",
            "args": ["mcp-server-filesystem", "--path", "/tmp"],
            "auth": {"type": "none"},
            "rate_limits": {"rpm": 200, "burst": 20},
            "cost_hints": {"per_call_usd": 0.0},
            "latency_hints_ms": {"p50": 50, "p95": 200},
            "fault_tolerance": "high"
        },
        
        "metadata": {
            "has_variables_dependencies": False,
            "user_facing": True
        },
        
        "descriptionDev": "通过 MCP 协议读取文件系统内容，支持文本文件读取",
        "descriptionUser": "读取指定路径的文件内容",
        
        "inputsDeveloperSchema": {
            "type": "object",
            "required": ["path"],
            "properties": {
                "path": {
                    "type": "string",
                    "description": "要读取的文件路径",
                    "pattern": "^/tmp/.*"
                }
            }
        },
        
        "outputsSchema": {
            "type": "object",
            "properties": {
                "content": {"type": "string"},
                "size": {"type": "integer"},
                "mimeType": {"type": "string"}
            }
        },
        
        "examples": [
            {
                "userQuery": "读取配置文件内容",
                "parsedInputs": {
                    "path": "/tmp/config.txt"
                }
            }
        ],
        
        "quickstarts": [
            {
                "kind": "mcp",
                "title": "MCP 调用示例",
                "content": "{\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"read_file\",\n    \"arguments\": {\n      \"path\": \"/tmp/example.txt\"\n    }\n  }\n}"
            }
        ],
        
        "content": "文件系统读取 MCP file read filesystem 文件操作",
        "createdBy": "system",
        "createdTime": datetime.now().isoformat(),
        "updatedBy": "system",
        "updatedTime": datetime.now().isoformat(),
        "deleteFlag": 0
    }
]

async def create_mock_tools():
    """Create mock tools in Elasticsearch for demonstration"""
    es_client = get_es_client()
    if not es_client:
        logger.error("Elasticsearch client not initialized")
        return
    
    try:
        # Check if mock data already exists
        response = await es_client.count(index=settings.es_index)
        if response['count'] > 0:
            logger.info(f"Mock data already exists ({response['count']} documents)")
            return
        
        # Insert mock tools
        for tool in MOCK_TOOLS:
            await es_client.index(
                index=settings.es_index,
                id=tool['toolId'],
                body=tool
            )
        
        # Refresh index to make documents searchable
        await es_client.indices.refresh(index=settings.es_index)
        
        logger.info(f"Created {len(MOCK_TOOLS)} mock tools in Elasticsearch")
        
    except Exception as e:
        logger.error(f"Failed to create mock tools: {e}")

async def get_mock_tool_by_id(tool_id: str):
    """Get a specific mock tool by ID"""
    for tool in MOCK_TOOLS:
        if tool['toolId'] == tool_id:
            return tool
    return None