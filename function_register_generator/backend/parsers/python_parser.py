import ast
import inspect
import re
import json
import logging
from typing import Dict, List, Optional, Any, Union, Tuple, get_type_hints
from dataclasses import dataclass
from enum import Enum
import importlib.util
import sys
import tempfile
import os

logger = logging.getLogger(__name__)


class ParameterType(str, Enum):
    """参数类型枚举"""
    POSITIONAL = "positional"
    KEYWORD = "keyword"
    VAR_POSITIONAL = "var_positional"  # *args
    VAR_KEYWORD = "var_keyword"  # **kwargs


@dataclass
class FunctionParameter:
    """函数参数信息"""
    name: str
    type_hint: Optional[str] = None
    default_value: Optional[Any] = None
    has_default: bool = False
    parameter_type: ParameterType = ParameterType.POSITIONAL
    description: Optional[str] = None

    def to_json_schema(self) -> Dict[str, Any]:
        """转换为 JSON Schema 格式"""
        schema = {}
        
        # 推断类型
        if self.type_hint:
            schema.update(self._type_hint_to_schema(self.type_hint))
        elif self.default_value is not None:
            schema.update(self._value_to_schema(self.default_value))
        else:
            schema["type"] = "string"  # 默认类型
        
        # 添加描述
        if self.description:
            schema["description"] = self.description
        else:
            schema["description"] = f"参数: {self.name}"
        
        # 添加默认值
        if self.has_default and self.default_value is not None:
            schema["default"] = self.default_value
        
        return schema

    def _type_hint_to_schema(self, type_hint: str) -> Dict[str, Any]:
        """将类型提示转换为 JSON Schema"""
        type_hint = type_hint.lower().strip()
        
        if type_hint in ['str', 'string']:
            return {"type": "string"}
        elif type_hint in ['int', 'integer']:
            return {"type": "integer"}
        elif type_hint in ['float', 'number']:
            return {"type": "number"}
        elif type_hint in ['bool', 'boolean']:
            return {"type": "boolean"}
        elif type_hint in ['list', 'array']:
            return {"type": "array"}
        elif type_hint in ['dict', 'object']:
            return {"type": "object"}
        elif 'list[' in type_hint or 'list<' in type_hint:
            # 处理 List[str] 等泛型
            inner_type = re.search(r'list\[([^\]]+)\]|list<([^>]+)>', type_hint)
            if inner_type:
                inner = inner_type.group(1) or inner_type.group(2)
                return {
                    "type": "array",
                    "items": self._type_hint_to_schema(inner)
                }
            return {"type": "array"}
        elif 'dict[' in type_hint or 'dict<' in type_hint:
            return {"type": "object"}
        elif 'optional[' in type_hint:
            # 处理 Optional[str] 等
            inner_type = re.search(r'optional\[([^\]]+)\]', type_hint)
            if inner_type:
                inner = inner_type.group(1)
                schema = self._type_hint_to_schema(inner)
                schema["nullable"] = True
                return schema
            return {"type": "string", "nullable": True}
        elif 'union[' in type_hint:
            # 处理 Union 类型
            return {"type": "string"}  # 简化处理
        else:
            return {"type": "string"}

    def _value_to_schema(self, value: Any) -> Dict[str, Any]:
        """从默认值推断类型"""
        if isinstance(value, bool):
            return {"type": "boolean"}
        elif isinstance(value, int):
            return {"type": "integer"}
        elif isinstance(value, float):
            return {"type": "number"}
        elif isinstance(value, str):
            return {"type": "string"}
        elif isinstance(value, list):
            return {"type": "array"}
        elif isinstance(value, dict):
            return {"type": "object"}
        else:
            return {"type": "string"}


@dataclass
class FunctionInfo:
    """函数信息"""
    name: str
    parameters: List[FunctionParameter]
    return_type_hint: Optional[str] = None
    docstring: Optional[str] = None
    description: Optional[str] = None
    examples: List[Dict[str, Any]] = None

    def __post_init__(self):
        if self.examples is None:
            self.examples = []

    def to_input_schema(self) -> Dict[str, Any]:
        """生成输入参数的 JSON Schema"""
        schema = {
            "type": "object",
            "properties": {},
            "required": []
        }
        
        for param in self.parameters:
            if param.parameter_type in [ParameterType.POSITIONAL, ParameterType.KEYWORD]:
                schema["properties"][param.name] = param.to_json_schema()
                # 如果没有默认值，则为必填参数
                if not param.has_default:
                    schema["required"].append(param.name)
        
        return schema

    def to_output_schema(self) -> Dict[str, Any]:
        """生成输出的 JSON Schema"""
        if self.return_type_hint:
            return self._type_hint_to_schema(self.return_type_hint)
        else:
            # 默认返回结构
            return {
                "type": "object",
                "properties": {
                    "result": {"type": "string", "description": "函数执行结果"},
                    "success": {"type": "boolean", "description": "执行是否成功"}
                }
            }

    def _type_hint_to_schema(self, type_hint: str) -> Dict[str, Any]:
        """将返回类型提示转换为 JSON Schema"""
        param = FunctionParameter("temp", type_hint=type_hint)
        return param._type_hint_to_schema(type_hint)


class PythonScriptAnalyzer:
    """Python 脚本分析器"""

    def __init__(self):
        self.reset()

    def reset(self):
        """重置分析器状态"""
        self.functions = []
        self.imports = []
        self.global_variables = []
        self.classes = []

    def analyze_code(self, code: str) -> List[FunctionInfo]:
        """分析 Python 代码"""
        try:
            # 解析 AST
            tree = ast.parse(code)
            
            # 重置状态
            self.reset()
            
            # 遍历 AST 节点
            self._visit_node(tree)
            
            logger.info(f"Successfully analyzed Python code, found {len(self.functions)} functions")
            return self.functions
            
        except SyntaxError as e:
            logger.error(f"Python syntax error: {e}")
            raise ValueError(f"Invalid Python syntax: {e}")
        except Exception as e:
            logger.error(f"Failed to analyze Python code: {e}")
            raise ValueError(f"Code analysis failed: {e}")

    def analyze_function(self, code: str, function_name: str) -> Optional[FunctionInfo]:
        """分析指定的函数"""
        functions = self.analyze_code(code)
        for func in functions:
            if func.name == function_name:
                return func
        return None

    def analyze_file(self, file_path: str) -> List[FunctionInfo]:
        """分析 Python 文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            return self.analyze_code(code)
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise ValueError(f"Cannot read file: {e}")

    def _visit_node(self, node: ast.AST):
        """访问 AST 节点"""
        if isinstance(node, ast.FunctionDef):
            self._analyze_function(node)
        elif isinstance(node, ast.ClassDef):
            self._analyze_class(node)
        elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
            self._analyze_import(node)
        elif isinstance(node, ast.Assign):
            self._analyze_assignment(node)
        
        # 递归访问子节点
        for child in ast.iter_child_nodes(node):
            self._visit_node(child)

    def _analyze_function(self, node: ast.FunctionDef):
        """分析函数定义"""
        func_info = FunctionInfo(
            name=node.name,
            parameters=[],
            docstring=ast.get_docstring(node),
        )
        
        # 解析参数
        func_info.parameters = self._parse_function_args(node.args)
        
        # 解析返回类型
        if node.returns:
            func_info.return_type_hint = self._ast_to_string(node.returns)
        
        # 解析文档字符串
        if func_info.docstring:
            func_info.description = self._extract_description_from_docstring(func_info.docstring)
            self._parse_docstring_params(func_info)
        
        self.functions.append(func_info)

    def _parse_function_args(self, args: ast.arguments) -> List[FunctionParameter]:
        """解析函数参数"""
        parameters = []
        
        # 普通参数
        for i, arg in enumerate(args.args):
            param = FunctionParameter(
                name=arg.arg,
                parameter_type=ParameterType.POSITIONAL
            )
            
            # 类型注解
            if arg.annotation:
                param.type_hint = self._ast_to_string(arg.annotation)
            
            # 默认值
            defaults_offset = len(args.args) - len(args.defaults)
            if i >= defaults_offset:
                default_index = i - defaults_offset
                param.default_value = self._ast_to_value(args.defaults[default_index])
                param.has_default = True
                param.parameter_type = ParameterType.KEYWORD
            
            parameters.append(param)
        
        # *args 参数
        if args.vararg:
            param = FunctionParameter(
                name=args.vararg.arg,
                parameter_type=ParameterType.VAR_POSITIONAL
            )
            if args.vararg.annotation:
                param.type_hint = self._ast_to_string(args.vararg.annotation)
            parameters.append(param)
        
        # **kwargs 参数
        if args.kwarg:
            param = FunctionParameter(
                name=args.kwarg.arg,
                parameter_type=ParameterType.VAR_KEYWORD
            )
            if args.kwarg.annotation:
                param.type_hint = self._ast_to_string(args.kwarg.annotation)
            parameters.append(param)
        
        # 仅关键字参数
        for i, arg in enumerate(args.kwonlyargs):
            param = FunctionParameter(
                name=arg.arg,
                parameter_type=ParameterType.KEYWORD
            )
            if arg.annotation:
                param.type_hint = self._ast_to_string(arg.annotation)
            
            # 仅关键字参数的默认值
            if i < len(args.kw_defaults) and args.kw_defaults[i] is not None:
                param.default_value = self._ast_to_value(args.kw_defaults[i])
                param.has_default = True
            
            parameters.append(param)
        
        return parameters

    def _analyze_class(self, node: ast.ClassDef):
        """分析类定义"""
        class_info = {
            "name": node.name,
            "docstring": ast.get_docstring(node),
            "methods": []
        }
        
        # 分析类中的方法
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                method_info = FunctionInfo(
                    name=item.name,
                    parameters=self._parse_function_args(item.args),
                    docstring=ast.get_docstring(item)
                )
                class_info["methods"].append(method_info)
        
        self.classes.append(class_info)

    def _analyze_import(self, node: Union[ast.Import, ast.ImportFrom]):
        """分析导入语句"""
        if isinstance(node, ast.Import):
            for alias in node.names:
                self.imports.append({
                    "module": alias.name,
                    "alias": alias.asname,
                    "type": "import"
                })
        elif isinstance(node, ast.ImportFrom):
            for alias in node.names:
                self.imports.append({
                    "module": node.module,
                    "name": alias.name,
                    "alias": alias.asname,
                    "type": "from_import"
                })

    def _analyze_assignment(self, node: ast.Assign):
        """分析赋值语句（全局变量）"""
        for target in node.targets:
            if isinstance(target, ast.Name):
                self.global_variables.append({
                    "name": target.id,
                    "value": self._ast_to_value(node.value)
                })

    def _ast_to_string(self, node: ast.AST) -> str:
        """将 AST 节点转换为字符串"""
        try:
            return ast.unparse(node)
        except:
            # 如果 unparse 不可用，使用简单的类型映射
            if isinstance(node, ast.Name):
                return node.id
            elif isinstance(node, ast.Constant):
                return str(type(node.value).__name__)
            else:
                return "Any"

    def _ast_to_value(self, node: ast.AST) -> Any:
        """将 AST 节点转换为 Python 值"""
        try:
            if isinstance(node, ast.Constant):
                return node.value
            elif isinstance(node, ast.Num):  # Python < 3.8
                return node.n
            elif isinstance(node, ast.Str):  # Python < 3.8
                return node.s
            elif isinstance(node, ast.NameConstant):  # Python < 3.8
                return node.value
            elif isinstance(node, ast.List):
                return [self._ast_to_value(item) for item in node.elts]
            elif isinstance(node, ast.Dict):
                return {
                    self._ast_to_value(k): self._ast_to_value(v)
                    for k, v in zip(node.keys, node.values)
                    if k is not None
                }
            elif isinstance(node, ast.Name):
                return f"<{node.id}>"  # 变量引用
            else:
                return None
        except:
            return None

    def _extract_description_from_docstring(self, docstring: str) -> str:
        """从文档字符串提取描述"""
        if not docstring:
            return ""
        
        # 取第一行作为简短描述
        lines = docstring.strip().split('\n')
        return lines[0].strip()

    def _parse_docstring_params(self, func_info: FunctionInfo):
        """从文档字符串解析参数描述"""
        if not func_info.docstring:
            return
        
        # 简单的参数描述解析
        lines = func_info.docstring.split('\n')
        current_param = None
        
        for line in lines:
            line = line.strip()
            
            # 匹配参数描述格式
            param_match = re.match(r'(\w+)\s*\(([^)]+)\):\s*(.+)', line)
            if param_match:
                param_name, param_type, param_desc = param_match.groups()
                # 找到对应的参数并更新信息
                for param in func_info.parameters:
                    if param.name == param_name:
                        param.description = param_desc.strip()
                        if not param.type_hint:
                            param.type_hint = param_type.strip()
                        break
            
            # 匹配简单格式：param_name: description
            elif ':' in line and not line.startswith(' '):
                parts = line.split(':', 1)
                if len(parts) == 2:
                    param_name = parts[0].strip()
                    param_desc = parts[1].strip()
                    for param in func_info.parameters:
                        if param.name == param_name:
                            param.description = param_desc
                            break


class PythonCodeGenerator:
    """Python 代码生成器"""

    def __init__(self):
        pass

    def generate_function_template(self, func_info: FunctionInfo) -> str:
        """生成函数模板代码"""
        lines = []
        
        # 函数签名
        params = []
        for param in func_info.parameters:
            param_str = param.name
            
            # 添加类型注解
            if param.type_hint:
                param_str += f": {param.type_hint}"
            
            # 添加默认值
            if param.has_default:
                if param.default_value is not None:
                    param_str += f" = {repr(param.default_value)}"
                else:
                    param_str += " = None"
            
            params.append(param_str)
        
        # 返回类型
        return_type = ""
        if func_info.return_type_hint:
            return_type = f" -> {func_info.return_type_hint}"
        
        signature = f"def {func_info.name}({', '.join(params)}){return_type}:"
        lines.append(signature)
        
        # 文档字符串
        if func_info.description or func_info.parameters:
            lines.append('    """')
            if func_info.description:
                lines.append(f"    {func_info.description}")
                lines.append("")
            
            # 参数文档
            for param in func_info.parameters:
                if param.description:
                    type_info = f" ({param.type_hint})" if param.type_hint else ""
                    lines.append(f"    {param.name}{type_info}: {param.description}")
            
            lines.append('    """')
        
        # 函数体
        lines.append("    # TODO: 实现函数逻辑")
        lines.append("    pass")
        
        return '\n'.join(lines)

    def generate_test_code(self, func_info: FunctionInfo) -> str:
        """生成测试代码"""
        lines = []
        lines.append(f"def test_{func_info.name}():")
        lines.append(f'    """测试 {func_info.name} 函数"""')
        
        # 生成测试用例
        if func_info.examples:
            for i, example in enumerate(func_info.examples):
                lines.append(f"    # 测试用例 {i + 1}")
                if "input" in example:
                    args = ", ".join([f"{k}={repr(v)}" for k, v in example["input"].items()])
                    lines.append(f"    result = {func_info.name}({args})")
                    if "output" in example:
                        lines.append(f"    assert result == {repr(example['output'])}")
                lines.append("")
        else:
            # 生成基本测试
            lines.append("    # TODO: 添加测试用例")
            lines.append(f"    result = {func_info.name}()")
            lines.append("    assert result is not None")
        
        return '\n'.join(lines)


# 便捷函数
def analyze_python_code(code: str) -> List[FunctionInfo]:
    """分析 Python 代码的便捷函数"""
    analyzer = PythonScriptAnalyzer()
    return analyzer.analyze_code(code)


def analyze_python_function(code: str, function_name: str) -> Optional[FunctionInfo]:
    """分析指定 Python 函数的便捷函数"""
    analyzer = PythonScriptAnalyzer()
    return analyzer.analyze_function(code, function_name)


def python_to_schema(code: str, function_name: str) -> Dict[str, Any]:
    """从 Python 代码生成 Schema 的便捷函数"""
    func_info = analyze_python_function(code, function_name)
    if not func_info:
        raise ValueError(f"Function '{function_name}' not found in code")
    
    return {
        "function_info": {
            "name": func_info.name,
            "description": func_info.description,
            "docstring": func_info.docstring,
            "parameters": [
                {
                    "name": p.name,
                    "type_hint": p.type_hint,
                    "default_value": p.default_value,
                    "has_default": p.has_default,
                    "parameter_type": p.parameter_type.value,
                    "description": p.description
                }
                for p in func_info.parameters
            ],
            "return_type_hint": func_info.return_type_hint
        },
        "input_schema": func_info.to_input_schema(),
        "output_schema": func_info.to_output_schema()
    }