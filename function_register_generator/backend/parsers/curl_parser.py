import re
import json
import shlex
import urllib.parse
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class HttpMethod(str, Enum):
    """HTTP 方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"

@dataclass
class CurlParseResult:
    """cURL 解析结果"""
    url: str
    method: HttpMethod = HttpMethod.GET
    headers: Dict[str, str] = None
    query_params: Dict[str, str] = None
    body: Optional[Union[str, Dict[str, Any]]] = None
    body_type: str = "none"  # none, json, form, raw
    auth: Optional[Dict[str, str]] = None
    cookies: Dict[str, str] = None
    timeout: Optional[int] = None
    follow_redirects: bool = False
    verify_ssl: bool = True
    proxy: Optional[str] = None
    user_agent: Optional[str] = None
    
    def __post_init__(self):
        if self.headers is None:
            self.headers = {}
        if self.query_params is None:
            self.query_params = {}
        if self.cookies is None:
            self.cookies = {}

class CurlParser:
    """cURL 命令解析器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置解析器状态"""
        self.result = CurlParseResult(url="")
        self.tokens = []
        self.current_index = 0
    
    def parse(self, curl_command: str) -> CurlParseResult:
        """解析 cURL 命令"""
        if not curl_command or not curl_command.strip():
            raise ValueError("Empty cURL command")
        
        try:
            # 预处理命令
            curl_command = self._preprocess_command(curl_command)
            
            # 分词
            self.tokens = self._tokenize(curl_command)
            self.current_index = 0
            
            # 解析各个部分
            self._parse_tokens()
            
            # 验证必要字段
            if not self.result.url:
                raise ValueError("No URL found in cURL command")
            
            # 后处理
            self._post_process()
            
            logger.info(f"Successfully parsed cURL command: {self.result.method} {self.result.url}")
            return self.result
            
        except Exception as e:
            logger.error(f"Failed to parse cURL command: {e}")
            raise ValueError(f"Invalid cURL command: {e}")
    
    def _preprocess_command(self, command: str) -> str:
        """预处理命令"""
        # 处理换行符（cURL 命令可能跨多行）
        command = re.sub(r'\\\s*\n\s*', ' ', command)
        
        # 移除多余的空白字符
        command = re.sub(r'\s+', ' ', command.strip())
        
        # 确保以 curl 开头
        if not command.lower().startswith('curl'):
            # 检查是否是有效的命令
            if not any(keyword in command.lower() for keyword in ['http', 'api', '.']):
                raise ValueError("Invalid command format")
            command = 'curl ' + command
        
        return command
    
    def _tokenize(self, command: str) -> List[str]:
        """分词处理"""
        try:
            # 使用 shlex 进行智能分词，处理引号和转义
            tokens = shlex.split(command)
            return tokens
        except ValueError as e:
            # 如果 shlex 失败，使用简单分词
            logger.warning(f"shlex failed, using simple tokenization: {e}")
            return command.split()
    
    def _parse_tokens(self):
        """解析 token"""
        while self.current_index < len(self.tokens):
            token = self.tokens[self.current_index]
            
            if token.lower() == 'curl':
                self.current_index += 1
                continue
            
            # URL 处理
            if token.startswith('http://') or token.startswith('https://'):
                self._parse_url(token)
            
            # 选项处理
            elif token.startswith('-'):
                self._parse_option(token)
            
            # 可能是 URL（没有协议前缀）
            elif '.' in token and not self.result.url:
                self._parse_url(token)
            
            else:
                # 跳过未识别的 token
                self.current_index += 1
    
    def _parse_url(self, url: str):
        """解析 URL"""
        # 确保 URL 有协议
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # 解析 URL 组件
        parsed = urllib.parse.urlparse(url)
        
        # 提取查询参数
        if parsed.query:
            self.result.query_params = dict(urllib.parse.parse_qsl(parsed.query))
        
        # 重构 URL（不包含查询参数）
        self.result.url = urllib.parse.urlunparse((
            parsed.scheme, parsed.netloc, parsed.path,
            parsed.params, '', parsed.fragment
        ))
        
        self.current_index += 1
    
    def _parse_option(self, option: str):
        """解析选项"""
        # 长选项处理
        if option.startswith('--'):
            self._parse_long_option(option)
        # 短选项处理
        else:
            self._parse_short_option(option)
    
    def _parse_long_option(self, option: str):
        """解析长选项"""
        option = option[2:]  # 移除 --
        
        if '=' in option:
            # --option=value 格式
            key, value = option.split('=', 1)
            self._handle_option(key, value)
        else:
            # --option value 格式
            self._handle_option(option, self._get_next_token())
        
        self.current_index += 1
    
    def _parse_short_option(self, option: str):
        """解析短选项"""
        option = option[1:]  # 移除 -
        
        # 处理组合短选项 (如 -sL)
        if len(option) > 1 and not option[1:].isdigit():
            for char in option:
                self._handle_short_option(char)
        else:
            # 单个短选项
            if len(option) == 1:
                value = self._get_next_token()
            else:
                # -X POST 或 -XPOST 格式
                value = option[1:] if len(option) > 1 else self._get_next_token()
            
            self._handle_short_option(option[0], value)
        
        self.current_index += 1
    
    def _handle_option(self, key: str, value: Optional[str] = None):
        """处理长选项"""
        key = key.lower().replace('-', '_')
        
        if key == 'request':
            self._set_method(value)
        elif key == 'header':
            self._add_header(value)
        elif key == 'data' or key == 'data_raw' or key == 'data_binary':
            self._set_body(value, 'raw')
        elif key == 'data_urlencode':
            self._set_body(value, 'form')
        elif key == 'json':
            self._set_body(value, 'json')
        elif key == 'form':
            self._set_body(value, 'form')
        elif key == 'user':
            self._set_auth(value)
        elif key == 'cookie':
            self._add_cookie(value)
        elif key == 'user_agent':
            self.result.user_agent = value
        elif key == 'referer':
            self.result.headers['Referer'] = value
        elif key == 'location':
            self.result.follow_redirects = True
        elif key == 'insecure':
            self.result.verify_ssl = False
        elif key == 'proxy':
            self.result.proxy = value
        elif key == 'connect_timeout' or key == 'max_time':
            try:
                self.result.timeout = int(float(value))
            except (ValueError, TypeError):
                pass
    
    def _handle_short_option(self, char: str, value: Optional[str] = None):
        """处理短选项"""
        if char == 'X':
            self._set_method(value)
        elif char == 'H':
            self._add_header(value)
        elif char == 'd':
            self._set_body(value, 'raw')
        elif char == 'u':
            self._set_auth(value)
        elif char == 'b':
            self._add_cookie(value)
        elif char == 'A':
            self.result.user_agent = value
        elif char == 'e':
            self.result.headers['Referer'] = value
        elif char == 'L':
            self.result.follow_redirects = True
        elif char == 'k':
            self.result.verify_ssl = False
        elif char == 's':
            pass  # silent mode, ignore
        elif char == 'S':
            pass  # show error, ignore
        elif char == 'f':
            pass  # fail silently, ignore
        elif char == 'v':
            pass  # verbose, ignore
    
    def _set_method(self, method: Optional[str]):
        """设置 HTTP 方法"""
        if method:
            try:
                self.result.method = HttpMethod(method.upper())
            except ValueError:
                logger.warning(f"Unknown HTTP method: {method}")
                self.result.method = HttpMethod.GET
    
    def _add_header(self, header: Optional[str]):
        """添加请求头"""
        if not header:
            return
        
        if ':' in header:
            key, value = header.split(':', 1)
            self.result.headers[key.strip()] = value.strip()
        else:
            # 处理特殊格式的头
            self.result.headers[header] = ""
    
    def _set_body(self, data: Optional[str], body_type: str):
        """设置请求体"""
        if not data:
            return
        
        self.result.body_type = body_type
        
        if body_type == 'json':
            try:
                # 尝试解析 JSON
                if data.startswith('@'):
                    # 文件引用
                    self.result.body = f"@{data[1:]}"
                else:
                    self.result.body = json.loads(data)
            except json.JSONDecodeError:
                # 如果不是有效 JSON，作为原始数据处理
                self.result.body = data
                self.result.body_type = 'raw'
        
        elif body_type == 'form':
            # 处理表单数据
            if '=' in data:
                # key=value 格式
                try:
                    form_data = dict(urllib.parse.parse_qsl(data))
                    self.result.body = form_data
                    self.result.body_type = 'form'
                except:
                    self.result.body = data
            else:
                self.result.body = data
        
        else:
            # 原始数据
            if data.startswith('@'):
                # 文件引用
                self.result.body = f"@{data[1:]}"
            else:
                # 先尝试解析为表单数据
                if '=' in data and '&' in data:
                    try:
                        form_data = dict(urllib.parse.parse_qsl(data))
                        self.result.body = form_data
                        self.result.body_type = 'form'
                        return
                    except:
                        pass
                
                # 尝试解析为 JSON
                try:
                    parsed_json = json.loads(data)
                    self.result.body = parsed_json
                    self.result.body_type = 'json'
                except json.JSONDecodeError:
                    self.result.body = data
    
    def _set_auth(self, auth: Optional[str]):
        """设置认证信息"""
        if not auth:
            return
        
        if ':' in auth:
            username, password = auth.split(':', 1)
            self.result.auth = {
                "type": "basic",
                "username": username,
                "password": password
            }
        else:
            self.result.auth = {
                "type": "bearer",
                "token": auth
            }
    
    def _add_cookie(self, cookie: Optional[str]):
        """添加 Cookie"""
        if not cookie:
            return
        
        if '=' in cookie:
            # name=value 格式
            for pair in cookie.split(';'):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    self.result.cookies[key.strip()] = value.strip()
        else:
            # Cookie 文件引用
            self.result.cookies['_file'] = cookie
    
    def _get_next_token(self) -> Optional[str]:
        """获取下一个 token"""
        next_index = self.current_index + 1
        if next_index < len(self.tokens):
            token = self.tokens[next_index]
            # 如果下一个 token 是选项，返回 None
            if token.startswith('-'):
                return None
            return token
        return None
    
    def _post_process(self):
        """后处理"""
        # 设置默认 Content-Type
        if self.result.body and 'Content-Type' not in self.result.headers:
            if self.result.body_type == 'json':
                self.result.headers['Content-Type'] = 'application/json'
            elif self.result.body_type == 'form':
                self.result.headers['Content-Type'] = 'application/x-www-form-urlencoded'
        
        # 设置默认 User-Agent
        if not self.result.user_agent and 'User-Agent' not in self.result.headers:
            self.result.user_agent = 'curl/7.68.0'
        
        # 如果有请求体但方法是 GET，改为 POST
        if self.result.body and self.result.method == HttpMethod.GET:
            self.result.method = HttpMethod.POST
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "url": self.result.url,
            "method": self.result.method.value,
            "headers": self.result.headers,
            "query_params": self.result.query_params,
            "body": self.result.body,
            "body_type": self.result.body_type,
            "auth": self.result.auth,
            "cookies": self.result.cookies,
            "timeout": self.result.timeout,
            "follow_redirects": self.result.follow_redirects,
            "verify_ssl": self.result.verify_ssl,
            "proxy": self.result.proxy,
            "user_agent": self.result.user_agent
        }

class CurlSchemaInferrer:
    """从 cURL 解析结果推断 JSON Schema"""
    
    def __init__(self):
        pass
    
    def infer_input_schema(self, parse_result: CurlParseResult) -> Dict[str, Any]:
        """推断输入参数 Schema"""
        schema = {
            "type": "object",
            "properties": {},
            "required": []
        }
        
        # 从查询参数推断
        for key, value in parse_result.query_params.items():
            schema["properties"][key] = self._infer_field_type(value)
            schema["properties"][key]["description"] = f"查询参数: {key}"
        
        # 从请求体推断
        if parse_result.body:
            if parse_result.body_type == 'json' and isinstance(parse_result.body, dict):
                # JSON 请求体
                for key, value in parse_result.body.items():
                    schema["properties"][key] = self._infer_field_type(value)
                    schema["properties"][key]["description"] = f"请求体参数: {key}"
                    schema["required"].append(key)
            
            elif parse_result.body_type == 'form' and isinstance(parse_result.body, dict):
                # 表单数据
                for key, value in parse_result.body.items():
                    schema["properties"][key] = self._infer_field_type(value)
                    schema["properties"][key]["description"] = f"表单参数: {key}"
            
            else:
                # 原始数据
                schema["properties"]["data"] = {
                    "type": "string",
                    "description": "请求体数据"
                }
        
        # 从路径参数推断（简单的路径变量检测）
        path_vars = re.findall(r'\{(\w+)\}', parse_result.url)
        for var in path_vars:
            schema["properties"][var] = {
                "type": "string",
                "description": f"路径参数: {var}"
            }
            schema["required"].append(var)
        
        return schema
    
    def infer_output_schema(self, parse_result: CurlParseResult) -> Dict[str, Any]:
        """推断输出 Schema（基于 URL 和方法）"""
        # 基础输出 Schema
        schema = {
            "type": "object",
            "properties": {
                "status": {
                    "type": "integer",
                    "description": "HTTP 状态码"
                }
            }
        }
        
        # 根据 URL 路径推断可能的响应结构
        path = urllib.parse.urlparse(parse_result.url).path.lower()
        
        # 优先级：方法 > 路径关键词
        if parse_result.method == HttpMethod.POST:
            schema["properties"]["id"] = {
                "type": "string",
                "description": "创建的资源ID"
            }
            schema["properties"]["message"] = {
                "type": "string",
                "description": "操作结果消息"
            }
        elif 'list' in path or 'search' in path:
            schema["properties"]["data"] = {
                "type": "array",
                "description": "数据列表"
            }
            schema["properties"]["total"] = {
                "type": "integer",
                "description": "总数"
            }
        elif 'user' in path or 'profile' in path:
            schema["properties"]["user"] = {
                "type": "object",
                "description": "用户信息"
            }
        else:
            schema["properties"]["data"] = {
                "type": "object",
                "description": "响应数据"
            }
        
        return schema
    
    def _infer_field_type(self, value: Any) -> Dict[str, Any]:
        """推断字段类型"""
        if isinstance(value, bool):
            return {"type": "boolean"}
        elif isinstance(value, int):
            return {"type": "integer"}
        elif isinstance(value, float):
            return {"type": "number"}
        elif isinstance(value, str):
            # 尝试推断更具体的类型
            if value.lower() in ('true', 'false'):
                return {"type": "boolean"}
            elif value.isdigit():
                return {"type": "integer"}
            elif self._is_float(value):
                return {"type": "number"}
            elif '@' in value and '.' in value:
                return {"type": "string", "format": "email"}
            elif value.startswith(('http://', 'https://')):
                return {"type": "string", "format": "uri"}
            else:
                return {"type": "string"}
        elif isinstance(value, list):
            if value:
                item_type = self._infer_field_type(value[0])
                return {"type": "array", "items": item_type}
            else:
                return {"type": "array"}
        elif isinstance(value, dict):
            return {"type": "object"}
        else:
            return {"type": "string"}
    
    def _is_float(self, value: str) -> bool:
        """检查字符串是否为浮点数"""
        try:
            float(value)
            return '.' in value
        except ValueError:
            return False

# 便捷函数
def parse_curl(curl_command: str) -> CurlParseResult:
    """解析 cURL 命令的便捷函数"""
    parser = CurlParser()
    return parser.parse(curl_command)

def curl_to_schema(curl_command: str) -> Dict[str, Any]:
    """从 cURL 命令生成 Schema 的便捷函数"""
    parser = CurlParser()
    result = parser.parse(curl_command)
    
    inferrer = CurlSchemaInferrer()
    
    return {
        "parse_result": parser.to_dict(),
        "input_schema": inferrer.infer_input_schema(result),
        "output_schema": inferrer.infer_output_schema(result)
    }