"""
End-to-end tests for the complete tool registration user journey.
"""

import pytest
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options


class TestToolRegistrationJourney:
    """Test the complete user journey for tool registration."""
    
    @pytest.fixture(scope="class")
    def driver(self):
        """Setup Chrome WebDriver for testing."""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in headless mode for CI
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(10)
        
        yield driver
        
        driver.quit()
    
    @pytest.fixture
    def wait(self, driver):
        """Create WebDriverWait instance."""
        return WebDriverWait(driver, 10)
    
    def test_complete_curl_registration_journey(self, driver, wait):
        """Test the complete journey of registering a tool via cURL."""
        # Step 1: Navigate to the application
        driver.get("http://localhost:8080")
        
        # Verify home page loads
        assert "Function Register Generator" in driver.title
        
        # Step 2: Navigate to tool registration
        register_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Register Tool"))
        )
        register_button.click()
        
        # Verify registration page loads
        wait.until(
            EC.presence_of_element_located((By.TEXT, "How would you like to register your tool?"))
        )
        
        # Step 3: Select cURL registration method
        curl_option = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), 'cURL Command')]"))
        )
        curl_option.click()
        
        # Step 4: Enter cURL command
        curl_input = wait.until(
            EC.presence_of_element_located((By.TAG_NAME, "textarea"))
        )
        
        sample_curl = '''curl -X POST "https://api.weather.com/v1/current" \\
     -H "Content-Type: application/json" \\
     -H "Authorization: Bearer token123" \\
     -d '{"location": "New York", "units": "metric"}' '''
        
        curl_input.clear()
        curl_input.send_keys(sample_curl)
        
        # Step 5: Click analyze button
        analyze_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Analyze cURL"))
        )
        analyze_button.click()
        
        # Step 6: Wait for analysis results
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Analysis Results"))
        )
        
        # Verify parsed information is displayed
        assert driver.find_element(By.XPATH, "//span[contains(text(), 'POST')]")
        assert driver.find_element(By.XPATH, "//span[contains(text(), 'api.weather.com')]")
        
        # Step 7: Proceed to tool details
        next_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Next"))
        )
        next_button.click()
        
        # Step 8: Fill in tool details
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Tool Details"))
        )
        
        # Fill tool name
        name_input = driver.find_element(By.XPATH, "//input[@placeholder='Enter tool name']")
        name_input.clear()
        name_input.send_keys("Weather API Tool")
        
        # Fill description
        description_input = driver.find_element(By.XPATH, "//textarea[@placeholder='Describe what your tool does']")
        description_input.clear()
        description_input.send_keys("Get current weather information for any location")
        
        # Select category
        category_dropdown = driver.find_element(By.XPATH, "//select")
        category_dropdown.click()
        api_option = driver.find_element(By.XPATH, "//option[@value='api']")
        api_option.click()
        
        # Add tags
        tags_input = driver.find_element(By.XPATH, "//input[@placeholder='Add tags']")
        tags_input.send_keys("weather")
        tags_input.send_keys(Keys.ENTER)
        tags_input.send_keys("api")
        tags_input.send_keys(Keys.ENTER)
        
        # Step 9: Proceed to schema configuration
        next_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Next"))
        )
        next_button.click()
        
        # Step 10: Review and configure schema
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Schema Configuration"))
        )
        
        # Verify auto-generated schema is displayed
        schema_editor = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "code-editor"))
        )
        assert schema_editor.is_displayed()
        
        # Step 11: Test the tool
        test_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Test Tool"))
        )
        test_button.click()
        
        # Fill test parameters
        location_input = wait.until(
            EC.presence_of_element_located((By.XPATH, "//input[@name='location']"))
        )
        location_input.send_keys("New York")
        
        # Run test
        run_test_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Run Test"))
        )
        run_test_button.click()
        
        # Wait for test results
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Test Results"))
        )
        
        # Step 12: Proceed to publish
        next_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Next"))
        )
        next_button.click()
        
        # Step 13: Review and publish
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Review & Publish"))
        )
        
        # Verify all information is displayed correctly
        assert driver.find_element(By.TEXT, "Weather API Tool")
        assert driver.find_element(By.TEXT, "Get current weather information")
        assert driver.find_element(By.TEXT, "api")
        
        # Publish the tool
        publish_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Publish Tool"))
        )
        publish_button.click()
        
        # Step 14: Verify success
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Congratulations!"))
        )
        
        success_message = driver.find_element(By.TEXT, "Your tool has been successfully published")
        assert success_message.is_displayed()
        
        # Step 15: Navigate to browse tools to verify tool is listed
        browse_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Browse Tools"))
        )
        browse_button.click()
        
        # Search for the newly created tool
        search_input = wait.until(
            EC.presence_of_element_located((By.XPATH, "//input[@placeholder*='Search tools']"))
        )
        search_input.send_keys("Weather API Tool")
        
        # Verify tool appears in search results
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Weather API Tool"))
        )
        
        tool_card = driver.find_element(By.XPATH, "//div[contains(text(), 'Weather API Tool')]")
        assert tool_card.is_displayed()
    
    def test_python_function_registration_journey(self, driver, wait):
        """Test registering a tool via Python function."""
        driver.get("http://localhost:8080/register")
        
        # Select Python function option
        python_option = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), 'Python Function')]"))
        )
        python_option.click()
        
        # Upload or paste Python function
        code_input = wait.until(
            EC.presence_of_element_located((By.TAG_NAME, "textarea"))
        )
        
        sample_function = '''
def calculate_bmi(weight: float, height: float) -> dict:
    """
    Calculate Body Mass Index (BMI).
    
    Args:
        weight: Weight in kilograms
        height: Height in meters
        
    Returns:
        Dictionary with BMI value and category
    """
    bmi = weight / (height ** 2)
    
    if bmi < 18.5:
        category = "Underweight"
    elif bmi < 25:
        category = "Normal weight"
    elif bmi < 30:
        category = "Overweight"
    else:
        category = "Obese"
    
    return {
        "bmi": round(bmi, 2),
        "category": category
    }
'''
        
        code_input.clear()
        code_input.send_keys(sample_function)
        
        # Analyze function
        analyze_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Analyze Function"))
        )
        analyze_button.click()
        
        # Verify analysis results
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Function Analysis"))
        )
        
        # Check that function signature was parsed
        assert driver.find_element(By.TEXT, "calculate_bmi")
        assert driver.find_element(By.TEXT, "weight")
        assert driver.find_element(By.TEXT, "height")
        
        # Continue with the rest of the registration process
        next_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Next"))
        )
        next_button.click()
        
        # Fill tool details
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Tool Details"))
        )
        
        name_input = driver.find_element(By.XPATH, "//input[@placeholder='Enter tool name']")
        name_input.clear()
        name_input.send_keys("BMI Calculator")
        
        description_input = driver.find_element(By.XPATH, "//textarea[@placeholder='Describe what your tool does']")
        description_input.clear()
        description_input.send_keys("Calculate Body Mass Index and health category")
        
        # Select utility category
        category_dropdown = driver.find_element(By.XPATH, "//select")
        category_dropdown.click()
        utility_option = driver.find_element(By.XPATH, "//option[@value='utility']")
        utility_option.click()
        
        # Complete registration
        next_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Next"))
        )
        next_button.click()
        
        # Skip schema step (auto-generated)
        next_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Next"))
        )
        next_button.click()
        
        # Publish
        publish_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Publish Tool"))
        )
        publish_button.click()
        
        # Verify success
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Congratulations!"))
        )
    
    def test_search_and_filter_functionality(self, driver, wait):
        """Test the search and filter functionality in tool browsing."""
        driver.get("http://localhost:8080/tools")
        
        # Wait for tools page to load
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Browse Tools"))
        )
        
        # Test search functionality
        search_input = wait.until(
            EC.presence_of_element_located((By.XPATH, "//input[@placeholder*='Search tools']"))
        )
        search_input.send_keys("weather")
        
        # Wait for search results
        time.sleep(2)  # Allow for debounced search
        
        # Verify search results contain weather-related tools
        tool_cards = driver.find_elements(By.CLASS_NAME, "tool-card")
        assert len(tool_cards) > 0
        
        # Test filter functionality
        filters_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Filters"))
        )
        filters_button.click()
        
        # Select API category filter
        api_filter = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//span[text()='API']"))
        )
        api_filter.click()
        
        # Apply filters
        apply_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Apply"))
        )
        apply_button.click()
        
        # Verify filtered results
        time.sleep(2)  # Allow for filter application
        filtered_cards = driver.find_elements(By.CLASS_NAME, "tool-card")
        
        # All visible tools should be API category
        for card in filtered_cards:
            assert "api" in card.text.lower() or "API" in card.text
    
    def test_tool_detail_view_and_try_out(self, driver, wait):
        """Test viewing tool details and trying out functionality."""
        driver.get("http://localhost:8080/tools")
        
        # Wait for tools to load
        wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "tool-card"))
        )
        
        # Click on the first tool card
        first_tool = driver.find_element(By.CLASS_NAME, "tool-card")
        first_tool.click()
        
        # Wait for detail panel to open
        wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "tool-detail-panel"))
        )
        
        # Verify detail panel content
        assert driver.find_element(By.TEXT, "Overview")
        assert driver.find_element(By.TEXT, "Schema")
        assert driver.find_element(By.TEXT, "Try Out")
        
        # Switch to Try Out tab
        try_out_tab = driver.find_element(By.TEXT, "Try Out")
        try_out_tab.click()
        
        # Fill in test parameters (if any)
        parameter_inputs = driver.find_elements(By.XPATH, "//input[@type='text']")
        for input_field in parameter_inputs:
            input_field.send_keys("test value")
        
        # Run test
        run_test_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Run Test"))
        )
        run_test_button.click()
        
        # Wait for test results
        wait.until(
            EC.presence_of_element_located((By.TEXT, "Test Results"))
        )
        
        # Verify test results are displayed
        results_section = driver.find_element(By.CLASS_NAME, "test-results")
        assert results_section.is_displayed()
    
    def test_responsive_design(self, driver, wait):
        """Test responsive design on different screen sizes."""
        # Test desktop view
        driver.set_window_size(1920, 1080)
        driver.get("http://localhost:8080")
        
        # Verify desktop layout
        nav_bar = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "nav-bar"))
        )
        assert nav_bar.is_displayed()
        
        # Test tablet view
        driver.set_window_size(768, 1024)
        driver.refresh()
        
        # Verify tablet layout adjustments
        wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "nav-bar"))
        )
        
        # Test mobile view
        driver.set_window_size(375, 667)
        driver.refresh()
        
        # Verify mobile layout
        wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "mobile-nav"))
        )
        
        # Test navigation on mobile
        menu_button = driver.find_element(By.CLASS_NAME, "menu-button")
        menu_button.click()
        
        # Verify mobile menu opens
        mobile_menu = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "mobile-menu"))
        )
        assert mobile_menu.is_displayed()
    
    def test_error_handling(self, driver, wait):
        """Test error handling scenarios."""
        driver.get("http://localhost:8080/register")
        
        # Try to proceed without selecting registration method
        next_button = wait.until(
            EC.element_to_be_clickable((By.TEXT, "Next"))
        )
        next_button.click()
        
        # Verify error message is displayed
        error_message = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "error-message"))
        )
        assert "Please select a registration method" in error_message.text
        
        # Test invalid cURL command
        curl_option = driver.find_element(By.XPATH, "//div[contains(text(), 'cURL Command')]")
        curl_option.click()
        
        curl_input = wait.until(
            EC.presence_of_element_located((By.TAG_NAME, "textarea"))
        )
        curl_input.send_keys("invalid curl command")
        
        analyze_button = driver.find_element(By.TEXT, "Analyze cURL")
        analyze_button.click()
        
        # Verify error handling for invalid cURL
        error_message = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "error-message"))
        )
        assert "Invalid cURL command" in error_message.text or "Failed to parse" in error_message.text
