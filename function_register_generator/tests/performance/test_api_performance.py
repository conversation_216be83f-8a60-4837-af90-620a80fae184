"""
Performance tests for API endpoints.
"""

import pytest
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor
import requests
from locust import HttpUser, task, between


class TestAPIPerformance:
    """Performance tests for API endpoints."""
    
    BASE_URL = "http://localhost:8000"
    
    def test_single_tool_retrieval_performance(self):
        """Test performance of retrieving a single tool."""
        url = f"{self.BASE_URL}/api/v1/tools/test-tool-1"
        
        # Warm up
        for _ in range(5):
            requests.get(url)
        
        # Measure performance
        times = []
        for _ in range(100):
            start_time = time.time()
            response = requests.get(url)
            end_time = time.time()
            
            assert response.status_code == 200
            times.append(end_time - start_time)
        
        # Analyze results
        avg_time = statistics.mean(times)
        p95_time = statistics.quantiles(times, n=20)[18]  # 95th percentile
        p99_time = statistics.quantiles(times, n=100)[98]  # 99th percentile
        
        print(f"Single tool retrieval performance:")
        print(f"  Average: {avg_time:.3f}s")
        print(f"  95th percentile: {p95_time:.3f}s")
        print(f"  99th percentile: {p99_time:.3f}s")
        
        # Performance assertions
        assert avg_time < 0.1, f"Average response time {avg_time:.3f}s exceeds 100ms"
        assert p95_time < 0.2, f"95th percentile {p95_time:.3f}s exceeds 200ms"
        assert p99_time < 0.5, f"99th percentile {p99_time:.3f}s exceeds 500ms"
    
    def test_tools_list_performance(self):
        """Test performance of listing tools with pagination."""
        url = f"{self.BASE_URL}/api/v1/tools"
        
        # Test different page sizes
        page_sizes = [10, 50, 100]
        
        for page_size in page_sizes:
            params = {"limit": page_size, "offset": 0}
            
            # Warm up
            for _ in range(3):
                requests.get(url, params=params)
            
            # Measure performance
            times = []
            for _ in range(50):
                start_time = time.time()
                response = requests.get(url, params=params)
                end_time = time.time()
                
                assert response.status_code == 200
                times.append(end_time - start_time)
            
            avg_time = statistics.mean(times)
            print(f"Tools list (page_size={page_size}) average: {avg_time:.3f}s")
            
            # Performance should scale reasonably with page size
            assert avg_time < 0.5, f"Tools list with {page_size} items took {avg_time:.3f}s"
    
    def test_search_performance(self):
        """Test performance of search functionality."""
        url = f"{self.BASE_URL}/api/v1/tools/search"
        
        search_queries = [
            "weather",
            "api",
            "data processing",
            "machine learning",
            "utility tool"
        ]
        
        for query in search_queries:
            params = {"q": query, "limit": 20}
            
            # Warm up
            for _ in range(3):
                requests.get(url, params=params)
            
            # Measure performance
            times = []
            for _ in range(30):
                start_time = time.time()
                response = requests.get(url, params=params)
                end_time = time.time()
                
                assert response.status_code == 200
                times.append(end_time - start_time)
            
            avg_time = statistics.mean(times)
            print(f"Search '{query}' average: {avg_time:.3f}s")
            
            # Search should be fast
            assert avg_time < 0.3, f"Search for '{query}' took {avg_time:.3f}s"
    
    def test_function_calling_performance(self):
        """Test performance of function calling endpoint."""
        url = f"{self.BASE_URL}/api/v1/function-calling/call"
        
        call_data = {
            "tool_id": "test-tool-1",
            "arguments": {"location": "New York"}
        }
        
        # Warm up
        for _ in range(5):
            requests.post(url, json=call_data)
        
        # Measure performance
        times = []
        for _ in range(50):
            start_time = time.time()
            response = requests.post(url, json=call_data)
            end_time = time.time()
            
            assert response.status_code == 200
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        p95_time = statistics.quantiles(times, n=20)[18]
        
        print(f"Function calling performance:")
        print(f"  Average: {avg_time:.3f}s")
        print(f"  95th percentile: {p95_time:.3f}s")
        
        # Function calling should be reasonably fast
        assert avg_time < 2.0, f"Function calling average {avg_time:.3f}s exceeds 2s"
        assert p95_time < 5.0, f"Function calling 95th percentile {p95_time:.3f}s exceeds 5s"
    
    def test_concurrent_requests_performance(self):
        """Test performance under concurrent load."""
        url = f"{self.BASE_URL}/api/v1/tools"
        
        def make_request():
            start_time = time.time()
            response = requests.get(url)
            end_time = time.time()
            return response.status_code, end_time - start_time
        
        # Test with different concurrency levels
        concurrency_levels = [5, 10, 20]
        
        for concurrency in concurrency_levels:
            with ThreadPoolExecutor(max_workers=concurrency) as executor:
                # Submit concurrent requests
                futures = [executor.submit(make_request) for _ in range(concurrency * 10)]
                
                # Collect results
                results = [future.result() for future in futures]
                
                # Analyze results
                status_codes = [result[0] for result in results]
                times = [result[1] for result in results]
                
                success_rate = sum(1 for code in status_codes if code == 200) / len(status_codes)
                avg_time = statistics.mean(times)
                
                print(f"Concurrency {concurrency}:")
                print(f"  Success rate: {success_rate:.2%}")
                print(f"  Average time: {avg_time:.3f}s")
                
                # Performance assertions
                assert success_rate >= 0.95, f"Success rate {success_rate:.2%} below 95%"
                assert avg_time < 1.0, f"Average time {avg_time:.3f}s exceeds 1s under load"
    
    def test_memory_usage_during_load(self):
        """Test memory usage during sustained load."""
        import psutil
        import os
        
        # Get current process (assuming API server is running)
        # In a real test, you'd monitor the actual API server process
        process = psutil.Process(os.getpid())
        
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        url = f"{self.BASE_URL}/api/v1/tools"
        
        # Generate sustained load
        for i in range(1000):
            response = requests.get(url)
            assert response.status_code == 200
            
            # Check memory every 100 requests
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                print(f"Request {i}: Memory usage {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
                
                # Memory shouldn't grow excessively
                assert memory_increase < 100, f"Memory increased by {memory_increase:.1f}MB"


class APILoadTestUser(HttpUser):
    """Locust user for load testing."""
    
    wait_time = between(1, 3)
    
    def on_start(self):
        """Setup for each user."""
        self.client.verify = False  # Disable SSL verification for testing
    
    @task(3)
    def list_tools(self):
        """List tools - most common operation."""
        self.client.get("/api/v1/tools?limit=20")
    
    @task(2)
    def search_tools(self):
        """Search tools."""
        queries = ["weather", "api", "data", "utility"]
        query = self.random.choice(queries)
        self.client.get(f"/api/v1/tools/search?q={query}")
    
    @task(1)
    def get_tool_details(self):
        """Get specific tool details."""
        # In a real test, you'd use actual tool IDs
        tool_id = "test-tool-1"
        self.client.get(f"/api/v1/tools/{tool_id}")
    
    @task(1)
    def get_function_schemas(self):
        """Get function schemas."""
        self.client.get("/api/v1/function-calling/function-schema")
    
    @task(1)
    def call_function(self):
        """Call a function."""
        data = {
            "tool_id": "test-tool-1",
            "arguments": {"location": "New York"}
        }
        self.client.post("/api/v1/function-calling/call", json=data)


class TestDatabasePerformance:
    """Performance tests for database operations."""
    
    def test_tool_query_performance(self, db_session):
        """Test performance of tool queries."""
        from app.models.tool import Tool
        
        # Create test data
        tools = []
        for i in range(1000):
            tool = Tool(
                id=f"perf-tool-{i}",
                name=f"Performance Test Tool {i}",
                description=f"Tool for performance testing {i}",
                category="utility",
                user_id="test-user",
                status="active"
            )
            tools.append(tool)
        
        db_session.add_all(tools)
        db_session.commit()
        
        # Test query performance
        start_time = time.time()
        result = db_session.query(Tool).filter(Tool.status == "active").limit(50).all()
        end_time = time.time()
        
        query_time = end_time - start_time
        print(f"Query 50 tools from 1000: {query_time:.3f}s")
        
        assert len(result) == 50
        assert query_time < 0.1, f"Query took {query_time:.3f}s, exceeds 100ms"
    
    def test_search_query_performance(self, db_session):
        """Test performance of search queries."""
        from app.models.tool import Tool
        from sqlalchemy import or_
        
        # Test search query
        search_term = "test"
        
        start_time = time.time()
        result = db_session.query(Tool).filter(
            or_(
                Tool.name.ilike(f"%{search_term}%"),
                Tool.description.ilike(f"%{search_term}%")
            )
        ).limit(20).all()
        end_time = time.time()
        
        query_time = end_time - start_time
        print(f"Search query: {query_time:.3f}s")
        
        assert query_time < 0.2, f"Search query took {query_time:.3f}s, exceeds 200ms"
    
    def test_function_call_logging_performance(self, db_session):
        """Test performance of function call logging."""
        from app.models.function_call import FunctionCall
        
        # Test bulk insert performance
        function_calls = []
        for i in range(1000):
            call = FunctionCall(
                id=f"perf-call-{i}",
                tool_id="test-tool-1",
                user_id="test-user",
                arguments='{"test": "data"}',
                result='{"result": "success"}',
                execution_time=0.5,
                success=True
            )
            function_calls.append(call)
        
        start_time = time.time()
        db_session.add_all(function_calls)
        db_session.commit()
        end_time = time.time()
        
        insert_time = end_time - start_time
        print(f"Insert 1000 function calls: {insert_time:.3f}s")
        
        assert insert_time < 2.0, f"Bulk insert took {insert_time:.3f}s, exceeds 2s"


class TestFrontendPerformance:
    """Performance tests for frontend components."""
    
    def test_tool_list_rendering_performance(self):
        """Test performance of rendering large tool lists."""
        # This would be implemented using Flutter integration tests
        # or browser automation tools to measure rendering performance
        pass
    
    def test_search_debouncing_performance(self):
        """Test search debouncing performance."""
        # This would test that search requests are properly debounced
        # and don't overwhelm the backend
        pass
    
    def test_infinite_scroll_performance(self):
        """Test infinite scroll performance with large datasets."""
        # This would test that infinite scroll doesn't cause memory leaks
        # or performance degradation
        pass


if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "-s"])
