#!/usr/bin/env python3
"""
Python 脚本沙盒演示

这个脚本演示了如何使用 Python 沙盒服务来安全地验证和执行 Python 代码，
包括静态安全检查、Docker 容器隔离、资源限制等功能。
"""

import asyncio
import json
import sys
import os

# 添加 backend 目录到 Python 路径
sys.path.append('backend')

from services.python_sandbox_service import (
    PythonSandboxService,
    PythonSandboxRequest,
    PythonSandboxConfig,
    get_python_sandbox_service
)


async def demo_basic_function_validation():
    """演示基本的 Python 函数验证"""
    print("=" * 60)
    print("演示 1: 基本 Python 函数验证")
    print("=" * 60)
    
    # 创建 Python 沙盒服务
    sandbox = PythonSandboxService()
    
    # 测试一个简单的函数
    script_content = '''
import math

def calculate_circle_area(radius):
    """计算圆的面积"""
    if radius < 0:
        raise ValueError("半径不能为负数")
    return math.pi * radius * radius

def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
'''
    
    print("测试脚本内容:")
    print(script_content)
    
    # 验证函数
    result = await sandbox.validate_python_function(script_content, "calculate_circle_area")
    
    print(f"\n验证结果:")
    print(f"成功: {result['success']}")
    print(f"函数找到: {result['function_found']}")
    print(f"语法有效: {result['syntax_valid']}")
    
    if result['function_found']:
        func_info = result['function_info']
        print(f"函数名: {func_info['name']}")
        print(f"参数: {func_info['args']}")
        print(f"文档字符串: {func_info['docstring']}")
        print(f"行号: {func_info['line_number']}")
    
    if result.get('security_warnings'):
        print(f"安全警告:")
        for warning in result['security_warnings']:
            print(f"⚠️  {warning}")
    else:
        print("✅ 无安全问题")


async def demo_security_checks():
    """演示安全检查功能"""
    print("\n" + "=" * 60)
    print("演示 2: 安全检查功能")
    print("=" * 60)
    
    sandbox = PythonSandboxService()
    
    # 测试不同安全级别的代码
    test_cases = [
        {
            "name": "安全代码",
            "code": '''
import json
import math

def safe_function(data):
    """安全的数据处理函数"""
    result = []
    for item in data:
        if isinstance(item, (int, float)):
            result.append(math.sqrt(abs(item)))
        else:
            result.append(str(item).upper())
    return {"processed": result, "count": len(result)}
'''
        },
        {
            "name": "危险代码 - 系统调用",
            "code": '''
import os
import subprocess

def dangerous_function():
    """危险的系统调用函数"""
    os.system("ls -la")
    subprocess.call(["curl", "http://example.com"])
    return "system calls executed"
'''
        },
        {
            "name": "危险代码 - 动态执行",
            "code": '''
def malicious_function(code):
    """恶意的动态执行函数"""
    eval(code)
    exec("print('malicious code')")
    return __import__('os').listdir('/')
'''
        },
        {
            "name": "危险代码 - 网络访问",
            "code": '''
import socket
import threading

def network_function():
    """网络访问函数"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect(("example.com", 80))
    return "network access"
'''
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        print("-" * 40)
        
        warnings = sandbox._static_security_check(test_case['code'])
        
        if warnings:
            print("🚨 发现安全问题:")
            for warning in warnings:
                print(f"   - {warning}")
        else:
            print("✅ 安全检查通过")


async def demo_script_preparation():
    """演示脚本准备功能"""
    print("\n" + "=" * 60)
    print("演示 3: 脚本准备功能")
    print("=" * 60)
    
    sandbox = PythonSandboxService()
    
    # 测试脚本准备
    request = PythonSandboxRequest(
        script_content='''
def greet_user(name, age=25, active=True):
    """问候用户函数"""
    status = "活跃" if active else "不活跃"
    return {
        "message": f"你好 {name}，你 {age} 岁",
        "status": status,
        "timestamp": "2023-12-25T10:30:00Z"
    }
''',
        entry_function="greet_user",
        function_args={
            "name": "张三",
            "age": 30,
            "active": True
        }
    )
    
    print("原始脚本:")
    print(request.script_content)
    
    print(f"\n入口函数: {request.entry_function}")
    print(f"函数参数: {json.dumps(request.function_args, ensure_ascii=False)}")
    
    # 准备脚本
    script_path = await sandbox._prepare_script(request)
    
    try:
        print(f"\n准备后的脚本文件: {script_path}")
        
        with open(script_path, 'r', encoding='utf-8') as f:
            prepared_content = f.read()
        
        print("\n准备后的脚本内容:")
        print(prepared_content)
        
    finally:
        # 清理临时文件
        if os.path.exists(script_path):
            os.unlink(script_path)


async def demo_output_parsing():
    """演示输出解析功能"""
    print("\n" + "=" * 60)
    print("演示 4: 输出解析功能")
    print("=" * 60)
    
    sandbox = PythonSandboxService()
    
    # 测试不同类型的输出
    test_outputs = [
        {
            "name": "成功执行输出",
            "stdout": '''
Debug: Starting function
SANDBOX_RESULT_START
{"success": true, "result": {"message": "Hello World", "count": 42}}
SANDBOX_RESULT_END
Debug: Function completed
'''
        },
        {
            "name": "错误执行输出",
            "stdout": '''
SANDBOX_RESULT_START
{"success": false, "error": "ValueError: Invalid input", "traceback": "Traceback..."}
SANDBOX_RESULT_END
'''
        },
        {
            "name": "无标记输出",
            "stdout": '''
Just some regular output without markers
This might happen if the script fails early
'''
        },
        {
            "name": "包含特殊字符的输出",
            "stdout": '''
SANDBOX_RESULT_START
{"success": true, "result": "Hello, 世界! 🌍 测试中文"}
SANDBOX_RESULT_END
'''
        }
    ]
    
    for test_case in test_outputs:
        print(f"\n测试: {test_case['name']}")
        print("原始输出:")
        print(repr(test_case['stdout']))
        
        parsed_result = sandbox._parse_execution_output(test_case['stdout'])
        print("解析结果:")
        print(json.dumps(parsed_result, indent=2, ensure_ascii=False))


async def demo_test_script_generation():
    """演示测试脚本生成"""
    print("\n" + "=" * 60)
    print("演示 5: 测试脚本生成")
    print("=" * 60)
    
    sandbox = PythonSandboxService()
    
    # 测试不同类型的函数
    function_examples = [
        {
            "name": "calculate_bmi",
            "args": ["weight", "height"]
        },
        {
            "name": "get_current_time",
            "args": []
        },
        {
            "name": "process_user_data",
            "args": ["user_name", "user_age", "is_active"]
        },
        {
            "name": "send_notification",
            "args": ["message", "recipient_email"]
        }
    ]
    
    for func_info in function_examples:
        print(f"\n为函数 '{func_info['name']}' 生成测试脚本:")
        print("-" * 50)
        
        test_script = sandbox.generate_test_script(func_info)
        print(test_script)


async def demo_comprehensive_validation():
    """演示综合验证功能"""
    print("\n" + "=" * 60)
    print("演示 6: 综合验证功能")
    print("=" * 60)
    
    sandbox = PythonSandboxService()
    
    # 复杂的 Python 脚本
    complex_script = '''
import json
import math
from datetime import datetime, timedelta

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, name="default"):
        self.name = name
        self.processed_count = 0
    
    def process_item(self, item):
        """处理单个数据项"""
        self.processed_count += 1
        return f"Processed by {self.name}: {item}"

def analyze_sales_data(sales_records, time_period_days=30):
    """
    分析销售数据
    
    Args:
        sales_records: 销售记录列表，每个记录包含 amount 和 date
        time_period_days: 分析的时间周期（天数）
    
    Returns:
        分析结果字典，包含总销售额、平均值、趋势等
    """
    if not sales_records:
        return {"error": "没有销售记录"}
    
    # 过滤时间范围内的记录
    cutoff_date = datetime.now() - timedelta(days=time_period_days)
    recent_records = [
        record for record in sales_records 
        if datetime.fromisoformat(record.get('date', '2023-01-01')) >= cutoff_date
    ]
    
    if not recent_records:
        return {"error": "指定时间范围内没有销售记录"}
    
    # 计算统计数据
    amounts = [record['amount'] for record in recent_records]
    total_sales = sum(amounts)
    average_sale = total_sales / len(amounts)
    max_sale = max(amounts)
    min_sale = min(amounts)
    
    # 计算标准差
    variance = sum((x - average_sale) ** 2 for x in amounts) / len(amounts)
    std_deviation = math.sqrt(variance)
    
    # 简单趋势分析（比较前半期和后半期）
    mid_point = len(recent_records) // 2
    first_half_avg = sum(amounts[:mid_point]) / mid_point if mid_point > 0 else 0
    second_half_avg = sum(amounts[mid_point:]) / (len(amounts) - mid_point) if len(amounts) > mid_point else 0
    
    trend = "上升" if second_half_avg > first_half_avg else "下降" if second_half_avg < first_half_avg else "平稳"
    
    return {
        "period_days": time_period_days,
        "total_records": len(recent_records),
        "total_sales": round(total_sales, 2),
        "average_sale": round(average_sale, 2),
        "max_sale": max_sale,
        "min_sale": min_sale,
        "std_deviation": round(std_deviation, 2),
        "trend": trend,
        "analysis_date": datetime.now().isoformat()
    }

def helper_function():
    """辅助函数"""
    return "helper result"
'''
    
    print("复杂脚本验证:")
    print("脚本长度:", len(complex_script), "字符")
    
    # 验证主函数
    result = await sandbox.validate_python_function(complex_script, "analyze_sales_data")
    
    print(f"\n验证结果:")
    print(f"成功: {result['success']}")
    print(f"函数找到: {result['function_found']}")
    print(f"语法有效: {result['syntax_valid']}")
    
    if result['function_found']:
        func_info = result['function_info']
        print(f"\n函数信息:")
        print(f"名称: {func_info['name']}")
        print(f"参数: {func_info['args']}")
        print(f"默认参数数量: {func_info['defaults']}")
        print(f"文档字符串: {func_info['docstring'][:100]}...")
        print(f"行号: {func_info['line_number']}")
    
    # 安全检查
    warnings = sandbox._static_security_check(complex_script)
    if warnings:
        print(f"\n安全警告:")
        for warning in warnings:
            print(f"⚠️  {warning}")
    else:
        print("\n✅ 安全检查通过")
    
    # 生成测试脚本
    if result['function_found']:
        print(f"\n生成的测试脚本:")
        print("-" * 50)
        test_script = sandbox.generate_test_script(result['function_info'])
        print(test_script)


async def demo_configuration_and_limits():
    """演示配置和限制"""
    print("\n" + "=" * 60)
    print("演示 7: 配置和限制")
    print("=" * 60)
    
    config = PythonSandboxConfig()
    
    print("沙盒配置:")
    print(f"默认超时: {config.DEFAULT_TIMEOUT} 秒")
    print(f"最大超时: {config.MAX_TIMEOUT} 秒")
    print(f"内存限制: {config.MEMORY_LIMIT}")
    print(f"CPU 限制: {config.CPU_LIMIT}")
    print(f"Docker 镜像: {config.DOCKER_IMAGE}")
    print(f"工作目录: {config.DOCKER_WORK_DIR}")
    print(f"运行用户: {config.DOCKER_USER}")
    
    print(f"\n允许的模块 ({len(config.ALLOWED_MODULES)} 个):")
    for i, module in enumerate(sorted(config.ALLOWED_MODULES)):
        if i % 6 == 0:
            print()
        print(f"{module:12}", end=" ")
    print()
    
    print(f"\n禁止的模式 ({len(config.FORBIDDEN_PATTERNS)} 个):")
    for pattern in config.FORBIDDEN_PATTERNS[:10]:  # 只显示前10个
        print(f"  - {pattern}")
    if len(config.FORBIDDEN_PATTERNS) > 10:
        print(f"  ... 还有 {len(config.FORBIDDEN_PATTERNS) - 10} 个模式")
    
    # 测试请求验证
    print(f"\n请求验证测试:")
    
    # 有效请求
    try:
        valid_request = PythonSandboxRequest(
            script_content="def test(): return 'hello'",
            entry_function="test",
            timeout=30
        )
        print("✅ 有效请求创建成功")
    except Exception as e:
        print(f"❌ 有效请求创建失败: {e}")
    
    # 无效请求测试
    invalid_cases = [
        ("空脚本", "", "test"),
        ("无效函数名", "def test(): pass", "123invalid"),
        ("超时过大", "def test(): pass", "test", 400),
    ]
    
    for case_name, script, func_name, *args in invalid_cases:
        try:
            timeout = args[0] if args else 30
            PythonSandboxRequest(
                script_content=script,
                entry_function=func_name,
                timeout=timeout
            )
            print(f"❌ {case_name}: 应该失败但成功了")
        except Exception as e:
            print(f"✅ {case_name}: 正确捕获错误")


async def main():
    """主函数"""
    print("Python 脚本沙盒服务演示")
    print("这个演示展示了如何使用 Python 沙盒服务安全地验证和执行 Python 代码")
    
    try:
        await demo_basic_function_validation()
        await demo_security_checks()
        await demo_script_preparation()
        await demo_output_parsing()
        await demo_test_script_generation()
        await demo_comprehensive_validation()
        await demo_configuration_and_limits()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\nPython 沙盒服务的主要功能:")
        print("✓ 静态代码安全检查和 AST 分析")
        print("✓ Docker 容器隔离执行环境")
        print("✓ 资源限制和超时控制")
        print("✓ 模块白名单和危险函数检测")
        print("✓ 智能脚本准备和结果解析")
        print("✓ 自动测试脚本生成")
        print("✓ 函数签名和文档提取")
        print("✓ 错误处理和异常捕获")
        
        print("\n安全特性:")
        print("• 禁止系统调用和文件访问")
        print("• 阻止网络访问和进程创建")
        print("• 防止动态代码执行 (eval/exec)")
        print("• 模块导入白名单控制")
        print("• 容器化隔离和资源限制")
        print("• 只读文件系统和临时目录")
        
        print("\n支持的功能:")
        print("• Python 3.11 标准库支持")
        print("• 数学计算和数据处理")
        print("• JSON 和文本处理")
        print("• 日期时间操作")
        print("• 受限的网络库 (requests)")
        print("• 自定义函数和类定义")
        
        print("\n注意事项:")
        print("⚠️  需要 Docker 环境支持")
        print("⚠️  执行时间受超时限制")
        print("⚠️  内存和 CPU 使用受限")
        print("⚠️  网络访问默认禁用")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())