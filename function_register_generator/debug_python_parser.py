#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from parsers.python_parser import parse_python

code = '''
def greet(name: str, age: int = 25, active: bool = True):
    """Greet a person.
    
    Args:
        name (str): Person's name
        age (int): Person's age
        active (bool): Whether the person is active
    
    Returns:
        str: Greeting message
    """
    return f"Hello {name}, you are {age} years old"
'''

result = parse_python(code)
func = result.functions[0]

print(f"Function: {func.name}")
print(f"Description: {func.description}")

print(f"Parameters:")
for param in func.parameters:
    print(f"  - {param.name}: desc={param.description}")