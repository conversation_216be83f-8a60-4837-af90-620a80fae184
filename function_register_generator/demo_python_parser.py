#!/usr/bin/env python3
"""
Python 脚本分析器演示

这个脚本演示了如何使用 PythonScriptAnalyzer 来分析 Python 代码，
提取函数信息并生成 JSON Schema。
"""

import json
import sys
import os

# 添加 backend 目录到 Python 路径
sys.path.append('backend')

from parsers.python_parser import (
    PythonScriptAnalyzer, 
    analyze_python_code, 
    analyze_python_function, 
    python_to_schema,
    PythonCodeGenerator
)


def demo_basic_analysis():
    """演示基本的 Python 代码分析"""
    print("=" * 60)
    print("演示 1: 基本 Python 代码分析")
    print("=" * 60)
    
    code = '''
def calculate_area(length: float, width: float = 1.0) -> float:
    """
    计算矩形面积
    
    Args:
        length (float): 矩形的长度
        width (float): 矩形的宽度，默认为1.0
    
    Returns:
        float: 矩形的面积
    """
    return length * width

def greet_user(name: str, age: int = 25, active: bool = True) -> str:
    """问候用户"""
    status = "活跃" if active else "不活跃"
    return f"你好 {name}，你 {age} 岁，状态：{status}"
'''
    
    print("分析的代码:")
    print(code)
    print("\n分析结果:")
    
    functions = analyze_python_code(code)
    
    for func in functions:
        print(f"\n函数: {func.name}")
        print(f"描述: {func.description}")
        print(f"返回类型: {func.return_type_hint}")
        print(f"参数数量: {len(func.parameters)}")
        
        for param in func.parameters:
            default_info = f" (默认值: {param.default_value})" if param.has_default else ""
            print(f"  - {param.name}: {param.type_hint}{default_info}")
            if param.description:
                print(f"    描述: {param.description}")


def demo_schema_generation():
    """演示 JSON Schema 生成"""
    print("\n" + "=" * 60)
    print("演示 2: JSON Schema 生成")
    print("=" * 60)
    
    code = '''
from typing import List, Dict, Optional

def process_user_data(
    users: List[Dict[str, str]], 
    config: Dict[str, int],
    callback: Optional[callable] = None,
    debug: bool = False
) -> Dict[str, any]:
    """
    处理用户数据
    
    Args:
        users (List[Dict[str, str]]): 用户数据列表
        config (Dict[str, int]): 配置参数
        callback (Optional[callable]): 可选的回调函数
        debug (bool): 是否启用调试模式
    
    Returns:
        Dict[str, any]: 处理结果
    """
    result = {"processed": len(users), "debug": debug}
    if callback:
        callback(result)
    return result
'''
    
    print("生成 process_user_data 函数的 Schema:")
    
    try:
        schema = python_to_schema(code, "process_user_data")
        
        print("\n函数信息:")
        func_info = schema["function_info"]
        print(f"名称: {func_info['name']}")
        print(f"描述: {func_info['description']}")
        print(f"参数数量: {len(func_info['parameters'])}")
        
        print("\n输入 Schema:")
        print(json.dumps(schema["input_schema"], indent=2, ensure_ascii=False))
        
        print("\n输出 Schema:")
        print(json.dumps(schema["output_schema"], indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"生成 Schema 时出错: {e}")


def demo_complex_analysis():
    """演示复杂代码分析"""
    print("\n" + "=" * 60)
    print("演示 3: 复杂代码分析（类和方法）")
    print("=" * 60)
    
    code = '''
class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, batch_size: int = 100):
        """
        初始化数据处理器
        
        Args:
            batch_size (int): 批处理大小
        """
        self.batch_size = batch_size
    
    def process_batch(self, data: List[dict], *args, **kwargs) -> List[dict]:
        """
        批量处理数据
        
        Args:
            data (List[dict]): 要处理的数据
            *args: 额外的位置参数
            **kwargs: 额外的关键字参数
        
        Returns:
            List[dict]: 处理后的数据
        """
        processed = []
        for item in data:
            processed.append(self._process_item(item))
        return processed
    
    def _process_item(self, item: dict) -> dict:
        """处理单个数据项"""
        return {"processed": True, **item}

async def async_fetch_data(url: str, timeout: int = 30) -> dict:
    """异步获取数据"""
    import asyncio
    await asyncio.sleep(0.1)  # 模拟网络请求
    return {"url": url, "data": "example"}
'''
    
    print("分析包含类和异步函数的代码:")
    
    analyzer = PythonScriptAnalyzer()
    functions = analyzer.analyze_code(code)
    
    print(f"\n找到 {len(functions)} 个函数/方法:")
    
    for func in functions:
        print(f"\n函数: {func.name}")
        print(f"描述: {func.description or '无描述'}")
        print(f"参数: {[p.name for p in func.parameters]}")
        
        # 显示复杂参数类型
        for param in func.parameters:
            if param.parameter_type.value in ['var_positional', 'var_keyword']:
                print(f"  特殊参数: {param.name} ({param.parameter_type.value})")


def demo_code_generation():
    """演示代码生成功能"""
    print("\n" + "=" * 60)
    print("演示 4: 代码生成")
    print("=" * 60)
    
    code = '''
def send_email(to: str, subject: str, body: str, cc: List[str] = None) -> bool:
    """
    发送邮件
    
    Args:
        to (str): 收件人邮箱
        subject (str): 邮件主题
        body (str): 邮件内容
        cc (List[str]): 抄送列表
    
    Returns:
        bool: 发送是否成功
    """
    # 实际的邮件发送逻辑
    return True
'''
    
    print("从现有函数生成模板和测试代码:")
    
    func_info = analyze_python_function(code, "send_email")
    if func_info:
        generator = PythonCodeGenerator()
        
        print("\n生成的函数模板:")
        print("-" * 40)
        template = generator.generate_function_template(func_info)
        print(template)
        
        print("\n生成的测试代码:")
        print("-" * 40)
        test_code = generator.generate_test_code(func_info)
        print(test_code)


def demo_error_handling():
    """演示错误处理"""
    print("\n" + "=" * 60)
    print("演示 5: 错误处理")
    print("=" * 60)
    
    # 测试语法错误
    invalid_code = '''
def invalid_function(
    # 缺少参数定义和冒号
    return "This will cause a syntax error"
'''
    
    print("测试无效语法处理:")
    print(invalid_code)
    
    try:
        functions = analyze_python_code(invalid_code)
        print("意外：没有抛出异常")
    except ValueError as e:
        print(f"✓ 正确捕获语法错误: {e}")
    
    # 测试函数不存在
    valid_code = '''
def existing_function():
    pass
'''
    
    print("\n测试查找不存在的函数:")
    try:
        schema = python_to_schema(valid_code, "nonexistent_function")
        print("意外：没有抛出异常")
    except ValueError as e:
        print(f"✓ 正确处理函数不存在: {e}")


def main():
    """主函数"""
    print("Python 脚本分析器演示")
    print("这个演示展示了如何分析 Python 代码并生成 JSON Schema")
    
    try:
        demo_basic_analysis()
        demo_schema_generation()
        demo_complex_analysis()
        demo_code_generation()
        demo_error_handling()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\nPython 脚本分析器的主要功能:")
        print("✓ 解析函数定义和参数")
        print("✓ 提取类型注解和默认值")
        print("✓ 生成 JSON Schema")
        print("✓ 处理复杂类型（List, Dict, Optional 等）")
        print("✓ 解析文档字符串")
        print("✓ 分析类和方法")
        print("✓ 生成代码模板和测试")
        print("✓ 错误处理和验证")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()