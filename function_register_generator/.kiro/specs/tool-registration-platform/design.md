# 工具注册平台设计文档

## 概述

基于现有 Elasticsearch 数据库架构，构建一个现代化的工具注册平台。采用前后端分离架构，后端使用 FastAPI 提供 RESTful API，前端使用 Flutter Web 构建响应式界面。平台集成大语言模型进行智能补全，使用 Docker 容器提供安全沙盒环境。

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[Flutter Web UI] --> B[状态管理 Riverpod]
        B --> C[HTTP 客户端 Dio]
    end
    
    subgraph "API 网关层"
        D[FastAPI 服务器]
        D --> E[认证中间件]
        D --> F[限流中间件]
    end
    
    subgraph "业务逻辑层"
        G[工具注册服务]
        H[智能补全服务]
        I[沙盒验证服务]
        J[搜索服务]
        K[MCP 集成服务]
    end
    
    subgraph "数据层"
        L[Elasticsearch]
        M[Redis 缓存]
    end
    
    subgraph "外部服务"
        N[大语言模型 API]
        O[Docker 沙盒]
        P[MCP 服务器]
    end
    
    C --> D
    G --> L
    H --> N
    I --> O
    J --> L
    K --> P
    G --> M
    J --> M
```

### 技术栈选择

**前端：**
- Flutter Web (CanvasKit 渲染)
- Riverpod (状态管理)
- Dio (HTTP 客户端)
- Rive (动画效果)

**后端：**
- FastAPI (Python Web 框架)
- Pydantic (数据验证)
- Elasticsearch (数据存储)
- Redis (缓存)
- Docker (沙盒环境)

**AI 集成：**
- OpenAI GPT-4 (智能补全)
- 文本向量化 (语义搜索)

## 组件和接口

### 前端组件架构

```mermaid
graph TD
    A[App Shell] --> B[路由管理]
    B --> C[首页]
    B --> D[工具注册]
    B --> E[工具浏览]
    B --> F[搜索结果]
    
    D --> G[表单注册]
    D --> H[智能注册]
    D --> I[MCP 导入]
    D --> J[零代码向导]
    
    E --> K[工具卡片]
    E --> L[详情面板]
    E --> M[快速试用]
```

**核心组件：**

1. **ToolRegistrationWizard** - 分步注册向导
2. **SmartFormField** - 智能表单字段（支持 AI 补全）
3. **SandboxRunner** - 沙盒执行状态显示
4. **ToolCard** - 工具展示卡片
5. **SearchInterface** - 搜索和筛选界面
6. **MCPImporter** - MCP 服务器导入组件

### 后端 API 设计

**核心端点：**

```python
# 工具注册
POST /api/v1/tools/register
POST /api/v1/tools/register-from-curl
POST /api/v1/tools/register-from-python
POST /api/v1/tools/import-mcp

# 智能补全
POST /api/v1/tools/smart-complete
POST /api/v1/tools/generate-from-description

# 沙盒验证
POST /api/v1/sandbox/validate-http
POST /api/v1/sandbox/validate-python
POST /api/v1/sandbox/validate-mcp

# 搜索和发现
GET /api/v1/tools/search
GET /api/v1/tools/{tool_id}
GET /api/v1/tools/recommendations

# 管理
GET /api/v1/tools/my-tools
PUT /api/v1/tools/{tool_id}
DELETE /api/v1/tools/{tool_id}
```

## 数据模型

### Elasticsearch 文档结构

基于现有 schema，优化以下字段：

```json
{
  "toolId": "weather.openmeteo.forecast",
  "displayName": "天气预报查询",
  "aliases": ["weather", "forecast", "天气"],
  "version": "1.0.0",
  "visibility": "public",
  "category": "weather",
  "capabilities": ["http", "sync"],
  
  "owner": {
    "org": "company",
    "contact": "<EMAIL>",
    "userId": "user123"
  },
  
  "runtime": {
    "transport": "http",
    "endpoint": "https://api.open-meteo.com/v1/forecast",
    "httpMethod": "GET",
    "auth": {"type": "none"},
    "rate_limits": {"rpm": 100, "burst": 10}
  },
  
  "inputsDeveloperSchema": {
    "type": "object",
    "required": ["latitude", "longitude"],
    "properties": {
      "latitude": {"type": "number", "description": "纬度"},
      "longitude": {"type": "number", "description": "经度"},
      "hourly": {"type": "string", "description": "小时级数据"}
    }
  },
  
  "outputsSchema": {
    "type": "object",
    "properties": {
      "hourly": {"type": "object"},
      "hourly_units": {"type": "object"}
    }
  },
  
  "examples": [{
    "userQuery": "查询台北的天气",
    "parsedInputs": {
      "latitude": 25.0478,
      "longitude": 121.5319,
      "hourly": "temperature_2m"
    }
  }],
  
  "quickstarts": [{
    "kind": "curl",
    "title": "cURL 示例",
    "content": "curl 'https://api.open-meteo.com/v1/forecast?latitude=25.0478&longitude=121.5319&hourly=temperature_2m'"
  }],
  
  "content": "天气预报查询 weather forecast 获取指定位置的天气信息",
  "contentEmb": [0.1, 0.2, ...], // 1024维向量
  
  "createdTime": "2024-01-01T00:00:00Z",
  "updatedTime": "2024-01-01T00:00:00Z"
}
```

### 内存数据模型

**前端状态模型：**

```dart
class ToolModel {
  final String toolId;
  final String displayName;
  final String description;
  final ToolRuntime runtime;
  final JsonSchema inputSchema;
  final JsonSchema outputSchema;
  final List<ToolExample> examples;
  final List<QuickStart> quickstarts;
}

class RegistrationState {
  final RegistrationStep currentStep;
  final Map<String, dynamic> formData;
  final List<ValidationError> errors;
  final bool isLoading;
  final SandboxResult? sandboxResult;
}
```

## 错误处理

### 分层错误处理策略

1. **前端错误处理**
   - 网络错误：自动重试 + 用户友好提示
   - 表单验证：实时验证 + 错误高亮
   - 沙盒错误：详细错误信息 + 修复建议

2. **后端错误处理**
   - 输入验证：Pydantic 自动验证
   - 业务逻辑错误：自定义异常类
   - 外部服务错误：熔断器模式

3. **沙盒错误处理**
   - 超时处理：自动终止进程
   - 资源限制：内存/CPU 监控
   - 安全检查：恶意代码检测

### 错误响应格式

```json
{
  "error": {
    "code": "SANDBOX_TIMEOUT",
    "message": "沙盒执行超时",
    "details": {
      "timeout_seconds": 30,
      "suggestion": "请检查工具是否存在无限循环"
    }
  }
}
```

## 测试策略

### 测试金字塔

1. **单元测试 (70%)**
   - 前端：Widget 测试
   - 后端：函数和类测试
   - 覆盖率目标：85%

2. **集成测试 (20%)**
   - API 端到端测试
   - 数据库集成测试
   - 沙盒集成测试

3. **E2E 测试 (10%)**
   - 关键用户流程
   - 跨浏览器兼容性
   - 性能基准测试

### 测试环境

- **开发环境**：本地 Docker Compose
- **测试环境**：Kubernetes 集群
- **沙盒环境**：隔离的 Docker 容器

## 性能优化

### 前端优化

1. **渲染优化**
   - 使用 CanvasKit 渲染引擎
   - 虚拟滚动长列表
   - 图片懒加载

2. **状态管理优化**
   - Riverpod 细粒度更新
   - 缓存计算结果
   - 防抖搜索输入

3. **网络优化**
   - HTTP/2 多路复用
   - 响应压缩
   - 智能缓存策略

### 后端优化

1. **数据库优化**
   - Elasticsearch 索引优化
   - 查询缓存
   - 分页查询

2. **API 优化**
   - 异步处理
   - 连接池管理
   - 响应压缩

3. **缓存策略**
   - Redis 多层缓存
   - 搜索结果缓存
   - 工具元数据缓存

## 安全考虑

### 沙盒安全

1. **容器隔离**
   - 无网络访问（白名单除外）
   - 只读文件系统
   - 资源限制（CPU/内存/时间）

2. **代码安全**
   - 静态代码分析
   - 危险函数检测
   - 执行时间限制

### API 安全

1. **认证授权**
   - JWT Token 认证
   - 基于角色的权限控制
   - API 密钥管理

2. **输入验证**
   - Pydantic 数据验证
   - SQL 注入防护
   - XSS 防护

### 数据安全

1. **敏感信息保护**
   - API 密钥加密存储
   - 日志脱敏
   - 传输加密

2. **访问控制**
   - 工具可见性控制
   - 组织级别隔离
   - 审计日志

## 部署架构

### 容器化部署

```yaml
# docker-compose.yml
services:
  frontend:
    build: ./frontend
    ports: ["80:80"]
    
  backend:
    build: ./backend
    ports: ["8000:8000"]
    environment:
      - ES_HOST=elasticsearch:9200
      - REDIS_URL=redis:6379
    
  elasticsearch:
    image: elasticsearch:8.11
    environment:
      - discovery.type=single-node
    
  redis:
    image: redis:7-alpine
    
  sandbox:
    build: ./sandbox
    privileged: false
    read_only: true
```

### 监控和日志

1. **应用监控**
   - Prometheus + Grafana
   - 自定义业务指标
   - 告警规则

2. **日志管理**
   - 结构化日志
   - 集中式日志收集
   - 日志分析和搜索

3. **性能监控**
   - API 响应时间
   - 数据库查询性能
   - 沙盒执行统计