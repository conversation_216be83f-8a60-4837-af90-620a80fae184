name: tool_registration_platform
description: A Flutter Web application for tool registration and management platform.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # HTTP Client
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Routing
  go_router: ^12.1.3
  
  # UI Components
  flutter_animate: ^4.3.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  rive: ^0.12.4
  lottie: ^2.7.0
  animations: ^2.0.8
  
  # Forms and Validation
  reactive_forms: ^16.1.1
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.2.1
  shared_preferences: ^2.2.2
  
  # Development
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  
  # Linting
  flutter_lints: ^3.0.0
  
  # Testing
  mockito: ^5.4.4

flutter:
  uses-material-design: true