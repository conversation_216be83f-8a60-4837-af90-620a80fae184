import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:function_register_generator/features/tools/presentation/widgets/tool_card.dart';
import 'package:function_register_generator/core/theme/app_colors.dart';

void main() {
  group('ToolCard Widget Tests', () {
    late Map<String, dynamic> sampleTool;
    
    setUp(() {
      sampleTool = {
        'id': 'test-tool-1',
        'name': 'Test Weather API',
        'description': 'A test weather API tool for getting current weather information',
        'category': 'api',
        'transport': 'http',
        'status': 'active',
        'tags': ['weather', 'api', 'test'],
        'author': 'Test Author',
        'version': '1.0.0',
        'created_at': '2024-01-15T10:00:00Z',
        'updated_at': '2024-01-20T15:30:00Z',
        'usage_count': 1250,
        'rating': 4.8,
        'favorite_count': 89,
        'is_favorited': false,
      };
    });
    
    Widget createTestWidget({
      required Map<String, dynamic> tool,
      VoidCallback? onTap,
      VoidCallback? onFavorite,
      VoidCallback? onTryOut,
      bool isCompact = false,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: ToolCard(
              tool: tool,
              onTap: onTap,
              onFavorite: onFavorite,
              onTryOut: onTryOut,
              isCompact: isCompact,
            ),
          ),
        ),
      );
    }
    
    testWidgets('displays tool information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      
      // Check if tool name is displayed
      expect(find.text('Test Weather API'), findsOneWidget);
      
      // Check if description is displayed
      expect(find.textContaining('A test weather API tool'), findsOneWidget);
      
      // Check if category icon is displayed
      expect(find.byIcon(Icons.api), findsOneWidget);
      
      // Check if status badge is displayed
      expect(find.text('Active'), findsOneWidget);
      
      // Check if transport badge is displayed
      expect(find.text('HTTP'), findsOneWidget);
    });
    
    testWidgets('displays tags correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      
      // Check if tags are displayed (should show first 3)
      expect(find.text('weather'), findsOneWidget);
      expect(find.text('api'), findsOneWidget);
      expect(find.text('test'), findsOneWidget);
    });
    
    testWidgets('displays statistics correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      
      // Check if usage count is displayed
      expect(find.text('1250'), findsOneWidget);
      
      // Check if rating is displayed
      expect(find.text('4.8'), findsOneWidget);
    });
    
    testWidgets('handles favorite button tap', (WidgetTester tester) async {
      bool favoriteTapped = false;
      
      await tester.pumpWidget(createTestWidget(
        tool: sampleTool,
        onFavorite: () => favoriteTapped = true,
      ));
      
      // Find and tap the favorite button
      final favoriteButton = find.byIcon(Icons.favorite_border);
      expect(favoriteButton, findsOneWidget);
      
      await tester.tap(favoriteButton);
      await tester.pump();
      
      expect(favoriteTapped, isTrue);
    });
    
    testWidgets('shows favorited state correctly', (WidgetTester tester) async {
      final favoritedTool = Map<String, dynamic>.from(sampleTool);
      favoritedTool['is_favorited'] = true;
      
      await tester.pumpWidget(createTestWidget(tool: favoritedTool));
      
      // Should show filled favorite icon
      expect(find.byIcon(Icons.favorite), findsOneWidget);
      expect(find.byIcon(Icons.favorite_border), findsNothing);
    });
    
    testWidgets('handles try out button tap', (WidgetTester tester) async {
      bool tryOutTapped = false;
      
      await tester.pumpWidget(createTestWidget(
        tool: sampleTool,
        onTryOut: () => tryOutTapped = true,
      ));
      
      // Find and tap the try out button
      final tryOutButton = find.text('Try Out');
      expect(tryOutButton, findsOneWidget);
      
      await tester.tap(tryOutButton);
      await tester.pump();
      
      expect(tryOutTapped, isTrue);
    });
    
    testWidgets('handles card tap', (WidgetTester tester) async {
      bool cardTapped = false;
      
      await tester.pumpWidget(createTestWidget(
        tool: sampleTool,
        onTap: () => cardTapped = true,
      ));
      
      // Find and tap the view details button
      final viewDetailsButton = find.text('View Details');
      expect(viewDetailsButton, findsOneWidget);
      
      await tester.tap(viewDetailsButton);
      await tester.pump();
      
      expect(cardTapped, isTrue);
    });
    
    testWidgets('displays compact mode correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        tool: sampleTool,
        isCompact: true,
      ));
      
      // In compact mode, should still show name and description
      expect(find.text('Test Weather API'), findsOneWidget);
      expect(find.textContaining('A test weather API tool'), findsOneWidget);
      
      // But should not show detailed stats and action buttons
      expect(find.text('View Details'), findsNothing);
      expect(find.text('Try Out'), findsNothing);
    });
    
    testWidgets('displays different category icons correctly', (WidgetTester tester) async {
      // Test API category
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      expect(find.byIcon(Icons.api), findsOneWidget);
      
      // Test data category
      final dataTool = Map<String, dynamic>.from(sampleTool);
      dataTool['category'] = 'data';
      await tester.pumpWidget(createTestWidget(tool: dataTool));
      expect(find.byIcon(Icons.storage), findsOneWidget);
      
      // Test utility category
      final utilityTool = Map<String, dynamic>.from(sampleTool);
      utilityTool['category'] = 'utility';
      await tester.pumpWidget(createTestWidget(tool: utilityTool));
      expect(find.byIcon(Icons.build), findsOneWidget);
      
      // Test AI category
      final aiTool = Map<String, dynamic>.from(sampleTool);
      aiTool['category'] = 'ai';
      await tester.pumpWidget(createTestWidget(tool: aiTool));
      expect(find.byIcon(Icons.psychology), findsOneWidget);
    });
    
    testWidgets('displays different status badges correctly', (WidgetTester tester) async {
      // Test active status
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      expect(find.text('Active'), findsOneWidget);
      
      // Test deprecated status
      final deprecatedTool = Map<String, dynamic>.from(sampleTool);
      deprecatedTool['status'] = 'deprecated';
      await tester.pumpWidget(createTestWidget(tool: deprecatedTool));
      expect(find.text('Deprecated'), findsOneWidget);
      
      // Test private status
      final privateTool = Map<String, dynamic>.from(sampleTool);
      privateTool['status'] = 'private';
      await tester.pumpWidget(createTestWidget(tool: privateTool));
      expect(find.text('Private'), findsOneWidget);
    });
    
    testWidgets('handles missing or null data gracefully', (WidgetTester tester) async {
      final incompleteTool = {
        'id': 'incomplete-tool',
        'name': 'Incomplete Tool',
        // Missing description, tags, etc.
      };
      
      await tester.pumpWidget(createTestWidget(tool: incompleteTool));
      
      // Should still display the name
      expect(find.text('Incomplete Tool'), findsOneWidget);
      
      // Should show default values for missing data
      expect(find.text('No description available'), findsOneWidget);
      expect(find.text('0'), findsAtLeastNWidgets(1)); // Default usage count
    });
    
    testWidgets('formats date correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      
      // Should show relative time format
      expect(find.textContaining('ago'), findsAtLeastNWidgets(1));
    });
    
    testWidgets('applies correct colors for categories', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      
      // Find the category icon container
      final iconContainer = find.byType(Container).first;
      await tester.pump();
      
      // The container should have the correct category color
      // This is a simplified test - in practice, you'd need to check the actual color
      expect(iconContainer, findsOneWidget);
    });
    
    testWidgets('shows hover effects on desktop', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      
      // Find the MouseRegion widget
      final mouseRegion = find.byType(MouseRegion);
      expect(mouseRegion, findsOneWidget);
      
      // Simulate mouse enter
      await tester.pump();
      // Note: Testing actual hover effects would require more complex setup
    });
    
    testWidgets('handles animation controller lifecycle', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(tool: sampleTool));
      
      // Verify widget builds without errors
      expect(find.byType(ToolCard), findsOneWidget);
      
      // Dispose the widget
      await tester.pumpWidget(Container());
      
      // Should not throw any errors during disposal
    });
  });
  
  group('ToolCard Edge Cases', () {
    testWidgets('handles extremely long tool names', (WidgetTester tester) async {
      final longNameTool = {
        'id': 'long-name-tool',
        'name': 'This is an extremely long tool name that should be truncated properly to avoid layout issues',
        'description': 'Test description',
        'category': 'utility',
      };
      
      await tester.pumpWidget(ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: ToolCard(tool: longNameTool),
          ),
        ),
      ));
      
      // Should not cause overflow
      expect(tester.takeException(), isNull);
    });
    
    testWidgets('handles empty tags array', (WidgetTester tester) async {
      final noTagsTool = {
        'id': 'no-tags-tool',
        'name': 'No Tags Tool',
        'description': 'Tool without tags',
        'category': 'utility',
        'tags': <String>[],
      };
      
      await tester.pumpWidget(ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: ToolCard(tool: noTagsTool),
          ),
        ),
      ));
      
      // Should not show tags section
      expect(find.byType(Wrap), findsNothing);
    });
    
    testWidgets('handles null callback functions', (WidgetTester tester) async {
      final tool = {
        'id': 'test-tool',
        'name': 'Test Tool',
        'description': 'Test description',
        'category': 'utility',
      };
      
      await tester.pumpWidget(ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: ToolCard(
              tool: tool,
              onTap: null,
              onFavorite: null,
              onTryOut: null,
            ),
          ),
        ),
      ));
      
      // Should build without errors
      expect(find.byType(ToolCard), findsOneWidget);
      
      // Buttons should still be present but may be disabled
      expect(find.text('View Details'), findsOneWidget);
      expect(find.text('Try Out'), findsOneWidget);
    });
  });
}
