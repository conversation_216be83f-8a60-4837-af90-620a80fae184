import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../core/router/app_router.dart';
import 'wizard_steps/curl_input_step.dart';
import 'wizard_steps/python_input_step.dart';
import 'wizard_steps/mcp_input_step.dart';
import 'wizard_steps/natural_input_step.dart';
import 'wizard_steps/tool_details_step.dart';
import 'wizard_steps/preview_step.dart';
import 'wizard_steps/publish_step.dart';

class RegistrationWizard extends ConsumerStatefulWidget {
  final RegistrationType type;
  final VoidCallback onBack;
  final Function(Map<String, dynamic>) onComplete;
  
  const RegistrationWizard({
    super.key,
    required this.type,
    required this.onBack,
    required this.onComplete,
  });
  
  @override
  ConsumerState<RegistrationWizard> createState() => _RegistrationWizardState();
}

class _RegistrationWizardState extends ConsumerState<RegistrationWizard> {
  int currentStep = 0;
  Map<String, dynamic> toolData = {};
  
  late List<WizardStep> steps;
  
  @override
  void initState() {
    super.initState();
    _initializeSteps();
  }
  
  void _initializeSteps() {
    steps = [
      // Step 1: Input (varies by type)
      _getInputStep(),
      
      // Step 2: Tool Details
      WizardStep(
        title: 'Tool Details',
        description: 'Configure your tool\'s metadata and settings',
        content: ToolDetailsStep(
          initialData: toolData,
          onDataChanged: (data) {
            setState(() {
              toolData.addAll(data);
            });
          },
        ),
      ),
      
      // Step 3: Preview
      WizardStep(
        title: 'Preview',
        description: 'Review your tool configuration',
        content: PreviewStep(
          toolData: toolData,
          onDataChanged: (data) {
            setState(() {
              toolData.addAll(data);
            });
          },
        ),
      ),
      
      // Step 4: Publish
      WizardStep(
        title: 'Publish',
        description: 'Test and publish your tool',
        content: PublishStep(
          toolData: toolData,
          onComplete: widget.onComplete,
        ),
      ),
    ];
  }
  
  WizardStep _getInputStep() {
    switch (widget.type) {
      case RegistrationType.curl:
        return WizardStep(
          title: 'cURL Input',
          description: 'Paste your cURL command',
          content: CurlInputStep(
            onDataChanged: (data) {
              setState(() {
                toolData.addAll(data);
              });
            },
          ),
        );
        
      case RegistrationType.python:
        return WizardStep(
          title: 'Python Function',
          description: 'Define your Python function',
          content: PythonInputStep(
            onDataChanged: (data) {
              setState(() {
                toolData.addAll(data);
              });
            },
          ),
        );
        
      case RegistrationType.mcp:
        return WizardStep(
          title: 'MCP Configuration',
          description: 'Configure MCP server connection',
          content: McpInputStep(
            onDataChanged: (data) {
              setState(() {
                toolData.addAll(data);
              });
            },
          ),
        );
        
      case RegistrationType.natural:
        return WizardStep(
          title: 'Describe Your Tool',
          description: 'Tell us what your tool should do',
          content: NaturalInputStep(
            onDataChanged: (data) {
              setState(() {
                toolData.addAll(data);
              });
            },
          ),
        );
        
      default:
        return WizardStep(
          title: 'Input',
          description: 'Configure your tool',
          content: Container(),
        );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Progress indicator
          _buildProgressIndicator(),
          
          // Content
          Expanded(
            child: _buildStepContent(),
          ),
          
          // Navigation
          _buildNavigation(),
        ],
      ),
    );
  }
  
  Widget _buildHeader() {
    return Container(
      padding: context.responsivePadding,
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: AppColors.outline,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: widget.onBack,
            icon: const Icon(Icons.arrow_back),
          ),
          
          const SizedBox(width: 16),
          
          Icon(
            _getTypeIcon(widget.type),
            color: _getTypeColor(widget.type),
            size: 24,
          ),
          
          const SizedBox(width: 12),
          
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getTypeTitle(widget.type),
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                steps[currentStep].title,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Step indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              'Step ${currentStep + 1} of ${steps.length}',
              style: AppTextStyles.labelMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: List.generate(steps.length, (index) {
          final isActive = index == currentStep;
          final isCompleted = index < currentStep;
          
          return Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 4,
                    decoration: BoxDecoration(
                      color: isCompleted || isActive
                          ? AppColors.primary
                          : AppColors.outline,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                if (index < steps.length - 1)
                  const SizedBox(width: 8),
              ],
            ),
          );
        }),
      ),
    );
  }
  
  Widget _buildStepContent() {
    return Container(
      width: double.infinity,
      padding: context.responsivePadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step title and description
          Text(
            steps[currentStep].title,
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            steps[currentStep].description,
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Step content
          Expanded(
            child: steps[currentStep].content,
          ),
        ],
      ),
    );
  }
  
  Widget _buildNavigation() {
    return Container(
      padding: context.responsivePadding,
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.outline,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (currentStep > 0)
            OutlinedButton.icon(
              onPressed: _previousStep,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Previous'),
            ),
          
          const Spacer(),
          
          if (currentStep < steps.length - 1)
            ElevatedButton.icon(
              onPressed: _canProceed() ? _nextStep : null,
              icon: const Icon(Icons.arrow_forward),
              label: const Text('Next'),
            )
          else
            ElevatedButton.icon(
              onPressed: _canProceed() ? _finish : null,
              icon: const Icon(Icons.check),
              label: const Text('Finish'),
            ),
        ],
      ),
    );
  }
  
  bool _canProceed() {
    // TODO: Add validation logic for each step
    return true;
  }
  
  void _previousStep() {
    if (currentStep > 0) {
      setState(() {
        currentStep--;
      });
    }
  }
  
  void _nextStep() {
    if (currentStep < steps.length - 1) {
      setState(() {
        currentStep++;
      });
    }
  }
  
  void _finish() {
    widget.onComplete(toolData);
  }
  
  IconData _getTypeIcon(RegistrationType type) {
    switch (type) {
      case RegistrationType.curl:
        return Icons.code;
      case RegistrationType.python:
        return Icons.integration_instructions;
      case RegistrationType.mcp:
        return Icons.extension;
      case RegistrationType.natural:
        return Icons.chat;
      default:
        return Icons.build;
    }
  }
  
  Color _getTypeColor(RegistrationType type) {
    switch (type) {
      case RegistrationType.curl:
        return AppColors.categoryApi;
      case RegistrationType.python:
        return AppColors.transportPython;
      case RegistrationType.mcp:
        return AppColors.transportStdio;
      case RegistrationType.natural:
        return AppColors.secondary;
      default:
        return AppColors.primary;
    }
  }
  
  String _getTypeTitle(RegistrationType type) {
    switch (type) {
      case RegistrationType.curl:
        return 'From cURL Command';
      case RegistrationType.python:
        return 'Python Function';
      case RegistrationType.mcp:
        return 'MCP Tool';
      case RegistrationType.natural:
        return 'Natural Language';
      default:
        return 'Register Tool';
    }
  }
}

class WizardStep {
  final String title;
  final String description;
  final Widget content;
  
  const WizardStep({
    required this.title,
    required this.description,
    required this.content,
  });
}