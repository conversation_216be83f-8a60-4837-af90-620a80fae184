import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../core/router/app_router.dart';

class RegistrationTypeSelector extends StatelessWidget {
  final Function(RegistrationType) onTypeSelected;
  
  const RegistrationTypeSelector({
    super.key,
    required this.onTypeSelected,
  });
  
  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: context.isDesktop ? 2 : 1,
      crossAxisSpacing: context.responsiveSpacing,
      mainAxisSpacing: context.responsiveSpacing,
      childAspectRatio: context.isDesktop ? 2.5 : 3.5,
      children: [
        _RegistrationTypeCard(
          type: RegistrationType.curl,
          icon: Icons.code,
          title: 'From cURL Command',
          description: 'Convert existing cURL commands into reusable tools. Perfect for API endpoints you already know.',
          features: [
            'Automatic parameter extraction',
            'Header and authentication support',
            'Request/response validation',
          ],
          color: AppColors.categoryApi,
          onTap: () => onTypeSelected(RegistrationType.curl),
        ),
        
        _RegistrationTypeCard(
          type: RegistrationType.python,
          icon: Icons.integration_instructions,
          title: 'Python Function',
          description: 'Register Python functions as tools. Great for data processing and custom logic.',
          features: [
            'Automatic type inference',
            'Sandbox execution',
            'Dependency management',
          ],
          color: AppColors.transportPython,
          onTap: () => onTypeSelected(RegistrationType.python),
        ),
        
        _RegistrationTypeCard(
          type: RegistrationType.mcp,
          icon: Icons.extension,
          title: 'MCP Tool',
          description: 'Import tools from Model Context Protocol servers. Connect to existing MCP ecosystems.',
          features: [
            'Auto-discovery',
            'Schema validation',
            'Protocol compliance',
          ],
          color: AppColors.transportStdio,
          onTap: () => onTypeSelected(RegistrationType.mcp),
        ),
        
        _RegistrationTypeCard(
          type: RegistrationType.natural,
          icon: Icons.chat,
          title: 'Natural Language',
          description: 'Describe your tool in plain English. AI will help generate the implementation.',
          features: [
            'AI-powered generation',
            'Interactive refinement',
            'Multiple output formats',
          ],
          color: AppColors.secondary,
          onTap: () => onTypeSelected(RegistrationType.natural),
        ),
      ],
    );
  }
}

class _RegistrationTypeCard extends StatefulWidget {
  final RegistrationType type;
  final IconData icon;
  final String title;
  final String description;
  final List<String> features;
  final Color color;
  final VoidCallback onTap;
  
  const _RegistrationTypeCard({
    required this.type,
    required this.icon,
    required this.title,
    required this.description,
    required this.features,
    required this.color,
    required this.onTap,
  });
  
  @override
  State<_RegistrationTypeCard> createState() => _RegistrationTypeCardState();
}

class _RegistrationTypeCardState extends State<_RegistrationTypeCard>
    with SingleTickerProviderStateMixin {
  bool isHovered = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => isHovered = true);
        _animationController.forward();
      },
      onExit: (_) {
        setState(() => isHovered = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Card(
              elevation: isHovered ? 8 : 2,
              shadowColor: widget.color.withOpacity(0.3),
              child: InkWell(
                onTap: widget.onTap,
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: widget.color.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              widget.icon,
                              color: widget.color,
                              size: 28,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.title,
                                  style: AppTextStyles.titleLarge.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.description,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppColors.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Features
                      Text(
                        'Features:',
                        style: AppTextStyles.labelLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      ...widget.features.map((feature) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: widget.color,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                feature,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                      
                      const Spacer(),
                      
                      // Action button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: widget.onTap,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: widget.color,
                            foregroundColor: Colors.white,
                          ),
                          child: Text('Get Started'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}