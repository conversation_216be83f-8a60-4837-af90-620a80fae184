import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';

class ToolDetailsStep extends ConsumerStatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onDataChanged;
  
  const ToolDetailsStep({
    super.key,
    required this.initialData,
    required this.onDataChanged,
  });
  
  @override
  ConsumerState<ToolDetailsStep> createState() => _ToolDetailsStepState();
}

class _ToolDetailsStepState extends ConsumerState<ToolDetailsStep> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController();
  
  String _selectedCategory = 'utility';
  String _selectedVisibility = 'public';
  bool _requiresAuth = false;
  
  final List<String> _categories = [
    'utility',
    'data',
    'communication',
    'productivity',
    'development',
    'entertainment',
    'finance',
    'health',
    'education',
    'other',
  ];
  
  final List<String> _visibilityOptions = [
    'public',
    'private',
    'organization',
  ];
  
  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }
  
  void _initializeFromData() {
    // Initialize from parsed data if available
    if (widget.initialData.containsKey('parsed_data')) {
      final parsedData = widget.initialData['parsed_data'] as Map<String, dynamic>;
      
      if (parsedData.containsKey('function_name')) {
        _nameController.text = parsedData['function_name'];
      } else if (parsedData.containsKey('name')) {
        _nameController.text = parsedData['name'];
      }
      
      if (parsedData.containsKey('description')) {
        _descriptionController.text = parsedData['description'];
      }
    }
    
    // Initialize from generated tool if available
    if (widget.initialData.containsKey('generated_tool')) {
      final generatedTool = widget.initialData['generated_tool'] as Map<String, dynamic>;
      
      _nameController.text = generatedTool['name'] ?? '';
      _descriptionController.text = generatedTool['description'] ?? '';
      _selectedCategory = generatedTool['category'] ?? 'utility';
    }
    
    // Initialize from existing tool data
    _nameController.text = widget.initialData['name'] ?? _nameController.text;
    _descriptionController.text = widget.initialData['description'] ?? _descriptionController.text;
    _selectedCategory = widget.initialData['category'] ?? _selectedCategory;
    _selectedVisibility = widget.initialData['visibility'] ?? _selectedVisibility;
    _requiresAuth = widget.initialData['requires_auth'] ?? _requiresAuth;
    _tagsController.text = (widget.initialData['tags'] as List<String>?)?.join(', ') ?? '';
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic Information
          _buildSection(
            'Basic Information',
            [
              _buildTextField(
                'Tool Name',
                _nameController,
                'Enter a unique name for your tool',
                required: true,
              ),
              const SizedBox(height: 16),
              _buildTextField(
                'Description',
                _descriptionController,
                'Describe what your tool does and how to use it',
                maxLines: 3,
                required: true,
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Categorization
          _buildSection(
            'Categorization',
            [
              _buildDropdown(
                'Category',
                _selectedCategory,
                _categories,
                (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                  _notifyDataChanged();
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                'Tags',
                _tagsController,
                'Enter tags separated by commas (e.g., api, json, http)',
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Access Control
          _buildSection(
            'Access Control',
            [
              _buildDropdown(
                'Visibility',
                _selectedVisibility,
                _visibilityOptions,
                (value) {
                  setState(() {
                    _selectedVisibility = value!;
                  });
                  _notifyDataChanged();
                },
              ),
              const SizedBox(height: 16),
              _buildCheckbox(
                'Requires Authentication',
                _requiresAuth,
                'Check if this tool requires user authentication to use',
                (value) {
                  setState(() {
                    _requiresAuth = value!;
                  });
                  _notifyDataChanged();
                },
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Visibility explanation
          _buildVisibilityExplanation(),
        ],
      ),
    );
  }
  
  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }
  
  Widget _buildTextField(
    String label,
    TextEditingController controller,
    String hint, {
    int maxLines = 1,
    bool required = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (required) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            border: const OutlineInputBorder(),
          ),
          onChanged: (_) => _notifyDataChanged(),
        ),
      ],
    );
  }
  
  Widget _buildDropdown(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(_formatOptionLabel(option)),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }
  
  Widget _buildCheckbox(
    String label,
    bool value,
    String description,
    ValueChanged<bool?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          value: value,
          onChanged: onChanged,
          title: Text(
            'Yes, require authentication',
            style: AppTextStyles.bodyMedium,
          ),
          subtitle: Text(
            description,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }
  
  Widget _buildVisibilityExplanation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Visibility Options',
                style: AppTextStyles.labelLarge.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildVisibilityOption(
            'Public',
            'Anyone can discover and use this tool',
            Icons.public,
          ),
          const SizedBox(height: 8),
          _buildVisibilityOption(
            'Private',
            'Only you can see and use this tool',
            Icons.lock,
          ),
          const SizedBox(height: 8),
          _buildVisibilityOption(
            'Organization',
            'Only members of your organization can use this tool',
            Icons.business,
          ),
        ],
      ),
    );
  }
  
  Widget _buildVisibilityOption(String title, String description, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  String _formatOptionLabel(String option) {
    return option.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1)
    ).join(' ');
  }
  
  void _notifyDataChanged() {
    final tags = _tagsController.text
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
    
    widget.onDataChanged({
      'name': _nameController.text.trim(),
      'description': _descriptionController.text.trim(),
      'category': _selectedCategory,
      'visibility': _selectedVisibility,
      'requires_auth': _requiresAuth,
      'tags': tags,
    });
  }
}
