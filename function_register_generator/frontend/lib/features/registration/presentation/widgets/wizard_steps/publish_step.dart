import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../shared/presentation/widgets/loading_overlay.dart';
import '../../../../../shared/presentation/animations/success_animations.dart';
import '../../../../../shared/presentation/animations/loading_animations.dart';

enum PublishState {
  ready,
  validating,
  testing,
  publishing,
  success,
  error,
}

class PublishStep extends ConsumerStatefulWidget {
  final Map<String, dynamic> toolData;
  final Function(Map<String, dynamic>) onComplete;
  
  const PublishStep({
    super.key,
    required this.toolData,
    required this.onComplete,
  });
  
  @override
  ConsumerState<PublishStep> createState() => _PublishStepState();
}

class _PublishStepState extends ConsumerState<PublishStep> {
  PublishState _state = PublishState.ready;
  String? _errorMessage;
  String? _toolId;
  List<Map<String, dynamic>> _validationResults = [];
  List<Map<String, dynamic>> _testResults = [];
  
  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isLoading(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status header
          _buildStatusHeader(),
          
          const SizedBox(height: 24),
          
          // Content based on state
          Expanded(
            child: _buildStateContent(),
          ),
          
          // Actions
          _buildActions(),
        ],
      ),
    );
  }
  
  Widget _buildStatusHeader() {
    IconData icon;
    Color color;
    String title;
    String subtitle;
    
    switch (_state) {
      case PublishState.ready:
        icon = Icons.rocket_launch;
        color = AppColors.primary;
        title = 'Ready to Publish';
        subtitle = 'Your tool is ready for validation and publishing';
        break;
      case PublishState.validating:
        icon = Icons.verified;
        color = AppColors.warning;
        title = 'Validating Tool';
        subtitle = 'Checking tool configuration and schema';
        break;
      case PublishState.testing:
        icon = Icons.play_circle;
        color = AppColors.warning;
        title = 'Testing Tool';
        subtitle = 'Running sandbox tests to ensure functionality';
        break;
      case PublishState.publishing:
        icon = Icons.cloud_upload;
        color = AppColors.warning;
        title = 'Publishing Tool';
        subtitle = 'Registering your tool in the platform';
        break;
      case PublishState.success:
        icon = Icons.check_circle;
        color = AppColors.success;
        title = 'Tool Published Successfully!';
        subtitle = 'Your tool is now available for use';
        break;
      case PublishState.error:
        icon = Icons.error;
        color = AppColors.error;
        title = 'Publication Failed';
        subtitle = _errorMessage ?? 'An error occurred during publication';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStateContent() {
    switch (_state) {
      case PublishState.ready:
        return _buildReadyContent();
      case PublishState.validating:
      case PublishState.testing:
      case PublishState.publishing:
        return _buildProgressContent();
      case PublishState.success:
        return _buildSuccessContent();
      case PublishState.error:
        return _buildErrorContent();
    }
  }
  
  Widget _buildReadyContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Pre-publish checklist
          _buildChecklist(),
          
          const SizedBox(height: 24),
          
          // Tool summary
          _buildToolSummary(),
        ],
      ),
    );
  }
  
  Widget _buildProgressContent() {
    return Column(
      children: [
        // Progress steps
        _buildProgressSteps(),

        const SizedBox(height: 24),

        // Loading animation
        AnimatedLoadingState(
          type: LoadingAnimationType.pulsingDots,
          message: _getLoadingMessage(),
          color: AppColors.primary,
        ),

        const SizedBox(height: 24),

        // Current results
        Expanded(
          child: _buildCurrentResults(),
        ),
      ],
    );
  }

  String _getLoadingMessage() {
    switch (_state) {
      case PublishState.validating:
        return 'Validating tool configuration...';
      case PublishState.testing:
        return 'Testing tool functionality...';
      case PublishState.publishing:
        return 'Publishing tool to registry...';
      default:
        return 'Processing...';
    }
  }
  
  Widget _buildSuccessContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SuccessAnimations.celebration(
            title: 'Congratulations!',
            subtitle: 'Your tool has been successfully published',
            primaryColor: AppColors.success,
          ),
          const SizedBox(height: 32),
          if (_toolId != null) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.outline),
              ),
              child: Column(
                children: [
                  Text(
                    'Tool ID',
                    style: AppTextStyles.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SelectableText(
                    _toolId!,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OutlinedButton.icon(
                onPressed: () {
                  // TODO: Navigate to tool details
                },
                icon: const Icon(Icons.visibility),
                label: const Text('View Tool'),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Navigate to tools list
                },
                icon: const Icon(Icons.list),
                label: const Text('Browse Tools'),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildErrorContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: AppColors.error,
          ),
          const SizedBox(height: 24),
          Text(
            'Publication Failed',
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'An unexpected error occurred',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _retryPublication,
            icon: const Icon(Icons.refresh),
            label: const Text('Try Again'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildChecklist() {
    final items = [
      'Tool name and description are provided',
      'Input schema is valid',
      'Category and visibility are set',
      'All required fields are completed',
    ];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pre-publication Checklist',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item,
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }
  
  Widget _buildToolSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tool Summary',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildSummaryRow('Name', widget.toolData['name'] ?? 'Unnamed Tool'),
          _buildSummaryRow('Category', widget.toolData['category'] ?? 'utility'),
          _buildSummaryRow('Visibility', widget.toolData['visibility'] ?? 'public'),
          _buildSummaryRow('Source', widget.toolData['source_type'] ?? 'manual'),
        ],
      ),
    );
  }
  
  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProgressSteps() {
    final steps = ['Validate', 'Test', 'Publish'];
    final currentStepIndex = _state == PublishState.validating ? 0 
        : _state == PublishState.testing ? 1 
        : 2;
    
    return Row(
      children: List.generate(steps.length, (index) {
        final isActive = index == currentStepIndex;
        final isCompleted = index < currentStepIndex;
        
        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: isCompleted || isActive
                        ? AppColors.primary
                        : AppColors.outline,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              if (index < steps.length - 1)
                const SizedBox(width: 8),
            ],
          ),
        );
      }),
    );
  }
  
  Widget _buildCurrentResults() {
    if (_validationResults.isNotEmpty || _testResults.isNotEmpty) {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_validationResults.isNotEmpty) ...[
              Text(
                'Validation Results',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ..._validationResults.map((result) => _buildResultItem(result)),
              const SizedBox(height: 16),
            ],
            if (_testResults.isNotEmpty) ...[
              Text(
                'Test Results',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ..._testResults.map((result) => _buildResultItem(result)),
            ],
          ],
        ),
      );
    }
    
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
  
  Widget _buildResultItem(Map<String, dynamic> result) {
    final isSuccess = result['success'] == true;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSuccess 
            ? AppColors.success.withOpacity(0.05)
            : AppColors.error.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSuccess 
              ? AppColors.success.withOpacity(0.2)
              : AppColors.error.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            color: isSuccess ? AppColors.success : AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              result['message'] ?? 'No message',
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(color: AppColors.outline),
        ),
      ),
      child: Row(
        children: [
          if (_state == PublishState.success) ...[
            const Spacer(),
            ElevatedButton.icon(
              onPressed: () => widget.onComplete(widget.toolData),
              icon: const Icon(Icons.check),
              label: const Text('Done'),
            ),
          ] else if (_state == PublishState.error) ...[
            OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _state = PublishState.ready;
                  _errorMessage = null;
                });
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back'),
            ),
            const Spacer(),
            ElevatedButton.icon(
              onPressed: _retryPublication,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ] else if (_state == PublishState.ready) ...[
            const Spacer(),
            ElevatedButton.icon(
              onPressed: _startPublication,
              icon: const Icon(Icons.rocket_launch),
              label: const Text('Publish Tool'),
            ),
          ],
        ],
      ),
    );
  }
  
  bool _isLoading() {
    return _state == PublishState.validating ||
           _state == PublishState.testing ||
           _state == PublishState.publishing;
  }
  
  Future<void> _startPublication() async {
    try {
      // Step 1: Validation
      setState(() {
        _state = PublishState.validating;
        _validationResults = [];
        _testResults = [];
      });
      
      await Future.delayed(const Duration(seconds: 2));
      
      setState(() {
        _validationResults = [
          {'success': true, 'message': 'Tool name is valid and unique'},
          {'success': true, 'message': 'Input schema is well-formed'},
          {'success': true, 'message': 'All required fields are present'},
        ];
      });
      
      // Step 2: Testing
      setState(() {
        _state = PublishState.testing;
      });
      
      await Future.delayed(const Duration(seconds: 3));
      
      setState(() {
        _testResults = [
          {'success': true, 'message': 'Sandbox environment initialized'},
          {'success': true, 'message': 'Tool execution test passed'},
          {'success': true, 'message': 'Security validation passed'},
        ];
      });
      
      // Step 3: Publishing
      setState(() {
        _state = PublishState.publishing;
      });
      
      await Future.delayed(const Duration(seconds: 2));
      
      setState(() {
        _state = PublishState.success;
        _toolId = 'tool_${DateTime.now().millisecondsSinceEpoch}';
      });
      
    } catch (e) {
      setState(() {
        _state = PublishState.error;
        _errorMessage = e.toString();
      });
    }
  }
  
  void _retryPublication() {
    setState(() {
      _state = PublishState.ready;
      _errorMessage = null;
      _validationResults = [];
      _testResults = [];
    });
  }
}
