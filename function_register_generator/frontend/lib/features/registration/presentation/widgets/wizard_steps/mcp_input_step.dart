import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../shared/presentation/widgets/loading_overlay.dart';

class McpInputStep extends ConsumerStatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  
  const McpInputStep({
    super.key,
    required this.onDataChanged,
  });
  
  @override
  ConsumerState<McpInputStep> createState() => _McpInputStepState();
}

class _McpInputStepState extends ConsumerState<McpInputStep> {
  final TextEditingController _serverUrlController = TextEditingController();
  final TextEditingController _commandController = TextEditingController();
  String _transportType = 'stdio';
  bool _isConnecting = false;
  List<Map<String, dynamic>>? _availableTools;
  Set<String> _selectedTools = {};
  String? _error;
  
  @override
  void dispose() {
    _serverUrlController.dispose();
    _commandController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isConnecting,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.transportStdio.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.transportStdio.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.transportStdio,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Connect to MCP Server',
                        style: AppTextStyles.labelLarge.copyWith(
                          color: AppColors.transportStdio,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Configure connection to your MCP server and select tools to import.',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Transport type selection
          Text(
            'Transport Type',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('stdio'),
                  subtitle: const Text('Command line process'),
                  value: 'stdio',
                  groupValue: _transportType,
                  onChanged: (value) {
                    setState(() {
                      _transportType = value!;
                      _availableTools = null;
                      _selectedTools.clear();
                      _error = null;
                    });
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('http'),
                  subtitle: const Text('HTTP server'),
                  value: 'http',
                  groupValue: _transportType,
                  onChanged: (value) {
                    setState(() {
                      _transportType = value!;
                      _availableTools = null;
                      _selectedTools.clear();
                      _error = null;
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Connection configuration
          if (_transportType == 'stdio') ...[
            Text(
              'Command',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _commandController,
              decoration: const InputDecoration(
                hintText: 'python -m my_mcp_server',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _onConfigChanged(),
            ),
          ] else ...[
            Text(
              'Server URL',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _serverUrlController,
              decoration: const InputDecoration(
                hintText: 'http://localhost:8000',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _onConfigChanged(),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // Connect button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _canConnect() && !_isConnecting
                  ? _connectToServer
                  : null,
              icon: const Icon(Icons.link),
              label: const Text('Connect and Discover Tools'),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Results
          if (_error != null) ...[
            _buildErrorSection(),
            const SizedBox(height: 16),
          ],
          
          if (_availableTools != null) ...[
            Expanded(
              child: _buildToolsSection(),
            ),
          ],
        ],
      ),
    );
  }
  
  bool _canConnect() {
    if (_transportType == 'stdio') {
      return _commandController.text.trim().isNotEmpty;
    } else {
      return _serverUrlController.text.trim().isNotEmpty;
    }
  }
  
  void _onConfigChanged() {
    setState(() {
      _availableTools = null;
      _selectedTools.clear();
      _error = null;
    });
  }
  
  Future<void> _connectToServer() async {
    setState(() {
      _isConnecting = true;
      _error = null;
      _availableTools = null;
      _selectedTools.clear();
    });
    
    try {
      // TODO: Call backend API to connect to MCP server
      await Future.delayed(const Duration(seconds: 3)); // Simulate API call
      
      // Mock available tools for now
      final mockTools = [
        {
          'name': 'file_reader',
          'description': 'Read contents of a file',
          'inputSchema': {
            'type': 'object',
            'properties': {
              'path': {
                'type': 'string',
                'description': 'Path to the file to read',
              },
            },
            'required': ['path'],
          },
        },
        {
          'name': 'web_search',
          'description': 'Search the web for information',
          'inputSchema': {
            'type': 'object',
            'properties': {
              'query': {
                'type': 'string',
                'description': 'Search query',
              },
              'limit': {
                'type': 'integer',
                'description': 'Maximum number of results',
                'default': 10,
              },
            },
            'required': ['query'],
          },
        },
        {
          'name': 'calculator',
          'description': 'Perform mathematical calculations',
          'inputSchema': {
            'type': 'object',
            'properties': {
              'expression': {
                'type': 'string',
                'description': 'Mathematical expression to evaluate',
              },
            },
            'required': ['expression'],
          },
        },
      ];
      
      setState(() {
        _availableTools = mockTools;
        _isConnecting = false;
      });
      
    } catch (e) {
      setState(() {
        _error = 'Failed to connect to MCP server: ${e.toString()}';
        _isConnecting = false;
      });
    }
  }
  
  void _onToolSelectionChanged() {
    final selectedToolsData = _availableTools!
        .where((tool) => _selectedTools.contains(tool['name']))
        .toList();
    
    widget.onDataChanged({
      'source_type': 'mcp',
      'transport_type': _transportType,
      'server_config': _transportType == 'stdio' 
          ? {'command': _commandController.text.trim()}
          : {'url': _serverUrlController.text.trim()},
      'selected_tools': selectedToolsData,
    });
  }
  
  Widget _buildErrorSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.error.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildToolsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.check_circle_outline,
              color: AppColors.success,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              'Found ${_availableTools!.length} tools',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                setState(() {
                  if (_selectedTools.length == _availableTools!.length) {
                    _selectedTools.clear();
                  } else {
                    _selectedTools = _availableTools!
                        .map((tool) => tool['name'] as String)
                        .toSet();
                  }
                });
                _onToolSelectionChanged();
              },
              child: Text(
                _selectedTools.length == _availableTools!.length
                    ? 'Deselect All'
                    : 'Select All',
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Expanded(
          child: ListView.builder(
            itemCount: _availableTools!.length,
            itemBuilder: (context, index) {
              final tool = _availableTools![index];
              final isSelected = _selectedTools.contains(tool['name']);
              
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: CheckboxListTile(
                  value: isSelected,
                  onChanged: (selected) {
                    setState(() {
                      if (selected == true) {
                        _selectedTools.add(tool['name']);
                      } else {
                        _selectedTools.remove(tool['name']);
                      }
                    });
                    _onToolSelectionChanged();
                  },
                  title: Text(
                    tool['name'],
                    style: AppTextStyles.titleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(
                        tool['description'],
                        style: AppTextStyles.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Parameters: ${(tool['inputSchema']['properties'] as Map).keys.join(', ')}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  isThreeLine: true,
                ),
              );
            },
          ),
        ),
        
        if (_selectedTools.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${_selectedTools.length} tools selected for import',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
