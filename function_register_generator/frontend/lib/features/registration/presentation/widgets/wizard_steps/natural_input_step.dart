import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../shared/presentation/widgets/loading_overlay.dart';

class NaturalInputStep extends ConsumerStatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  
  const NaturalInputStep({
    super.key,
    required this.onDataChanged,
  });
  
  @override
  ConsumerState<NaturalInputStep> createState() => _NaturalInputStepState();
}

class _NaturalInputStepState extends ConsumerState<NaturalInputStep> {
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _exampleController = TextEditingController();
  bool _isGenerating = false;
  Map<String, dynamic>? _generatedTool;
  String? _error;
  
  @override
  void dispose() {
    _descriptionController.dispose();
    _exampleController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isGenerating,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.secondary.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.secondary.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: AppColors.secondary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Describe your tool in natural language',
                        style: AppTextStyles.labelLarge.copyWith(
                          color: AppColors.secondary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Our AI will generate a complete tool definition based on your description.',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Description input
          Text(
            'Tool Description',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          TextField(
            controller: _descriptionController,
            maxLines: 4,
            decoration: const InputDecoration(
              hintText: 'Describe what your tool should do. For example:\n\n"A tool that converts temperatures between Celsius and Fahrenheit. It should accept a temperature value and the source unit, then return the converted temperature with the target unit."',
              border: OutlineInputBorder(),
            ),
            onChanged: _onInputChanged,
          ),
          
          const SizedBox(height: 24),
          
          // Example input/output
          Text(
            'Example Usage (Optional)',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          TextField(
            controller: _exampleController,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: 'Provide an example of how the tool should work:\n\nInput: temperature=32, from_unit="fahrenheit"\nOutput: {"celsius": 0, "message": "32°F equals 0°C"}',
              border: OutlineInputBorder(),
            ),
            onChanged: _onInputChanged,
          ),
          
          const SizedBox(height: 16),
          
          // Generate button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _descriptionController.text.trim().isNotEmpty && !_isGenerating
                  ? _generateTool
                  : null,
              icon: const Icon(Icons.auto_awesome),
              label: const Text('Generate Tool'),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Results
          if (_error != null) ...[
            _buildErrorSection(),
            const SizedBox(height: 16),
          ],
          
          if (_generatedTool != null) ...[
            Expanded(
              child: _buildGeneratedToolSection(),
            ),
          ],
        ],
      ),
    );
  }
  
  void _onInputChanged(String value) {
    setState(() {
      _error = null;
      _generatedTool = null;
    });
  }
  
  Future<void> _generateTool() async {
    setState(() {
      _isGenerating = true;
      _error = null;
      _generatedTool = null;
    });
    
    try {
      // TODO: Call backend API to generate tool from description
      await Future.delayed(const Duration(seconds: 3)); // Simulate API call
      
      // Mock generated tool for now
      final mockTool = {
        'name': 'temperature_converter',
        'description': 'Converts temperatures between Celsius and Fahrenheit',
        'category': 'utility',
        'inputSchema': {
          'type': 'object',
          'properties': {
            'temperature': {
              'type': 'number',
              'description': 'The temperature value to convert',
            },
            'from_unit': {
              'type': 'string',
              'enum': ['celsius', 'fahrenheit'],
              'description': 'The source temperature unit',
            },
            'to_unit': {
              'type': 'string',
              'enum': ['celsius', 'fahrenheit'],
              'description': 'The target temperature unit',
            },
          },
          'required': ['temperature', 'from_unit', 'to_unit'],
        },
        'implementation': {
          'type': 'generated',
          'code': '''
def temperature_converter(temperature: float, from_unit: str, to_unit: str) -> dict:
    """Convert temperature between Celsius and Fahrenheit"""
    if from_unit == to_unit:
        return {"result": temperature, "unit": to_unit}
    
    if from_unit == "celsius" and to_unit == "fahrenheit":
        result = (temperature * 9/5) + 32
    elif from_unit == "fahrenheit" and to_unit == "celsius":
        result = (temperature - 32) * 5/9
    else:
        raise ValueError("Invalid unit combination")
    
    return {
        "result": round(result, 2),
        "unit": to_unit,
        "message": f"{temperature}°{from_unit.title()} equals {round(result, 2)}°{to_unit.title()}"
    }
''',
        },
        'test_cases': [
          {
            'input': {'temperature': 32, 'from_unit': 'fahrenheit', 'to_unit': 'celsius'},
            'expected_output': {'result': 0, 'unit': 'celsius', 'message': '32°Fahrenheit equals 0°Celsius'},
          },
          {
            'input': {'temperature': 0, 'from_unit': 'celsius', 'to_unit': 'fahrenheit'},
            'expected_output': {'result': 32, 'unit': 'fahrenheit', 'message': '0°Celsius equals 32°Fahrenheit'},
          },
        ],
      };
      
      setState(() {
        _generatedTool = mockTool;
        _isGenerating = false;
      });
      
      widget.onDataChanged({
        'source_type': 'natural',
        'description': _descriptionController.text.trim(),
        'example': _exampleController.text.trim(),
        'generated_tool': mockTool,
      });
      
    } catch (e) {
      setState(() {
        _error = 'Failed to generate tool: ${e.toString()}';
        _isGenerating = false;
      });
    }
  }
  
  Widget _buildErrorSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.error.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildGeneratedToolSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.success.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: AppColors.success,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Tool Generated Successfully',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow('Name', _generatedTool!['name']),
                  _buildInfoRow('Description', _generatedTool!['description']),
                  _buildInfoRow('Category', _generatedTool!['category']),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'Parameters:',
                    style: AppTextStyles.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  ...(_generatedTool!['inputSchema']['properties'] as Map).entries.map((entry) {
                    final param = entry.value as Map<String, dynamic>;
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                entry.key,
                                style: AppTextStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  param['type'],
                                  style: AppTextStyles.labelSmall.copyWith(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (param['description'] != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              param['description'],
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.onSurfaceVariant,
                              ),
                            ),
                          ],
                          if (param['enum'] != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              'Options: ${(param['enum'] as List).join(', ')}',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.onSurfaceVariant,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  }).toList(),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'Test Cases:',
                    style: AppTextStyles.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  ...(_generatedTool!['test_cases'] as List).asMap().entries.map((entry) {
                    final index = entry.key;
                    final testCase = entry.value as Map<String, dynamic>;
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Test Case ${index + 1}',
                            style: AppTextStyles.labelMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Input: ${testCase['input']}',
                            style: AppTextStyles.bodySmall.copyWith(
                              fontFamily: 'monospace',
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Expected: ${testCase['expected_output']}',
                            style: AppTextStyles.bodySmall.copyWith(
                              fontFamily: 'monospace',
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
