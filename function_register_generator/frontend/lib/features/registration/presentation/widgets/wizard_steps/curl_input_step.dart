import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../shared/presentation/widgets/code_editor.dart';
import '../../../../../shared/presentation/widgets/loading_overlay.dart';

class CurlInputStep extends ConsumerStatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;

  const CurlInputStep({
    super.key,
    required this.onDataChanged,
  });

  @override
  ConsumerState<CurlInputStep> createState() => _CurlInputStepState();
}

class _CurlInputStepState extends ConsumerState<CurlInputStep> {
  final TextEditingController _curlController = TextEditingController();
  bool _isAnalyzing = false;
  Map<String, dynamic>? _parsedData;
  String? _error;

  @override
  void dispose() {
    _curlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isAnalyzing,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Paste your cURL command',
                        style: AppTextStyles.labelLarge.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'We\'ll automatically extract the endpoint, parameters, and generate the tool schema for you.',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // cURL input
          Text(
            'cURL Command',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 8),

          Expanded(
            flex: 2,
            child: CodeEditor(
              controller: _curlController,
              language: 'bash',
              placeholder: 'curl -X POST "https://api.example.com/endpoint" \\\n  -H "Content-Type: application/json" \\\n  -d \'{"key": "value"}\'',
              onChanged: _onCurlChanged,
            ),
          ),

          const SizedBox(height: 16),

          // Analysis button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _curlController.text.trim().isNotEmpty && !_isAnalyzing
                  ? _analyzeCurl
                  : null,
              icon: const Icon(Icons.analytics),
              label: const Text('Analyze cURL Command'),
            ),
          ),

          const SizedBox(height: 24),

          // Results
          if (_error != null) ...[
            _buildErrorSection(),
            const SizedBox(height: 16),
          ],

          if (_parsedData != null) ...[
            Expanded(
              flex: 1,
              child: _buildResultsSection(),
            ),
          ],
        ],
      ),
    );
  }

  void _onCurlChanged(String value) {
    setState(() {
      _error = null;
      _parsedData = null;
    });
  }

  Future<void> _analyzeCurl() async {
    setState(() {
      _isAnalyzing = true;
      _error = null;
      _parsedData = null;
    });

    try {
      // TODO: Call backend API to parse cURL
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      // Mock parsed data for now
      final mockData = {
        'method': 'POST',
        'url': 'https://api.example.com/endpoint',
        'headers': {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token',
        },
        'body': {
          'key': 'value',
          'number': 123,
        },
        'inputSchema': {
          'type': 'object',
          'properties': {
            'key': {
              'type': 'string',
              'description': 'A string parameter',
            },
            'number': {
              'type': 'integer',
              'description': 'A numeric parameter',
            },
          },
          'required': ['key'],
        },
      };

      setState(() {
        _parsedData = mockData;
        _isAnalyzing = false;
      });

      widget.onDataChanged({
        'source_type': 'curl',
        'curl_command': _curlController.text.trim(),
        'parsed_data': mockData,
      });

    } catch (e) {
      setState(() {
        _error = 'Failed to parse cURL command: ${e.toString()}';
        _isAnalyzing = false;
      });
    }
  }

  Widget _buildErrorSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.error.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.success.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: AppColors.success,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Analysis Complete',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildResultItem('Method', _parsedData!['method']),
                  _buildResultItem('URL', _parsedData!['url']),
                  _buildResultItem('Headers', _parsedData!['headers'].keys.join(', ')),
                  if (_parsedData!['body'] != null)
                    _buildResultItem('Body Parameters', _parsedData!['body'].keys.join(', ')),
                  _buildResultItem('Input Schema', '${_parsedData!['inputSchema']['properties'].length} parameters detected'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}