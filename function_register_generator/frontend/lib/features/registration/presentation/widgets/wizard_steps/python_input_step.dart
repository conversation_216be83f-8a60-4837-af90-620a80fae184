import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../shared/presentation/widgets/code_editor.dart';
import '../../../../../shared/presentation/widgets/loading_overlay.dart';

class PythonInputStep extends ConsumerStatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  
  const PythonInputStep({
    super.key,
    required this.onDataChanged,
  });
  
  @override
  ConsumerState<PythonInputStep> createState() => _PythonInputStepState();
}

class _PythonInputStepState extends ConsumerState<PythonInputStep> {
  final TextEditingController _codeController = TextEditingController();
  bool _isAnalyzing = false;
  Map<String, dynamic>? _parsedData;
  String? _error;
  
  @override
  void initState() {
    super.initState();
    _codeController.text = '''def my_function(param1: str, param2: int = 10) -> dict:
    """
    Description of what this function does.
    
    Args:
        param1: Description of param1
        param2: Description of param2 (default: 10)
        
    Returns:
        A dictionary with the result
    """
    # Your implementation here
    return {"result": f"Hello {param1}, number is {param2}"}''';
  }
  
  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isAnalyzing,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.transportPython.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.transportPython.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.transportPython,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Define your Python function',
                        style: AppTextStyles.labelLarge.copyWith(
                          color: AppColors.transportPython,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Include type hints and docstrings for better schema generation.',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Python code input
          Text(
            'Python Function',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Expanded(
            flex: 2,
            child: CodeEditor(
              controller: _codeController,
              language: 'python',
              placeholder: 'def my_function(param: str) -> dict:\n    """Function description"""\n    return {"result": param}',
              onChanged: _onCodeChanged,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Analysis button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _codeController.text.trim().isNotEmpty && !_isAnalyzing
                  ? _analyzeCode
                  : null,
              icon: const Icon(Icons.analytics),
              label: const Text('Analyze Python Function'),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Results
          if (_error != null) ...[
            _buildErrorSection(),
            const SizedBox(height: 16),
          ],
          
          if (_parsedData != null) ...[
            Expanded(
              flex: 1,
              child: _buildResultsSection(),
            ),
          ],
        ],
      ),
    );
  }
  
  void _onCodeChanged(String value) {
    setState(() {
      _error = null;
      _parsedData = null;
    });
  }
  
  Future<void> _analyzeCode() async {
    setState(() {
      _isAnalyzing = true;
      _error = null;
      _parsedData = null;
    });
    
    try {
      // TODO: Call backend API to analyze Python code
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // Mock parsed data for now
      final mockData = {
        'function_name': 'my_function',
        'description': 'Description of what this function does.',
        'parameters': [
          {
            'name': 'param1',
            'type': 'string',
            'required': true,
            'description': 'Description of param1',
          },
          {
            'name': 'param2',
            'type': 'integer',
            'required': false,
            'default': 10,
            'description': 'Description of param2 (default: 10)',
          },
        ],
        'return_type': 'object',
        'inputSchema': {
          'type': 'object',
          'properties': {
            'param1': {
              'type': 'string',
              'description': 'Description of param1',
            },
            'param2': {
              'type': 'integer',
              'description': 'Description of param2 (default: 10)',
              'default': 10,
            },
          },
          'required': ['param1'],
        },
      };
      
      setState(() {
        _parsedData = mockData;
        _isAnalyzing = false;
      });
      
      widget.onDataChanged({
        'source_type': 'python',
        'python_code': _codeController.text.trim(),
        'parsed_data': mockData,
      });
      
    } catch (e) {
      setState(() {
        _error = 'Failed to analyze Python code: ${e.toString()}';
        _isAnalyzing = false;
      });
    }
  }
  
  Widget _buildErrorSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.error.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildResultsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.success.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: AppColors.success,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Analysis Complete',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildResultItem('Function', _parsedData!['function_name']),
                  _buildResultItem('Description', _parsedData!['description']),
                  _buildResultItem('Parameters', '${_parsedData!['parameters'].length} parameters'),
                  _buildResultItem('Return Type', _parsedData!['return_type']),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'Parameters:',
                    style: AppTextStyles.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  ...(_parsedData!['parameters'] as List).map((param) => 
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.outline,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  param['name'],
                                  style: AppTextStyles.labelMedium.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    param['type'],
                                    style: AppTextStyles.labelSmall.copyWith(
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ),
                                if (!param['required']) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: AppColors.secondary.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      'optional',
                                      style: AppTextStyles.labelSmall.copyWith(
                                        color: AppColors.secondary,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            if (param['description'] != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                param['description'],
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.onSurfaceVariant,
                                ),
                              ),
                            ],
                            if (param['default'] != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Default: ${param['default']}',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.onSurfaceVariant,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ).toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildResultItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
