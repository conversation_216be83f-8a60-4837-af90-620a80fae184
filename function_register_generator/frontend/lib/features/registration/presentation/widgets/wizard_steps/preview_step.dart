import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../shared/presentation/widgets/code_editor.dart';

class PreviewStep extends ConsumerStatefulWidget {
  final Map<String, dynamic> toolData;
  final Function(Map<String, dynamic>) onDataChanged;
  
  const PreviewStep({
    super.key,
    required this.toolData,
    required this.onDataChanged,
  });
  
  @override
  ConsumerState<PreviewStep> createState() => _PreviewStepState();
}

class _PreviewStepState extends ConsumerState<PreviewStep> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isEditing = false;
  late TextEditingController _schemaController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _schemaController = TextEditingController();
    _updateSchemaController();
  }
  
  @override
  void didUpdateWidget(PreviewStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.toolData != widget.toolData) {
      _updateSchemaController();
    }
  }
  
  void _updateSchemaController() {
    final schema = _getInputSchema();
    if (schema != null) {
      _schemaController.text = _formatJson(schema);
    }
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _schemaController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with edit toggle
        Row(
          children: [
            Text(
              'Tool Preview',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _isEditing = !_isEditing;
                });
              },
              icon: Icon(_isEditing ? Icons.visibility : Icons.edit),
              label: Text(_isEditing ? 'View Mode' : 'Edit Mode'),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Tab bar
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Schema'),
            Tab(text: 'Test'),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildSchemaTab(),
              _buildTestTab(),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic info card
          _buildInfoCard(
            'Basic Information',
            [
              _buildInfoRow('Name', widget.toolData['name'] ?? 'Unnamed Tool'),
              _buildInfoRow('Description', widget.toolData['description'] ?? 'No description'),
              _buildInfoRow('Category', _formatCategory(widget.toolData['category'] ?? 'utility')),
              _buildInfoRow('Visibility', _formatVisibility(widget.toolData['visibility'] ?? 'public')),
              _buildInfoRow('Authentication', widget.toolData['requires_auth'] == true ? 'Required' : 'Not Required'),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Tags
          if (widget.toolData['tags'] != null && (widget.toolData['tags'] as List).isNotEmpty) ...[
            _buildInfoCard(
              'Tags',
              [
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: (widget.toolData['tags'] as List<String>).map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.2),
                        ),
                      ),
                      child: Text(
                        tag,
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
          
          // Source info
          _buildSourceInfoCard(),
          
          const SizedBox(height: 16),
          
          // Parameters
          if (_getInputSchema() != null) ...[
            _buildParametersCard(),
          ],
        ],
      ),
    );
  }
  
  Widget _buildSchemaTab() {
    final schema = _getInputSchema();
    
    if (schema == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schema,
              size: 64,
              color: AppColors.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No schema available',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Input Schema (JSON)',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (_isEditing) ...[
              TextButton.icon(
                onPressed: _saveSchemaChanges,
                icon: const Icon(Icons.save),
                label: const Text('Save Changes'),
              ),
              const SizedBox(width: 8),
              TextButton.icon(
                onPressed: _resetSchema,
                icon: const Icon(Icons.refresh),
                label: const Text('Reset'),
              ),
            ],
          ],
        ),
        
        const SizedBox(height: 16),
        
        Expanded(
          child: CodeEditor(
            controller: _schemaController,
            language: 'json',
            readOnly: !_isEditing,
            placeholder: '{}',
          ),
        ),
      ],
    );
  }
  
  Widget _buildTestTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Test Your Tool',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.outline),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.play_circle_outline,
                  size: 64,
                  color: AppColors.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'Tool Testing',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Test functionality will be available after the tool is published.',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: null, // Will be enabled in publish step
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Run Test'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildInfoCard(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSourceInfoCard() {
    final sourceType = widget.toolData['source_type'] ?? 'unknown';
    String sourceDescription;
    IconData sourceIcon;
    Color sourceColor;
    
    switch (sourceType) {
      case 'curl':
        sourceDescription = 'Generated from cURL command';
        sourceIcon = Icons.code;
        sourceColor = AppColors.categoryApi;
        break;
      case 'python':
        sourceDescription = 'Generated from Python function';
        sourceIcon = Icons.integration_instructions;
        sourceColor = AppColors.transportPython;
        break;
      case 'mcp':
        sourceDescription = 'Imported from MCP server';
        sourceIcon = Icons.extension;
        sourceColor = AppColors.transportStdio;
        break;
      case 'natural':
        sourceDescription = 'Generated from natural language description';
        sourceIcon = Icons.auto_awesome;
        sourceColor = AppColors.secondary;
        break;
      default:
        sourceDescription = 'Manual configuration';
        sourceIcon = Icons.build;
        sourceColor = AppColors.primary;
    }
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: sourceColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: sourceColor.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            sourceIcon,
            color: sourceColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Source',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: sourceColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  sourceDescription,
                  style: AppTextStyles.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildParametersCard() {
    final schema = _getInputSchema()!;
    final properties = schema['properties'] as Map<String, dynamic>? ?? {};
    final required = schema['required'] as List<dynamic>? ?? [];
    
    return _buildInfoCard(
      'Parameters (${properties.length})',
      properties.entries.map((entry) {
        final param = entry.value as Map<String, dynamic>;
        final isRequired = required.contains(entry.key);
        
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.outline),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    entry.key,
                    style: AppTextStyles.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      param['type'] ?? 'unknown',
                      style: AppTextStyles.labelSmall.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                  if (isRequired) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'required',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.error,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              if (param['description'] != null) ...[
                const SizedBox(height: 4),
                Text(
                  param['description'],
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        );
      }).toList(),
    );
  }
  
  Map<String, dynamic>? _getInputSchema() {
    if (widget.toolData.containsKey('parsed_data')) {
      final parsedData = widget.toolData['parsed_data'] as Map<String, dynamic>;
      return parsedData['inputSchema'] as Map<String, dynamic>?;
    }
    
    if (widget.toolData.containsKey('generated_tool')) {
      final generatedTool = widget.toolData['generated_tool'] as Map<String, dynamic>;
      return generatedTool['inputSchema'] as Map<String, dynamic>?;
    }
    
    if (widget.toolData.containsKey('selected_tools')) {
      final selectedTools = widget.toolData['selected_tools'] as List;
      if (selectedTools.isNotEmpty) {
        return selectedTools.first['inputSchema'] as Map<String, dynamic>?;
      }
    }
    
    return null;
  }
  
  String _formatCategory(String category) {
    return category.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1)
    ).join(' ');
  }
  
  String _formatVisibility(String visibility) {
    return visibility[0].toUpperCase() + visibility.substring(1);
  }
  
  String _formatJson(Map<String, dynamic> json) {
    // Simple JSON formatting - in a real app, use a proper JSON formatter
    return json.toString().replaceAll(', ', ',\n  ').replaceAll('{', '{\n  ').replaceAll('}', '\n}');
  }
  
  void _saveSchemaChanges() {
    // TODO: Parse and validate the edited schema
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Schema changes saved'),
        backgroundColor: AppColors.success,
      ),
    );
  }
  
  void _resetSchema() {
    _updateSchemaController();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Schema reset to original'),
      ),
    );
  }
}
