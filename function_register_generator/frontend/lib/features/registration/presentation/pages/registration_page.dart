import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../core/router/app_router.dart';
import '../widgets/registration_wizard.dart';
import '../widgets/registration_type_selector.dart';

class RegistrationPage extends ConsumerStatefulWidget {
  final RegistrationType? initialType;
  
  const RegistrationPage({
    super.key,
    this.initialType,
  });
  
  @override
  ConsumerState<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends ConsumerState<RegistrationPage> {
  RegistrationType? selectedType;
  
  @override
  void initState() {
    super.initState();
    selectedType = widget.initialType;
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: selectedType == null 
            ? _buildTypeSelector()
            : _buildRegistrationWizard(),
      ),
    );
  }
  
  Widget _buildTypeSelector() {
    return SingleChildScrollView(
      padding: context.responsivePadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.add_circle,
                color: AppColors.primary,
                size: 32,
              ),
              const SizedBox(width: 12),
              Text(
                'Register New Tool',
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Choose how you want to register your tool',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Type selector
          RegistrationTypeSelector(
            onTypeSelected: (type) {
              setState(() {
                selectedType = type;
              });
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildRegistrationWizard() {
    return RegistrationWizard(
      type: selectedType!,
      onBack: () {
        setState(() {
          selectedType = null;
        });
      },
      onComplete: (toolData) {
        // TODO: Handle tool registration completion
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tool registered successfully!'),
            backgroundColor: AppColors.success,
          ),
        );
      },
    );
  }
}