import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: context.responsivePadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hero section
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(context.responsiveSpacing * 2),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome to Tool Registry',
                    style: (context.isDesktop
                        ? AppTextStyles.displaySmall
                        : AppTextStyles.headlineLarge).copyWith(
                      color: AppColors.onPrimary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Discover, register, and share powerful tools with the community',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.onPrimary.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => context.goNamed('register'),
                        icon: const Icon(Icons.add_circle),
                        label: const Text('Register Tool'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.onPrimary,
                          foregroundColor: AppColors.primary,
                        ),
                      ),
                      OutlinedButton.icon(
                        onPressed: () => context.goNamed('tools'),
                        icon: const Icon(Icons.explore),
                        label: const Text('Browse Tools'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.onPrimary,
                          side: BorderSide(color: AppColors.onPrimary),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            SizedBox(height: context.responsiveSpacing * 2),
            
            // Quick actions
            Text(
              'Quick Actions',
              style: AppTextStyles.headlineSmall,
            ),
            
            SizedBox(height: context.responsiveSpacing),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: context.gridColumns,
              crossAxisSpacing: context.responsiveSpacing,
              mainAxisSpacing: context.responsiveSpacing,
              childAspectRatio: 1.2,
              children: [
                _QuickActionCard(
                  icon: Icons.code,
                  title: 'From cURL',
                  description: 'Convert cURL commands to tools',
                  color: AppColors.categoryApi,
                  onTap: () => context.goNamed('register-curl'),
                ),
                _QuickActionCard(
                  icon: Icons.integration_instructions,
                  title: 'Python Function',
                  description: 'Register Python functions',
                  color: AppColors.transportPython,
                  onTap: () => context.goNamed('register-python'),
                ),
                _QuickActionCard(
                  icon: Icons.extension,
                  title: 'MCP Tool',
                  description: 'Import MCP tools',
                  color: AppColors.transportStdio,
                  onTap: () => context.goNamed('register-mcp'),
                ),
                _QuickActionCard(
                  icon: Icons.chat,
                  title: 'Natural Language',
                  description: 'Describe your tool in plain English',
                  color: AppColors.secondary,
                  onTap: () => context.goNamed('register-natural'),
                ),
              ],
            ),
            
            SizedBox(height: context.responsiveSpacing * 2),
            
            // Recent tools placeholder
            Text(
              'Recently Added Tools',
              style: AppTextStyles.headlineSmall,
            ),
            
            SizedBox(height: context.responsiveSpacing),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.build_circle_outlined,
                    size: 64,
                    color: AppColors.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No tools yet',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start by registering your first tool',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final Color color;
  final VoidCallback onTap;
  
  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
    required this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: AppTextStyles.titleMedium,
              ),
              const SizedBox(height: 4),
              Expanded(
                child: Text(
                  description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}