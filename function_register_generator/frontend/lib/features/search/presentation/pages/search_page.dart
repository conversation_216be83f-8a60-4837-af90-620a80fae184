import 'package:flutter/material.dart';

import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';

class SearchPage extends StatelessWidget {
  final String? initialQuery;
  
  const SearchPage({
    super.key,
    this.initialQuery,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: context.responsivePadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Search Tools',
              style: AppTextStyles.headlineMedium,
            ),
            const SizedBox(height: 16),
            if (initialQuery != null)
              Text('Query: $initialQuery'),
            const SizedBox(height: 16),
            const Expanded(
              child: Center(
                child: Text('Search page - Coming soon'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}