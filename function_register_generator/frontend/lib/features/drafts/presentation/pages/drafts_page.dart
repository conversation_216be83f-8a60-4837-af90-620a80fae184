import 'package:flutter/material.dart';

import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';

class DraftsPage extends StatelessWidget {
  const DraftsPage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: context.responsivePadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'My Drafts',
              style: AppTextStyles.headlineMedium,
            ),
            const SizedBox(height: 16),
            const Expanded(
              child: Center(
                child: Text('Drafts page - Coming soon'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DraftEditPage extends StatelessWidget {
  final String draftId;
  
  const DraftEditPage({
    super.key,
    required this.draftId,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: context.responsivePadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Edit Draft',
              style: AppTextStyles.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text('Draft ID: $draftId'),
            const SizedBox(height: 16),
            const Expanded(
              child: Center(
                child: Text('Draft edit page - Coming soon'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}