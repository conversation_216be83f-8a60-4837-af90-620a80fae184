import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../shared/presentation/widgets/loading_overlay.dart';
import 'tool_card.dart';

enum ToolsViewMode { grid, list, compact }

class ToolsGrid extends ConsumerStatefulWidget {
  final List<Map<String, dynamic>> tools;
  final bool isLoading;
  final bool hasMore;
  final VoidCallback? onLoadMore;
  final Function(Map<String, dynamic>)? onToolTap;
  final Function(Map<String, dynamic>)? onToolFavorite;
  final Function(Map<String, dynamic>)? onToolTryOut;
  final ToolsViewMode viewMode;
  final String? emptyMessage;
  
  const ToolsGrid({
    super.key,
    required this.tools,
    this.isLoading = false,
    this.hasMore = false,
    this.onLoadMore,
    this.onToolTap,
    this.onToolFavorite,
    this.onToolTryOut,
    this.viewMode = ToolsViewMode.grid,
    this.emptyMessage,
  });
  
  @override
  ConsumerState<ToolsGrid> createState() => _ToolsGridState();
}

class _ToolsGridState extends ConsumerState<ToolsGrid> {
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
  
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (widget.hasMore && !widget.isLoading) {
        widget.onLoadMore?.call();
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (widget.tools.isEmpty && !widget.isLoading) {
      return _buildEmptyState();
    }
    
    return Column(
      children: [
        Expanded(
          child: _buildContent(),
        ),
        if (widget.isLoading) ...[
          const Padding(
            padding: EdgeInsets.all(16),
            child: CircularProgressIndicator(),
          ),
        ],
      ],
    );
  }
  
  Widget _buildContent() {
    switch (widget.viewMode) {
      case ToolsViewMode.grid:
        return _buildGridView();
      case ToolsViewMode.list:
        return _buildListView();
      case ToolsViewMode.compact:
        return _buildCompactView();
    }
  }
  
  Widget _buildGridView() {
    return MasonryGridView.count(
      controller: _scrollController,
      crossAxisCount: _getCrossAxisCount(context),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      padding: context.responsivePadding,
      itemCount: widget.tools.length,
      itemBuilder: (context, index) {
        final tool = widget.tools[index];
        return ToolCard(
          tool: tool,
          onTap: () => widget.onToolTap?.call(tool),
          onFavorite: () => widget.onToolFavorite?.call(tool),
          onTryOut: () => widget.onToolTryOut?.call(tool),
        );
      },
    );
  }
  
  Widget _buildListView() {
    return ListView.separated(
      controller: _scrollController,
      padding: context.responsivePadding,
      itemCount: widget.tools.length,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        final tool = widget.tools[index];
        return ToolCard(
          tool: tool,
          onTap: () => widget.onToolTap?.call(tool),
          onFavorite: () => widget.onToolFavorite?.call(tool),
          onTryOut: () => widget.onToolTryOut?.call(tool),
        );
      },
    );
  }
  
  Widget _buildCompactView() {
    return ListView.separated(
      controller: _scrollController,
      padding: context.responsivePadding,
      itemCount: widget.tools.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final tool = widget.tools[index];
        return ToolCard(
          tool: tool,
          isCompact: true,
          onTap: () => widget.onToolTap?.call(tool),
          onFavorite: () => widget.onToolFavorite?.call(tool),
          onTryOut: () => widget.onToolTryOut?.call(tool),
        );
      },
    );
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            widget.emptyMessage ?? 'No tools found',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
  
  int _getCrossAxisCount(BuildContext context) {
    if (context.isDesktop) {
      return 3;
    } else if (context.isTablet) {
      return 2;
    } else {
      return 1;
    }
  }
}

class ToolsViewModeSelector extends StatelessWidget {
  final ToolsViewMode selectedMode;
  final Function(ToolsViewMode) onModeChanged;
  
  const ToolsViewModeSelector({
    super.key,
    required this.selectedMode,
    required this.onModeChanged,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.outline),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildModeButton(
            ToolsViewMode.grid,
            Icons.grid_view,
            'Grid',
          ),
          _buildModeButton(
            ToolsViewMode.list,
            Icons.view_list,
            'List',
          ),
          _buildModeButton(
            ToolsViewMode.compact,
            Icons.view_compact,
            'Compact',
          ),
        ],
      ),
    );
  }
  
  Widget _buildModeButton(ToolsViewMode mode, IconData icon, String tooltip) {
    final isSelected = selectedMode == mode;
    
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: () => onModeChanged(mode),
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary.withOpacity(0.1) : null,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 20,
            color: isSelected ? AppColors.primary : AppColors.onSurfaceVariant,
          ),
        ),
      ),
    );
  }
}

class ToolsStats extends StatelessWidget {
  final int totalCount;
  final int filteredCount;
  final bool isLoading;
  
  const ToolsStats({
    super.key,
    required this.totalCount,
    required this.filteredCount,
    this.isLoading = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.outline),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isLoading) ...[
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
          ],
          Icon(
            Icons.info_outline,
            size: 16,
            color: AppColors.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            filteredCount == totalCount
                ? '$totalCount tools'
                : '$filteredCount of $totalCount tools',
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}

class ToolsLoadingGrid extends StatelessWidget {
  final int itemCount;
  final ToolsViewMode viewMode;
  
  const ToolsLoadingGrid({
    super.key,
    this.itemCount = 6,
    this.viewMode = ToolsViewMode.grid,
  });
  
  @override
  Widget build(BuildContext context) {
    switch (viewMode) {
      case ToolsViewMode.grid:
        return MasonryGridView.count(
          crossAxisCount: _getCrossAxisCount(context),
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          padding: context.responsivePadding,
          itemCount: itemCount,
          itemBuilder: (context, index) => _buildSkeletonCard(),
        );
      case ToolsViewMode.list:
      case ToolsViewMode.compact:
        return ListView.separated(
          padding: context.responsivePadding,
          itemCount: itemCount,
          separatorBuilder: (context, index) => const SizedBox(height: 16),
          itemBuilder: (context, index) => _buildSkeletonCard(
            isCompact: viewMode == ToolsViewMode.compact,
          ),
        );
    }
  }
  
  Widget _buildSkeletonCard({bool isCompact = false}) {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(16),
        height: isCompact ? 80 : 200,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header skeleton
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.outline,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.outline,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 100,
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppColors.outline,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            if (!isCompact) ...[
              const SizedBox(height: 16),
              
              // Description skeleton
              Container(
                width: double.infinity,
                height: 12,
                decoration: BoxDecoration(
                  color: AppColors.outline,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: 200,
                height: 12,
                decoration: BoxDecoration(
                  color: AppColors.outline,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              
              const Spacer(),
              
              // Actions skeleton
              Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 36,
                      decoration: BoxDecoration(
                        color: AppColors.outline,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    width: 80,
                    height: 36,
                    decoration: BoxDecoration(
                      color: AppColors.outline,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  int _getCrossAxisCount(BuildContext context) {
    if (context.isDesktop) {
      return 3;
    } else if (context.isTablet) {
      return 2;
    } else {
      return 1;
    }
  }
}
