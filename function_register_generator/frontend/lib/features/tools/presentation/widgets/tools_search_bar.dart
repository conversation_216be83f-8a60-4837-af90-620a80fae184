import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/utils/debouncer.dart';
import '../../../../shared/services/suggestion_service.dart';

class ToolsSearchBar extends ConsumerStatefulWidget {
  final String? initialQuery;
  final Function(String) onQueryChanged;
  final Function()? onClear;
  final List<String> suggestions;
  final bool isLoading;
  
  const ToolsSearchBar({
    super.key,
    this.initialQuery,
    required this.onQueryChanged,
    this.onClear,
    this.suggestions = const [],
    this.isLoading = false,
  });
  
  @override
  ConsumerState<ToolsSearchBar> createState() => _ToolsSearchBarState();
}

class _ToolsSearchBarState extends ConsumerState<ToolsSearchBar> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();
  bool _showSuggestions = false;
  List<SearchSuggestion> _smartSuggestions = [];
  late SearchDebouncer _debouncer;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
    _focusNode.addListener(_onFocusChanged);
    _debouncer = SearchDebouncer(
      onSearch: _performSearch,
      delay: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _showSuggestions = _focusNode.hasFocus &&
          (_smartSuggestions.isNotEmpty || widget.suggestions.isNotEmpty);
    });
  }

  void _performSearch(String query) {
    widget.onQueryChanged(query);
    _updateSmartSuggestions(query);
  }

  void _updateSmartSuggestions(String query) {
    // This would typically get tools from a provider
    final mockTools = <Map<String, dynamic>>[];

    final suggestions = SuggestionService.generateSearchSuggestions(
      query: query,
      tools: mockTools,
      recentSearches: ['weather', 'api', 'database'],
      popularSearches: ['ai tools', 'data processing', 'api integration'],
    );

    setState(() {
      _smartSuggestions = suggestions;
      _showSuggestions = _focusNode.hasFocus && suggestions.isNotEmpty;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _focusNode.hasFocus ? AppColors.primary : AppColors.outline,
              width: _focusNode.hasFocus ? 2 : 1,
            ),
            boxShadow: _focusNode.hasFocus ? [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            style: AppTextStyles.bodyLarge,
            decoration: InputDecoration(
              hintText: 'Search tools by name, description, or tags...',
              hintStyle: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
              prefixIcon: widget.isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(12),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : Icon(
                      Icons.search,
                      color: AppColors.onSurfaceVariant,
                    ),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _controller.clear();
                        widget.onQueryChanged('');
                        widget.onClear?.call();
                      },
                      icon: Icon(
                        Icons.clear,
                        color: AppColors.onSurfaceVariant,
                      ),
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            onChanged: (value) {
              _debouncer.search(value);
              setState(() {
                _showSuggestions = _focusNode.hasFocus &&
                    (value.isNotEmpty || _smartSuggestions.isNotEmpty);
              });
            },
            onSubmitted: (value) {
              setState(() {
                _showSuggestions = false;
              });
            },
          ),
        ),
        
        // Suggestions dropdown
        if (_showSuggestions) ...[
          const SizedBox(height: 4),
          Container(
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.outline),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _smartSuggestions.isNotEmpty
                  ? _smartSuggestions.length
                  : widget.suggestions.length,
              itemBuilder: (context, index) {
                if (_smartSuggestions.isNotEmpty) {
                  final suggestion = _smartSuggestions[index];
                  return ListTile(
                    dense: true,
                    leading: Icon(
                      _getSuggestionIcon(suggestion.type),
                      size: 16,
                      color: _getSuggestionColor(suggestion.type),
                    ),
                    title: Text(
                      suggestion.text,
                      style: AppTextStyles.bodyMedium,
                    ),
                    subtitle: suggestion.type == SuggestionType.recent
                        ? const Text('Recent search')
                        : suggestion.type == SuggestionType.popular
                            ? const Text('Popular search')
                            : null,
                    onTap: () {
                      _controller.text = suggestion.text;
                      _debouncer.searchImmediate(suggestion.text);
                      setState(() {
                        _showSuggestions = false;
                      });
                      _focusNode.unfocus();
                    },
                  );
                } else {
                  final suggestion = widget.suggestions[index];
                  return ListTile(
                    dense: true,
                    leading: Icon(
                      Icons.search,
                      size: 16,
                      color: AppColors.onSurfaceVariant,
                    ),
                    title: Text(
                      suggestion,
                      style: AppTextStyles.bodyMedium,
                    ),
                    onTap: () {
                      _controller.text = suggestion;
                      _debouncer.searchImmediate(suggestion);
                      setState(() {
                        _showSuggestions = false;
                      });
                      _focusNode.unfocus();
                    },
                  );
                }
              },
            ),
          ),
        ],
      ],
    );
  }

  IconData _getSuggestionIcon(SuggestionType type) {
    switch (type) {
      case SuggestionType.toolName:
        return Icons.extension;
      case SuggestionType.category:
        return Icons.category;
      case SuggestionType.tag:
        return Icons.label;
      case SuggestionType.description:
        return Icons.description;
      case SuggestionType.recent:
        return Icons.history;
      case SuggestionType.popular:
        return Icons.trending_up;
    }
  }

  Color _getSuggestionColor(SuggestionType type) {
    switch (type) {
      case SuggestionType.toolName:
        return AppColors.primary;
      case SuggestionType.category:
        return AppColors.secondary;
      case SuggestionType.tag:
        return AppColors.success;
      case SuggestionType.description:
        return AppColors.onSurfaceVariant;
      case SuggestionType.recent:
        return AppColors.warning;
      case SuggestionType.popular:
        return AppColors.error;
    }
  }
}

class ToolsFilterChips extends ConsumerWidget {
  final List<String> selectedCategories;
  final List<String> selectedTags;
  final String? selectedSort;
  final Function(List<String>) onCategoriesChanged;
  final Function(List<String>) onTagsChanged;
  final Function(String?) onSortChanged;
  final VoidCallback? onClearAll;
  
  const ToolsFilterChips({
    super.key,
    required this.selectedCategories,
    required this.selectedTags,
    this.selectedSort,
    required this.onCategoriesChanged,
    required this.onTagsChanged,
    required this.onSortChanged,
    this.onClearAll,
  });
  
  static const List<String> categories = [
    'utility',
    'data',
    'api',
    'ai',
    'communication',
    'productivity',
    'development',
    'entertainment',
    'finance',
    'health',
    'education',
    'other',
  ];
  
  static const List<String> sortOptions = [
    'name',
    'created_at',
    'updated_at',
    'usage_count',
    'rating',
  ];
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasFilters = selectedCategories.isNotEmpty || 
                      selectedTags.isNotEmpty || 
                      selectedSort != null;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Filter header
        Row(
          children: [
            Icon(
              Icons.filter_list,
              size: 20,
              color: AppColors.onSurfaceVariant,
            ),
            const SizedBox(width: 8),
            Text(
              'Filters',
              style: AppTextStyles.titleSmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (hasFilters) ...[
              TextButton.icon(
                onPressed: onClearAll,
                icon: const Icon(Icons.clear_all, size: 16),
                label: const Text('Clear All'),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
            ],
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Categories
        Text(
          'Categories',
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: categories.map((category) {
            final isSelected = selectedCategories.contains(category);
            return FilterChip(
              label: Text(_formatCategoryName(category)),
              selected: isSelected,
              onSelected: (selected) {
                final newCategories = List<String>.from(selectedCategories);
                if (selected) {
                  newCategories.add(category);
                } else {
                  newCategories.remove(category);
                }
                onCategoriesChanged(newCategories);
              },
              backgroundColor: AppColors.surface,
              selectedColor: AppColors.primary.withOpacity(0.2),
              checkmarkColor: AppColors.primary,
              labelStyle: AppTextStyles.labelSmall.copyWith(
                color: isSelected ? AppColors.primary : AppColors.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            );
          }).toList(),
        ),
        
        const SizedBox(height: 16),
        
        // Sort
        Text(
          'Sort By',
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: sortOptions.map((sort) {
            final isSelected = selectedSort == sort;
            return ChoiceChip(
              label: Text(_formatSortName(sort)),
              selected: isSelected,
              onSelected: (selected) {
                onSortChanged(selected ? sort : null);
              },
              backgroundColor: AppColors.surface,
              selectedColor: AppColors.secondary.withOpacity(0.2),
              labelStyle: AppTextStyles.labelSmall.copyWith(
                color: isSelected ? AppColors.secondary : AppColors.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            );
          }).toList(),
        ),
        
        // Active filters summary
        if (hasFilters) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Active Filters',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                if (selectedCategories.isNotEmpty) ...[
                  Text(
                    'Categories: ${selectedCategories.map(_formatCategoryName).join(', ')}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
                if (selectedSort != null) ...[
                  Text(
                    'Sort: ${_formatSortName(selectedSort!)}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }
  
  String _formatCategoryName(String category) {
    return category.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1)
    ).join(' ');
  }
  
  String _formatSortName(String sort) {
    switch (sort) {
      case 'name':
        return 'Name';
      case 'created_at':
        return 'Created Date';
      case 'updated_at':
        return 'Updated Date';
      case 'usage_count':
        return 'Usage Count';
      case 'rating':
        return 'Rating';
      default:
        return sort;
    }
  }
}
