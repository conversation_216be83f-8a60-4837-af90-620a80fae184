import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/presentation/widgets/code_editor.dart';
import '../../../../shared/presentation/widgets/loading_overlay.dart';

class ToolDetailPanel extends ConsumerStatefulWidget {
  final Map<String, dynamic> tool;
  final VoidCallback? onClose;
  final VoidCallback? onTryOut;
  final VoidCallback? onFavorite;
  final VoidCallback? onEdit;
  
  const ToolDetailPanel({
    super.key,
    required this.tool,
    this.onClose,
    this.onTryOut,
    this.onFavorite,
    this.onEdit,
  });
  
  @override
  ConsumerState<ToolDetailPanel> createState() => _ToolDetailPanelState();
}

class _ToolDetailPanelState extends ConsumerState<ToolDetailPanel>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isTesting = false;
  Map<String, dynamic>? _testResult;
  final Map<String, TextEditingController> _paramControllers = {};
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeParameterControllers();
  }
  
  void _initializeParameterControllers() {
    final schema = widget.tool['input_schema'] as Map<String, dynamic>?;
    if (schema != null && schema['properties'] != null) {
      final properties = schema['properties'] as Map<String, dynamic>;
      for (final param in properties.keys) {
        _paramControllers[param] = TextEditingController();
      }
    }
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    for (final controller in _paramControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 400,
      height: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          left: BorderSide(color: AppColors.outline),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(-2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Overview'),
              Tab(text: 'Schema'),
              Tab(text: 'Try Out'),
            ],
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildSchemaTab(),
                _buildTryOutTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        border: Border(
          bottom: BorderSide(color: AppColors.outline),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and close button
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.tool['name'] ?? 'Unnamed Tool',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                onPressed: widget.onClose,
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Status and category badges
          Row(
            children: [
              _buildStatusBadge(),
              const SizedBox(width: 8),
              _buildCategoryBadge(),
              const SizedBox(width: 8),
              _buildTransportBadge(),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Description
          Text(
            widget.tool['description'] ?? 'No description available',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.onTryOut,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Try Out'),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: widget.onFavorite,
                icon: Icon(
                  widget.tool['is_favorited'] == true 
                      ? Icons.favorite 
                      : Icons.favorite_border,
                  color: widget.tool['is_favorited'] == true 
                      ? AppColors.error 
                      : null,
                ),
              ),
              if (widget.onEdit != null) ...[
                IconButton(
                  onPressed: widget.onEdit,
                  icon: const Icon(Icons.edit),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic info
          _buildInfoSection('Basic Information', [
            _buildInfoRow('Author', widget.tool['author'] ?? 'Unknown'),
            _buildInfoRow('Version', widget.tool['version'] ?? '1.0.0'),
            _buildInfoRow('Created', _formatDate(widget.tool['created_at'])),
            _buildInfoRow('Updated', _formatDate(widget.tool['updated_at'])),
          ]),
          
          const SizedBox(height: 24),
          
          // Stats
          _buildInfoSection('Statistics', [
            _buildInfoRow('Usage Count', widget.tool['usage_count']?.toString() ?? '0'),
            _buildInfoRow('Rating', '${widget.tool['rating'] ?? 0.0}/5.0'),
            _buildInfoRow('Favorites', widget.tool['favorite_count']?.toString() ?? '0'),
          ]),
          
          const SizedBox(height: 24),
          
          // Tags
          if (widget.tool['tags'] != null && (widget.tool['tags'] as List).isNotEmpty) ...[
            _buildTagsSection(),
            const SizedBox(height: 24),
          ],
          
          // Documentation
          if (widget.tool['documentation'] != null) ...[
            _buildDocumentationSection(),
          ],
        ],
      ),
    );
  }
  
  Widget _buildSchemaTab() {
    final schema = widget.tool['input_schema'] as Map<String, dynamic>?;
    
    if (schema == null) {
      return const Center(
        child: Text('No schema available'),
      );
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Input Schema',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Parameters
          if (schema['properties'] != null) ...[
            _buildParametersSection(schema),
            const SizedBox(height: 24),
          ],
          
          // Raw schema
          Text(
            'Raw Schema (JSON)',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Container(
            height: 200,
            child: CodeEditor(
              controller: TextEditingController(text: _formatJson(schema)),
              language: 'json',
              readOnly: true,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTryOutTab() {
    return LoadingOverlay(
      isLoading: _isTesting,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Parameters',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Parameter inputs
            _buildParameterInputs(),
            
            const SizedBox(height: 24),
            
            // Test button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isTesting ? null : _runTest,
                icon: const Icon(Icons.play_arrow),
                label: const Text('Run Test'),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Test result
            if (_testResult != null) ...[
              _buildTestResult(),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: (widget.tool['tags'] as List<String>).map((tag) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.2),
                ),
              ),
              child: Text(
                tag,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
  
  Widget _buildDocumentationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Documentation',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.outline),
          ),
          child: Text(
            widget.tool['documentation'],
            style: AppTextStyles.bodyMedium,
          ),
        ),
      ],
    );
  }
  
  Widget _buildParametersSection(Map<String, dynamic> schema) {
    final properties = schema['properties'] as Map<String, dynamic>;
    final required = schema['required'] as List<dynamic>? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Parameters',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...properties.entries.map((entry) {
          final param = entry.value as Map<String, dynamic>;
          final isRequired = required.contains(entry.key);
          
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.outline),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      entry.key,
                      style: AppTextStyles.labelMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        param['type'] ?? 'unknown',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                    if (isRequired) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.error.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'required',
                          style: AppTextStyles.labelSmall.copyWith(
                            color: AppColors.error,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                if (param['description'] != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    param['description'],
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          );
        }).toList(),
      ],
    );
  }
  
  Widget _buildParameterInputs() {
    final schema = widget.tool['input_schema'] as Map<String, dynamic>?;
    if (schema == null || schema['properties'] == null) {
      return const Text('No parameters required');
    }
    
    final properties = schema['properties'] as Map<String, dynamic>;
    final required = schema['required'] as List<dynamic>? ?? [];
    
    return Column(
      children: properties.entries.map((entry) {
        final param = entry.value as Map<String, dynamic>;
        final isRequired = required.contains(entry.key);
        final controller = _paramControllers[entry.key]!;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              labelText: entry.key + (isRequired ? ' *' : ''),
              hintText: param['description'] ?? 'Enter ${entry.key}',
              border: const OutlineInputBorder(),
            ),
          ),
        );
      }).toList(),
    );
  }
  
  Widget _buildTestResult() {
    final isSuccess = _testResult!['success'] == true;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              color: isSuccess ? AppColors.success : AppColors.error,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              isSuccess ? 'Test Successful' : 'Test Failed',
              style: AppTextStyles.titleSmall.copyWith(
                color: isSuccess ? AppColors.success : AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        Container(
          height: 200,
          child: CodeEditor(
            controller: TextEditingController(text: _formatJson(_testResult!)),
            language: 'json',
            readOnly: true,
          ),
        ),
      ],
    );
  }
  
  Widget _buildStatusBadge() {
    final status = widget.tool['status'] ?? 'active';
    Color color;
    String label;
    
    switch (status) {
      case 'active':
        color = AppColors.success;
        label = 'Active';
        break;
      case 'deprecated':
        color = AppColors.warning;
        label = 'Deprecated';
        break;
      case 'private':
        color = AppColors.secondary;
        label = 'Private';
        break;
      default:
        color = AppColors.onSurfaceVariant;
        label = 'Unknown';
    }
    
    return _buildBadge(label, color);
  }
  
  Widget _buildCategoryBadge() {
    final category = widget.tool['category'] ?? 'utility';
    return _buildBadge(
      category.toUpperCase(),
      _getCategoryColor(category),
    );
  }
  
  Widget _buildTransportBadge() {
    final transport = widget.tool['transport'] ?? 'http';
    return _buildBadge(
      transport.toUpperCase(),
      _getTransportColor(transport),
    );
  }
  
  Widget _buildBadge(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label,
        style: AppTextStyles.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
  
  Color _getCategoryColor(String category) {
    switch (category) {
      case 'data':
        return AppColors.categoryData;
      case 'api':
        return AppColors.categoryApi;
      case 'utility':
        return AppColors.categoryUtility;
      case 'ai':
        return AppColors.secondary;
      default:
        return AppColors.primary;
    }
  }
  
  Color _getTransportColor(String transport) {
    switch (transport) {
      case 'http':
        return AppColors.categoryApi;
      case 'python':
        return AppColors.transportPython;
      case 'stdio':
        return AppColors.transportStdio;
      default:
        return AppColors.onSurfaceVariant;
    }
  }
  
  String _formatDate(dynamic date) {
    if (date == null) return 'Unknown';
    if (date is String) {
      try {
        final parsedDate = DateTime.parse(date);
        return '${parsedDate.day}/${parsedDate.month}/${parsedDate.year}';
      } catch (e) {
        return 'Unknown';
      }
    }
    return 'Unknown';
  }
  
  String _formatJson(Map<String, dynamic> json) {
    // Simple JSON formatting
    return json.toString().replaceAll(', ', ',\n  ').replaceAll('{', '{\n  ').replaceAll('}', '\n}');
  }
  
  Future<void> _runTest() async {
    setState(() {
      _isTesting = true;
      _testResult = null;
    });
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock test result
      setState(() {
        _testResult = {
          'success': true,
          'result': {
            'message': 'Tool executed successfully',
            'output': 'Sample output data',
            'execution_time': '0.5s',
          },
          'timestamp': DateTime.now().toIso8601String(),
        };
        _isTesting = false;
      });
    } catch (e) {
      setState(() {
        _testResult = {
          'success': false,
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        };
        _isTesting = false;
      });
    }
  }
}
