import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/presentation/widgets/loading_overlay.dart';

class ToolCard extends ConsumerStatefulWidget {
  final Map<String, dynamic> tool;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final VoidCallback? onTryOut;
  final bool isCompact;
  
  const ToolCard({
    super.key,
    required this.tool,
    this.onTap,
    this.onFavorite,
    this.onTryOut,
    this.isCompact = false,
  });
  
  @override
  ConsumerState<ToolCard> createState() => _ToolCardState();
}

class _ToolCardState extends ConsumerState<ToolCard>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  bool _isFavorited = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _elevationAnimation = Tween<double>(
      begin: 2.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _isFavorited = widget.tool['is_favorited'] ?? false;
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => _isHovered = true);
        _animationController.forward();
      },
      onExit: (_) {
        setState(() => _isHovered = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Card(
              elevation: _elevationAnimation.value,
              shadowColor: _getCategoryColor().withOpacity(0.3),
              child: InkWell(
                onTap: () {
                  // Add tap animation
                  widget.onTap?.call();
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: widget.isCompact
                      ? _buildCompactContent()
                      : _buildFullContent(),
                ),
              ),
            )
                .animate(target: _isHovered ? 1 : 0)
                .boxShadow(
                  begin: BoxShadow(
                    color: Colors.transparent,
                    blurRadius: 0,
                    offset: const Offset(0, 0),
                  ),
                  end: BoxShadow(
                    color: _getCategoryColor().withOpacity(0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                  duration: const Duration(milliseconds: 200),
                ),
          );
        },
      ),
    )
        .animate()
        .fadeIn(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        )
        .slideY(
          begin: 0.1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
  }
  
  Widget _buildFullContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            // Category icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getCategoryColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getCategoryIcon(),
                color: _getCategoryColor(),
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Tool name and status
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.tool['name'] ?? 'Unnamed Tool',
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      _buildStatusBadge(),
                      const SizedBox(width: 8),
                      _buildTransportBadge(),
                    ],
                  ),
                ],
              ),
            ),
            
            // Favorite button
            IconButton(
              onPressed: _toggleFavorite,
              icon: Icon(
                _isFavorited ? Icons.favorite : Icons.favorite_border,
                color: _isFavorited ? AppColors.error : AppColors.onSurfaceVariant,
                size: 20,
              ),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Description
        Text(
          widget.tool['description'] ?? 'No description available',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        const SizedBox(height: 12),
        
        // Tags
        if (widget.tool['tags'] != null && (widget.tool['tags'] as List).isNotEmpty) ...[
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: (widget.tool['tags'] as List<String>).take(3).map((tag) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.primary.withOpacity(0.2),
                  ),
                ),
                child: Text(
                  tag,
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 12),
        ],
        
        // Stats
        Row(
          children: [
            _buildStatItem(Icons.play_arrow, widget.tool['usage_count']?.toString() ?? '0'),
            const SizedBox(width: 16),
            _buildStatItem(Icons.star, widget.tool['rating']?.toString() ?? '0.0'),
            const SizedBox(width: 16),
            _buildStatItem(Icons.access_time, _formatDate(widget.tool['updated_at'])),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Actions
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: widget.onTap,
                icon: const Icon(Icons.visibility, size: 16),
                label: const Text('View Details'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: widget.onTryOut,
              icon: const Icon(Icons.play_arrow, size: 16),
              label: const Text('Try Out'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _getCategoryColor(),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildCompactContent() {
    return Row(
      children: [
        // Category icon
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: _getCategoryColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            _getCategoryIcon(),
            color: _getCategoryColor(),
            size: 16,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Tool info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.tool['name'] ?? 'Unnamed Tool',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                widget.tool['description'] ?? 'No description',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        
        // Quick actions
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: _toggleFavorite,
              icon: Icon(
                _isFavorited ? Icons.favorite : Icons.favorite_border,
                color: _isFavorited ? AppColors.error : AppColors.onSurfaceVariant,
                size: 18,
              ),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
            IconButton(
              onPressed: widget.onTryOut,
              icon: const Icon(Icons.play_arrow, size: 18),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildStatusBadge() {
    final status = widget.tool['status'] ?? 'active';
    Color color;
    String label;
    
    switch (status) {
      case 'active':
        color = AppColors.success;
        label = 'Active';
        break;
      case 'deprecated':
        color = AppColors.warning;
        label = 'Deprecated';
        break;
      case 'private':
        color = AppColors.secondary;
        label = 'Private';
        break;
      default:
        color = AppColors.onSurfaceVariant;
        label = 'Unknown';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label,
        style: AppTextStyles.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
  
  Widget _buildTransportBadge() {
    final transport = widget.tool['transport'] ?? 'http';
    Color color;
    
    switch (transport) {
      case 'http':
        color = AppColors.categoryApi;
        break;
      case 'python':
        color = AppColors.transportPython;
        break;
      case 'stdio':
        color = AppColors.transportStdio;
        break;
      default:
        color = AppColors.onSurfaceVariant;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        transport.toUpperCase(),
        style: AppTextStyles.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
  
  Widget _buildStatItem(IconData icon, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: AppColors.onSurfaceVariant,
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: AppTextStyles.labelSmall.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
  
  Color _getCategoryColor() {
    final category = widget.tool['category'] ?? 'utility';
    switch (category) {
      case 'data':
        return AppColors.categoryData;
      case 'api':
        return AppColors.categoryApi;
      case 'utility':
        return AppColors.categoryUtility;
      case 'ai':
        return AppColors.secondary;
      default:
        return AppColors.primary;
    }
  }
  
  IconData _getCategoryIcon() {
    final category = widget.tool['category'] ?? 'utility';
    switch (category) {
      case 'data':
        return Icons.storage;
      case 'api':
        return Icons.api;
      case 'utility':
        return Icons.build;
      case 'ai':
        return Icons.psychology;
      case 'communication':
        return Icons.chat;
      case 'productivity':
        return Icons.work;
      case 'development':
        return Icons.code;
      case 'entertainment':
        return Icons.games;
      case 'finance':
        return Icons.attach_money;
      case 'health':
        return Icons.health_and_safety;
      case 'education':
        return Icons.school;
      default:
        return Icons.extension;
    }
  }
  
  String _formatDate(dynamic date) {
    if (date == null) return 'Unknown';
    if (date is String) {
      try {
        final parsedDate = DateTime.parse(date);
        final now = DateTime.now();
        final difference = now.difference(parsedDate);
        
        if (difference.inDays > 30) {
          return '${(difference.inDays / 30).floor()}mo ago';
        } else if (difference.inDays > 0) {
          return '${difference.inDays}d ago';
        } else if (difference.inHours > 0) {
          return '${difference.inHours}h ago';
        } else {
          return 'Just now';
        }
      } catch (e) {
        return 'Unknown';
      }
    }
    return 'Unknown';
  }
  
  void _toggleFavorite() {
    setState(() {
      _isFavorited = !_isFavorited;
    });
    widget.onFavorite?.call();
  }
}
