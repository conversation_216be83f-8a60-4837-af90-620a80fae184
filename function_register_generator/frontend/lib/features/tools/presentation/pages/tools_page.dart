import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../widgets/tools_search_bar.dart';
import '../widgets/tools_grid.dart';
import '../widgets/tool_detail_panel.dart';

class ToolsPage extends ConsumerStatefulWidget {
  const ToolsPage({super.key});

  @override
  ConsumerState<ToolsPage> createState() => _ToolsPageState();
}

class _ToolsPageState extends ConsumerState<ToolsPage> {
  String _searchQuery = '';
  List<String> _selectedCategories = [];
  List<String> _selectedTags = [];
  String? _selectedSort;
  ToolsViewMode _viewMode = ToolsViewMode.grid;
  Map<String, dynamic>? _selectedTool;
  bool _isLoading = false;
  bool _hasMore = true;

  // Mock data
  List<Map<String, dynamic>> _tools = [];
  List<Map<String, dynamic>> _filteredTools = [];

  @override
  void initState() {
    super.initState();
    _loadMockData();
    _applyFilters();
  }

  void _loadMockData() {
    _tools = [
      {
        'id': '1',
        'name': 'Weather API',
        'description': 'Get current weather information for any location worldwide',
        'category': 'api',
        'transport': 'http',
        'status': 'active',
        'tags': ['weather', 'api', 'location'],
        'author': 'WeatherCorp',
        'version': '2.1.0',
        'created_at': '2024-01-15T10:00:00Z',
        'updated_at': '2024-01-20T15:30:00Z',
        'usage_count': 1250,
        'rating': 4.8,
        'favorite_count': 89,
        'is_favorited': false,
        'input_schema': {
          'type': 'object',
          'properties': {
            'location': {
              'type': 'string',
              'description': 'City name or coordinates',
            },
            'units': {
              'type': 'string',
              'enum': ['metric', 'imperial'],
              'description': 'Temperature units',
              'default': 'metric',
            },
          },
          'required': ['location'],
        },
      },
      {
        'id': '2',
        'name': 'Text Summarizer',
        'description': 'AI-powered text summarization tool for long documents',
        'category': 'ai',
        'transport': 'python',
        'status': 'active',
        'tags': ['ai', 'nlp', 'summarization'],
        'author': 'AI Labs',
        'version': '1.5.2',
        'created_at': '2024-01-10T08:00:00Z',
        'updated_at': '2024-01-18T12:00:00Z',
        'usage_count': 890,
        'rating': 4.6,
        'favorite_count': 67,
        'is_favorited': true,
        'input_schema': {
          'type': 'object',
          'properties': {
            'text': {
              'type': 'string',
              'description': 'Text to summarize',
            },
            'max_length': {
              'type': 'integer',
              'description': 'Maximum summary length',
              'default': 150,
            },
          },
          'required': ['text'],
        },
      },
      {
        'id': '3',
        'name': 'Database Query Builder',
        'description': 'Visual query builder for SQL databases with syntax validation',
        'category': 'data',
        'transport': 'http',
        'status': 'active',
        'tags': ['database', 'sql', 'query'],
        'author': 'DataTools Inc',
        'version': '3.0.1',
        'created_at': '2024-01-05T14:00:00Z',
        'updated_at': '2024-01-22T09:15:00Z',
        'usage_count': 2100,
        'rating': 4.9,
        'favorite_count': 156,
        'is_favorited': false,
        'input_schema': {
          'type': 'object',
          'properties': {
            'table': {
              'type': 'string',
              'description': 'Table name',
            },
            'columns': {
              'type': 'array',
              'items': {'type': 'string'},
              'description': 'Columns to select',
            },
            'where': {
              'type': 'string',
              'description': 'WHERE clause conditions',
            },
          },
          'required': ['table'],
        },
      },
      {
        'id': '4',
        'name': 'File Converter',
        'description': 'Convert files between different formats (PDF, DOCX, HTML, etc.)',
        'category': 'utility',
        'transport': 'python',
        'status': 'active',
        'tags': ['converter', 'files', 'pdf'],
        'author': 'ConvertPro',
        'version': '1.8.0',
        'created_at': '2024-01-12T11:30:00Z',
        'updated_at': '2024-01-19T16:45:00Z',
        'usage_count': 567,
        'rating': 4.4,
        'favorite_count': 34,
        'is_favorited': false,
        'input_schema': {
          'type': 'object',
          'properties': {
            'input_file': {
              'type': 'string',
              'description': 'Input file path or URL',
            },
            'output_format': {
              'type': 'string',
              'enum': ['pdf', 'docx', 'html', 'txt'],
              'description': 'Target format',
            },
          },
          'required': ['input_file', 'output_format'],
        },
      },
      {
        'id': '5',
        'name': 'Email Validator',
        'description': 'Validate email addresses and check domain reputation',
        'category': 'utility',
        'transport': 'http',
        'status': 'deprecated',
        'tags': ['email', 'validation', 'security'],
        'author': 'ValidateIt',
        'version': '2.3.1',
        'created_at': '2023-12-20T09:00:00Z',
        'updated_at': '2024-01-08T13:20:00Z',
        'usage_count': 345,
        'rating': 4.2,
        'favorite_count': 23,
        'is_favorited': false,
        'input_schema': {
          'type': 'object',
          'properties': {
            'email': {
              'type': 'string',
              'description': 'Email address to validate',
            },
            'check_domain': {
              'type': 'boolean',
              'description': 'Check domain reputation',
              'default': true,
            },
          },
          'required': ['email'],
        },
      },
      {
        'id': '6',
        'name': 'Code Formatter',
        'description': 'Format and beautify code in multiple programming languages',
        'category': 'development',
        'transport': 'python',
        'status': 'active',
        'tags': ['code', 'formatter', 'development'],
        'author': 'DevTools',
        'version': '1.2.3',
        'created_at': '2024-01-08T16:00:00Z',
        'updated_at': '2024-01-21T10:30:00Z',
        'usage_count': 789,
        'rating': 4.7,
        'favorite_count': 45,
        'is_favorited': true,
        'input_schema': {
          'type': 'object',
          'properties': {
            'code': {
              'type': 'string',
              'description': 'Code to format',
            },
            'language': {
              'type': 'string',
              'enum': ['javascript', 'python', 'java', 'cpp'],
              'description': 'Programming language',
            },
          },
          'required': ['code', 'language'],
        },
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Main content
          Expanded(
            child: Column(
              children: [
                // Header and search
                _buildHeader(),

                // Tools grid
                Expanded(
                  child: _buildToolsContent(),
                ),
              ],
            ),
          ),

          // Detail panel
          if (_selectedTool != null) ...[
            ToolDetailPanel(
              tool: _selectedTool!,
              onClose: () {
                setState(() {
                  _selectedTool = null;
                });
              },
              onTryOut: () => _tryOutTool(_selectedTool!),
              onFavorite: () => _toggleFavorite(_selectedTool!),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: context.responsivePadding,
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(color: AppColors.outline),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and view mode selector
          Row(
            children: [
              Text(
                'Browse Tools',
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              ToolsViewModeSelector(
                selectedMode: _viewMode,
                onModeChanged: (mode) {
                  setState(() {
                    _viewMode = mode;
                  });
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Search bar
          ToolsSearchBar(
            initialQuery: _searchQuery,
            onQueryChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
              _applyFilters();
            },
            onClear: () {
              setState(() {
                _searchQuery = '';
              });
              _applyFilters();
            },
            suggestions: _getSearchSuggestions(),
            isLoading: _isLoading,
          ),

          const SizedBox(height: 16),

          // Stats and filters toggle
          Row(
            children: [
              ToolsStats(
                totalCount: _tools.length,
                filteredCount: _filteredTools.length,
                isLoading: _isLoading,
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () {
                  _showFiltersDialog();
                },
                icon: Icon(
                  Icons.filter_list,
                  size: 16,
                  color: _hasActiveFilters() ? AppColors.primary : null,
                ),
                label: Text(
                  'Filters${_hasActiveFilters() ? ' (${_getActiveFiltersCount()})' : ''}',
                  style: TextStyle(
                    color: _hasActiveFilters() ? AppColors.primary : null,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToolsContent() {
    if (_isLoading && _filteredTools.isEmpty) {
      return ToolsLoadingGrid(viewMode: _viewMode);
    }

    return ToolsGrid(
      tools: _filteredTools,
      isLoading: _isLoading,
      hasMore: _hasMore,
      onLoadMore: _loadMoreTools,
      onToolTap: (tool) {
        setState(() {
          _selectedTool = tool;
        });
      },
      onToolFavorite: _toggleFavorite,
      onToolTryOut: _tryOutTool,
      viewMode: _viewMode,
      emptyMessage: _getEmptyMessage(),
    );
  }

  void _applyFilters() {
    setState(() {
      _filteredTools = _tools.where((tool) {
        // Search query filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          final name = (tool['name'] as String).toLowerCase();
          final description = (tool['description'] as String).toLowerCase();
          final tags = (tool['tags'] as List<String>).join(' ').toLowerCase();

          if (!name.contains(query) &&
              !description.contains(query) &&
              !tags.contains(query)) {
            return false;
          }
        }

        // Category filter
        if (_selectedCategories.isNotEmpty) {
          if (!_selectedCategories.contains(tool['category'])) {
            return false;
          }
        }

        // Tags filter
        if (_selectedTags.isNotEmpty) {
          final toolTags = tool['tags'] as List<String>;
          if (!_selectedTags.any((tag) => toolTags.contains(tag))) {
            return false;
          }
        }

        return true;
      }).toList();

      // Apply sorting
      if (_selectedSort != null) {
        _filteredTools.sort((a, b) {
          switch (_selectedSort!) {
            case 'name':
              return (a['name'] as String).compareTo(b['name'] as String);
            case 'created_at':
              return DateTime.parse(b['created_at']).compareTo(DateTime.parse(a['created_at']));
            case 'updated_at':
              return DateTime.parse(b['updated_at']).compareTo(DateTime.parse(a['updated_at']));
            case 'usage_count':
              return (b['usage_count'] as int).compareTo(a['usage_count'] as int);
            case 'rating':
              return (b['rating'] as double).compareTo(a['rating'] as double);
            default:
              return 0;
          }
        });
      }
    });
  }

  List<String> _getSearchSuggestions() {
    final suggestions = <String>{};

    for (final tool in _tools) {
      suggestions.add(tool['name']);
      suggestions.addAll(tool['tags'] as List<String>);
    }

    return suggestions
        .where((s) => s.toLowerCase().contains(_searchQuery.toLowerCase()))
        .take(5)
        .toList();
  }

  bool _hasActiveFilters() {
    return _selectedCategories.isNotEmpty ||
           _selectedTags.isNotEmpty ||
           _selectedSort != null;
  }

  int _getActiveFiltersCount() {
    int count = 0;
    if (_selectedCategories.isNotEmpty) count++;
    if (_selectedTags.isNotEmpty) count++;
    if (_selectedSort != null) count++;
    return count;
  }

  String? _getEmptyMessage() {
    if (_searchQuery.isNotEmpty || _hasActiveFilters()) {
      return 'No tools match your search criteria';
    }
    return 'No tools available';
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Tools'),
        content: SizedBox(
          width: 400,
          child: ToolsFilterChips(
            selectedCategories: _selectedCategories,
            selectedTags: _selectedTags,
            selectedSort: _selectedSort,
            onCategoriesChanged: (categories) {
              setState(() {
                _selectedCategories = categories;
              });
              _applyFilters();
            },
            onTagsChanged: (tags) {
              setState(() {
                _selectedTags = tags;
              });
              _applyFilters();
            },
            onSortChanged: (sort) {
              setState(() {
                _selectedSort = sort;
              });
              _applyFilters();
            },
            onClearAll: () {
              setState(() {
                _selectedCategories.clear();
                _selectedTags.clear();
                _selectedSort = null;
              });
              _applyFilters();
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _loadMoreTools() {
    // Simulate loading more tools
    setState(() {
      _isLoading = true;
    });

    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isLoading = false;
        _hasMore = false; // No more tools to load in this demo
      });
    });
  }

  void _toggleFavorite(Map<String, dynamic> tool) {
    setState(() {
      tool['is_favorited'] = !(tool['is_favorited'] ?? false);
      if (tool['is_favorited']) {
        tool['favorite_count'] = (tool['favorite_count'] ?? 0) + 1;
      } else {
        tool['favorite_count'] = (tool['favorite_count'] ?? 1) - 1;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          tool['is_favorited']
              ? 'Added to favorites'
              : 'Removed from favorites',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _tryOutTool(Map<String, dynamic> tool) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${tool['name']} in test environment...'),
        duration: const Duration(seconds: 2),
      ),
    );

    // TODO: Navigate to tool test page or open test modal
  }
}