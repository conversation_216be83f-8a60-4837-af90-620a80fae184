import 'package:flutter/material.dart';

import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/responsive/responsive_layout.dart';

class ToolDetailPage extends StatelessWidget {
  final String toolId;
  
  const ToolDetailPage({
    super.key,
    required this.toolId,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: context.responsivePadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tool Details',
              style: AppTextStyles.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text('Tool ID: $toolId'),
            const SizedBox(height: 16),
            const Expanded(
              child: Center(
                child: Text('Tool detail page - Coming soon'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}