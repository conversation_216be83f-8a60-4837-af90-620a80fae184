import 'package:flutter/foundation.dart';

class AppConfig {
  static late AppConfig _instance;
  static AppConfig get instance => _instance;
  
  final String apiBaseUrl;
  final String appName;
  final String version;
  final bool enableLogging;
  final bool enableAnalytics;
  
  AppConfig._({
    required this.apiBaseUrl,
    required this.appName,
    required this.version,
    required this.enableLogging,
    required this.enableAnalytics,
  });
  
  static Future<void> initialize() async {
    _instance = AppConfig._(
      apiBaseUrl: _getApiBaseUrl(),
      appName: 'Tool Registration Platform',
      version: '1.0.0',
      enableLogging: kDebugMode,
      enableAnalytics: !kDebugMode,
    );
  }
  
  static String _getApiBaseUrl() {
    if (kDebugMode) {
      return 'http://localhost:8000';
    } else {
      return 'https://api.toolregistry.com';
    }
  }
  
  // Environment-specific configurations
  bool get isDevelopment => kDebugMode;
  bool get isProduction => !kDebugMode;
  
  // API endpoints
  String get toolsEndpoint => '$apiBaseUrl/api/tools';
  String get searchEndpoint => '$apiBaseUrl/api/tools/search';
  String get sandboxEndpoint => '$apiBaseUrl/api/sandbox';
}