import 'package:flutter/material.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= BreakPoints.desktop) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= BreakPoints.tablet) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}

class BreakPoints {
  static const double mobile = 0;
  static const double tablet = 768;
  static const double desktop = 1024;
  static const double largeDesktop = 1440;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < tablet;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= tablet && width < desktop;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
  
  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= largeDesktop;
  }
  
  static double getMaxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= largeDesktop) {
      return 1200; // Max content width for large screens
    } else if (screenWidth >= desktop) {
      return screenWidth * 0.9;
    } else if (screenWidth >= tablet) {
      return screenWidth * 0.95;
    } else {
      return screenWidth;
    }
  }
  
  static EdgeInsets getPadding(BuildContext context) {
    if (isDesktop(context)) {
      return const EdgeInsets.symmetric(horizontal: 32, vertical: 24);
    } else if (isTablet(context)) {
      return const EdgeInsets.symmetric(horizontal: 24, vertical: 20);
    } else {
      return const EdgeInsets.symmetric(horizontal: 16, vertical: 16);
    }
  }
  
  static double getSpacing(BuildContext context) {
    if (isDesktop(context)) {
      return 24;
    } else if (isTablet(context)) {
      return 20;
    } else {
      return 16;
    }
  }
  
  static int getGridColumns(BuildContext context) {
    if (isLargeDesktop(context)) {
      return 4;
    } else if (isDesktop(context)) {
      return 3;
    } else if (isTablet(context)) {
      return 2;
    } else {
      return 1;
    }
  }
}

class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints) builder;
  
  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: builder);
  }
}

class ResponsiveValue<T> {
  final T mobile;
  final T? tablet;
  final T? desktop;
  final T? largeDesktop;
  
  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });
  
  T getValue(BuildContext context) {
    if (BreakPoints.isLargeDesktop(context) && largeDesktop != null) {
      return largeDesktop!;
    } else if (BreakPoints.isDesktop(context) && desktop != null) {
      return desktop!;
    } else if (BreakPoints.isTablet(context) && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}

extension ResponsiveExtension on BuildContext {
  bool get isMobile => BreakPoints.isMobile(this);
  bool get isTablet => BreakPoints.isTablet(this);
  bool get isDesktop => BreakPoints.isDesktop(this);
  bool get isLargeDesktop => BreakPoints.isLargeDesktop(this);
  
  double get maxWidth => BreakPoints.getMaxWidth(this);
  EdgeInsets get responsivePadding => BreakPoints.getPadding(this);
  double get responsiveSpacing => BreakPoints.getSpacing(this);
  int get gridColumns => BreakPoints.getGridColumns(this);
}