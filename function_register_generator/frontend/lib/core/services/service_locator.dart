import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../network/dio_client.dart';

class ServiceLocator {
  static bool _initialized = false;
  
  static Future<void> setup() async {
    if (_initialized) return;
    
    try {
      // Initialize services here
      if (kDebugMode) {
        print('ServiceLocator: Initializing services...');
      }
      
      // Initialize HTTP client
      DioClient.initialize();
      
      // TODO: Initialize repositories, caches, etc.
      
      _initialized = true;
      
      if (kDebugMode) {
        print('ServiceLocator: All services initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('ServiceLocator: Failed to initialize services: $e');
      }
      rethrow;
    }
  }
  
  static void dispose() {
    if (!_initialized) return;
    
    try {
      // Dispose services here
      if (kDebugMode) {
        print('ServiceLocator: Disposing services...');
      }
      
      // Dispose HTTP client
      DioClient.dispose();
      
      // TODO: Dispose repositories, close connections, etc.
      
      _initialized = false;
      
      if (kDebugMode) {
        print('ServiceLocator: All services disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('ServiceLocator: Failed to dispose services: $e');
      }
    }
  }
}

// Providers
final dioProvider = Provider((ref) => DioClient.instance);