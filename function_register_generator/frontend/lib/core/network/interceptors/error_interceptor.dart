import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../exceptions/app_exception.dart';

class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppException appException;
    
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        appException = NetworkException(
          message: 'Connection timeout. Please check your internet connection.',
          statusCode: null,
        );
        break;
        
      case DioExceptionType.connectionError:
        appException = NetworkException(
          message: 'Unable to connect to server. Please check your internet connection.',
          statusCode: null,
        );
        break;
        
      case DioExceptionType.badResponse:
        appException = _handleResponseError(err);
        break;
        
      case DioExceptionType.cancel:
        appException = NetworkException(
          message: 'Request was cancelled.',
          statusCode: null,
        );
        break;
        
      case DioExceptionType.unknown:
      default:
        appException = NetworkException(
          message: 'An unexpected error occurred. Please try again.',
          statusCode: null,
        );
        break;
    }
    
    // Log error in debug mode
    if (kDebugMode) {
      print('API Error: ${appException.message}');
      print('Status Code: ${appException.statusCode}');
      print('Request: ${err.requestOptions.method} ${err.requestOptions.path}');
      if (err.response?.data != null) {
        print('Response: ${err.response?.data}');
      }
    }
    
    // Replace the original error with our custom exception
    final customError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: appException,
      message: appException.message,
    );
    
    handler.next(customError);
  }
  
  AppException _handleResponseError(DioException err) {
    final statusCode = err.response?.statusCode;
    final data = err.response?.data;
    
    String message = 'An error occurred while processing your request.';
    
    // Try to extract error message from response
    if (data is Map<String, dynamic>) {
      message = data['message'] ?? 
                data['error'] ?? 
                data['detail'] ?? 
                message;
    } else if (data is String) {
      message = data;
    }
    
    switch (statusCode) {
      case 400:
        return ValidationException(
          message: message.isNotEmpty ? message : 'Invalid request data.',
          statusCode: statusCode,
          errors: _extractValidationErrors(data),
        );
        
      case 401:
        return AuthenticationException(
          message: message.isNotEmpty ? message : 'Authentication required.',
          statusCode: statusCode,
        );
        
      case 403:
        return AuthorizationException(
          message: message.isNotEmpty ? message : 'Access denied.',
          statusCode: statusCode,
        );
        
      case 404:
        return NotFoundException(
          message: message.isNotEmpty ? message : 'Resource not found.',
          statusCode: statusCode,
        );
        
      case 409:
        return ConflictException(
          message: message.isNotEmpty ? message : 'Resource conflict.',
          statusCode: statusCode,
        );
        
      case 422:
        return ValidationException(
          message: message.isNotEmpty ? message : 'Validation failed.',
          statusCode: statusCode,
          errors: _extractValidationErrors(data),
        );
        
      case 429:
        return RateLimitException(
          message: message.isNotEmpty ? message : 'Too many requests. Please try again later.',
          statusCode: statusCode,
        );
        
      case 500:
      case 502:
      case 503:
      case 504:
        return ServerException(
          message: 'Server error. Please try again later.',
          statusCode: statusCode,
        );
        
      default:
        return NetworkException(
          message: message,
          statusCode: statusCode,
        );
    }
  }
  
  Map<String, List<String>>? _extractValidationErrors(dynamic data) {
    if (data is! Map<String, dynamic>) return null;
    
    final errors = <String, List<String>>{};
    
    // Handle different validation error formats
    if (data.containsKey('errors')) {
      final errorData = data['errors'];
      if (errorData is Map<String, dynamic>) {
        errorData.forEach((field, messages) {
          if (messages is List) {
            errors[field] = messages.map((e) => e.toString()).toList();
          } else if (messages is String) {
            errors[field] = [messages];
          }
        });
      }
    }
    
    // Handle FastAPI validation errors
    if (data.containsKey('detail') && data['detail'] is List) {
      final details = data['detail'] as List;
      for (final detail in details) {
        if (detail is Map<String, dynamic>) {
          final loc = detail['loc'] as List?;
          final msg = detail['msg'] as String?;
          
          if (loc != null && loc.isNotEmpty && msg != null) {
            final field = loc.last.toString();
            errors[field] = errors[field] ?? [];
            errors[field]!.add(msg);
          }
        }
      }
    }
    
    return errors.isNotEmpty ? errors : null;
  }
}