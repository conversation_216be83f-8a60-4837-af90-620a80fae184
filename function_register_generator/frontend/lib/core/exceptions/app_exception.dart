abstract class AppException implements Exception {
  final String message;
  final int? statusCode;
  
  const AppException({
    required this.message,
    this.statusCode,
  });
  
  @override
  String toString() => message;
}

class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.statusCode,
  });
}

class ValidationException extends AppException {
  final Map<String, List<String>>? errors;
  
  const ValidationException({
    required super.message,
    super.statusCode,
    this.errors,
  });
  
  bool hasFieldError(String field) {
    return errors?.containsKey(field) ?? false;
  }
  
  List<String> getFieldErrors(String field) {
    return errors?[field] ?? [];
  }
  
  String getFirstFieldError(String field) {
    final fieldErrors = getFieldErrors(field);
    return fieldErrors.isNotEmpty ? fieldErrors.first : '';
  }
}

class AuthenticationException extends AppException {
  const AuthenticationException({
    required super.message,
    super.statusCode,
  });
}

class AuthorizationException extends AppException {
  const AuthorizationException({
    required super.message,
    super.statusCode,
  });
}

class NotFoundException extends AppException {
  const NotFoundException({
    required super.message,
    super.statusCode,
  });
}

class ConflictException extends AppException {
  const ConflictException({
    required super.message,
    super.statusCode,
  });
}

class RateLimitException extends AppException {
  const RateLimitException({
    required super.message,
    super.statusCode,
  });
}

class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.statusCode,
  });
}

class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.statusCode,
  });
}

class ParseException extends AppException {
  const ParseException({
    required super.message,
    super.statusCode,
  });
}