import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Theme mode provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  return ThemeModeNotifier();
});

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  static const String _themeModeKey = 'theme_mode';
  
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }
  
  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeString = prefs.getString(_themeModeKey);
      
      if (themeModeString != null) {
        state = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == themeModeString,
          orElse: () => ThemeMode.system,
        );
      }
    } catch (e) {
      // If loading fails, keep system default
      state = ThemeMode.system;
    }
  }
  
  Future<void> setThemeMode(ThemeMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeModeKey, mode.toString());
      state = mode;
    } catch (e) {
      // If saving fails, still update the state
      state = mode;
    }
  }
  
  void toggleTheme() {
    switch (state) {
      case ThemeMode.light:
        setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        setThemeMode(ThemeMode.light);
        break;
      case ThemeMode.system:
        setThemeMode(ThemeMode.light);
        break;
    }
  }
}

// Current brightness provider (for detecting system theme)
final currentBrightnessProvider = Provider<Brightness>((ref) {
  return WidgetsBinding.instance.platformDispatcher.platformBrightness;
});

// Effective theme mode provider (resolves system theme)
final effectiveThemeModeProvider = Provider<ThemeMode>((ref) {
  final themeMode = ref.watch(themeModeProvider);
  final brightness = ref.watch(currentBrightnessProvider);
  
  if (themeMode == ThemeMode.system) {
    return brightness == Brightness.dark ? ThemeMode.dark : ThemeMode.light;
  }
  
  return themeMode;
});

// Is dark mode provider
final isDarkModeProvider = Provider<bool>((ref) {
  final effectiveTheme = ref.watch(effectiveThemeModeProvider);
  return effectiveTheme == ThemeMode.dark;
});