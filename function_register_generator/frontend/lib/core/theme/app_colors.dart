import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF2563EB);
  static const Color primaryVariant = Color(0xFF1D4ED8);
  static const Color onPrimary = Color(0xFFFFFFFF);
  
  // Secondary colors
  static const Color secondary = Color(0xFF10B981);
  static const Color secondaryVariant = Color(0xFF059669);
  static const Color onSecondary = Color(0xFFFFFFFF);
  
  // Surface colors (Light theme)
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF3F4F6);
  static const Color onSurface = Color(0xFF111827);
  static const Color onSurfaceVariant = Color(0xFF6B7280);
  
  // Surface colors (Dark theme)
  static const Color backgroundDark = Color(0xFF0F172A);
  static const Color surfaceDark = Color(0xFF1E293B);
  static const Color surfaceVariantDark = Color(0xFF334155);
  static const Color onSurfaceDark = Color(0xFFF1F5F9);
  static const Color onSurfaceVariantDark = Color(0xFF94A3B8);
  
  // Status colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);
  
  // Neutral colors
  static const Color outline = Color(0xFFE5E7EB);
  static const Color outlineVariant = Color(0xFFD1D5DB);
  static const Color shadow = Color(0x1A000000);
  
  // Semantic colors
  static const Color link = Color(0xFF2563EB);
  static const Color linkHover = Color(0xFF1D4ED8);
  
  // Category colors
  static const Color categoryApi = Color(0xFF3B82F6);
  static const Color categoryData = Color(0xFF10B981);
  static const Color categoryFile = Color(0xFF8B5CF6);
  static const Color categoryUtility = Color(0xFF6B7280);
  static const Color categoryWeather = Color(0xFF06B6D4);
  static const Color categorySecurity = Color(0xFFEF4444);
  static const Color categoryDefault = Color(0xFF6B7280);
  
  // Transport colors
  static const Color transportHttp = Color(0xFF10B981);
  static const Color transportPython = Color(0xFF3776AB);
  static const Color transportStdio = Color(0xFF8B5CF6);
  
  // Primary swatch for Material theme
  static const MaterialColor primarySwatch = MaterialColor(
    0xFF2563EB,
    <int, Color>{
      50: Color(0xFFEFF6FF),
      100: Color(0xFFDBEAFE),
      200: Color(0xFFBFDBFE),
      300: Color(0xFF93C5FD),
      400: Color(0xFF60A5FA),
      500: Color(0xFF3B82F6),
      600: Color(0xFF2563EB),
      700: Color(0xFF1D4ED8),
      800: Color(0xFF1E40AF),
      900: Color(0xFF1E3A8A),
    },
  );
  
  // Gradient colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryVariant],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, Color(0xFF059669)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, Color(0xFFD97706)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, Color(0xFFDC2626)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Helper methods
  static Color getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'api':
        return categoryApi;
      case 'data':
        return categoryData;
      case 'file':
        return categoryFile;
      case 'utility':
        return categoryUtility;
      case 'weather':
        return categoryWeather;
      case 'security':
        return categorySecurity;
      default:
        return categoryDefault;
    }
  }
  
  static Color getTransportColor(String transport) {
    switch (transport.toLowerCase()) {
      case 'http':
        return transportHttp;
      case 'python':
        return transportPython;
      case 'stdio':
        return transportStdio;
      default:
        return categoryDefault;
    }
  }
  
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'active':
        return success;
      case 'warning':
      case 'pending':
        return warning;
      case 'error':
      case 'failed':
        return error;
      case 'info':
      case 'draft':
        return info;
      default:
        return onSurfaceVariant;
    }
  }
}