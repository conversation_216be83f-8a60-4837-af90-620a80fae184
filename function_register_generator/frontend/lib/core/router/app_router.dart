import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/tools/presentation/pages/tools_page.dart';
import '../../features/tools/presentation/pages/tool_detail_page.dart';
import '../../features/registration/presentation/pages/registration_page.dart';
import '../../features/search/presentation/pages/search_page.dart';
import '../../features/drafts/presentation/pages/drafts_page.dart';
import '../../shared/presentation/pages/not_found_page.dart';
import '../../shared/presentation/widgets/app_shell.dart';
import '../../shared/presentation/animations/page_transitions.dart';

final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    
    routes: [
      ShellRoute(
        builder: (context, state, child) {
          return AppShell(child: child);
        },
        routes: [
          // Home route
          GoRoute(
            path: '/',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          
          // Tools routes
          GoRoute(
            path: '/tools',
            name: 'tools',
            builder: (context, state) => const ToolsPage(),
            routes: [
              GoRoute(
                path: '/:toolId',
                name: 'tool-detail',
                builder: (context, state) {
                  final toolId = state.pathParameters['toolId']!;
                  return ToolDetailPage(toolId: toolId);
                },
              ),
            ],
          ),
          
          // Registration routes
          GoRoute(
            path: '/register',
            name: 'register',
            builder: (context, state) => const RegistrationPage(),
            routes: [
              GoRoute(
                path: '/curl',
                name: 'register-curl',
                builder: (context, state) => const RegistrationPage(
                  initialType: RegistrationType.curl,
                ),
              ),
              GoRoute(
                path: '/python',
                name: 'register-python',
                builder: (context, state) => const RegistrationPage(
                  initialType: RegistrationType.python,
                ),
              ),
              GoRoute(
                path: '/mcp',
                name: 'register-mcp',
                builder: (context, state) => const RegistrationPage(
                  initialType: RegistrationType.mcp,
                ),
              ),
              GoRoute(
                path: '/natural',
                name: 'register-natural',
                builder: (context, state) => const RegistrationPage(
                  initialType: RegistrationType.natural,
                ),
              ),
            ],
          ),
          
          // Search route
          GoRoute(
            path: '/search',
            name: 'search',
            builder: (context, state) {
              final query = state.uri.queryParameters['q'];
              return SearchPage(initialQuery: query);
            },
          ),
          
          // Drafts routes
          GoRoute(
            path: '/drafts',
            name: 'drafts',
            builder: (context, state) => const DraftsPage(),
            routes: [
              GoRoute(
                path: '/:draftId/edit',
                name: 'draft-edit',
                builder: (context, state) {
                  final draftId = state.pathParameters['draftId']!;
                  return DraftEditPage(draftId: draftId);
                },
              ),
            ],
          ),
        ],
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => NotFoundPage(
      error: state.error?.toString(),
    ),
    
    // Redirect logic
    redirect: (context, state) {
      // Add any global redirect logic here
      return null;
    },
  );
});

// Route names for easy navigation
class AppRoutes {
  static const String home = '/';
  static const String tools = '/tools';
  static const String toolDetail = '/tools/:toolId';
  static const String register = '/register';
  static const String registerCurl = '/register/curl';
  static const String registerPython = '/register/python';
  static const String registerMcp = '/register/mcp';
  static const String registerNatural = '/register/natural';
  static const String search = '/search';
  static const String drafts = '/drafts';
  static const String draftEdit = '/drafts/:draftId/edit';
}

// Navigation helper
class AppNavigation {
  static void goHome(BuildContext context) {
    context.goNamed('home');
  }
  
  static void goToTools(BuildContext context) {
    context.goNamed('tools');
  }
  
  static void goToToolDetail(BuildContext context, String toolId) {
    context.goNamed('tool-detail', pathParameters: {'toolId': toolId});
  }
  
  static void goToRegister(BuildContext context, {RegistrationType? type}) {
    switch (type) {
      case RegistrationType.curl:
        context.goNamed('register-curl');
        break;
      case RegistrationType.python:
        context.goNamed('register-python');
        break;
      case RegistrationType.mcp:
        context.goNamed('register-mcp');
        break;
      case RegistrationType.natural:
        context.goNamed('register-natural');
        break;
      default:
        context.goNamed('register');
    }
  }
  
  static void goToSearch(BuildContext context, {String? query}) {
    if (query != null) {
      context.goNamed('search', queryParameters: {'q': query});
    } else {
      context.goNamed('search');
    }
  }
  
  static void goToDrafts(BuildContext context) {
    context.goNamed('drafts');
  }
  
  static void goToDraftEdit(BuildContext context, String draftId) {
    context.goNamed('draft-edit', pathParameters: {'draftId': draftId});
  }
}

enum RegistrationType {
  basic,
  curl,
  python,
  mcp,
  natural,
}