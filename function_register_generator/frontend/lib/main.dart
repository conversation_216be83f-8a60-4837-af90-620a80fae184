import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_web_plugins/url_strategy.dart';

import 'core/app.dart';
import 'core/config/app_config.dart';
import 'core/services/service_locator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Remove # from URL
  usePathUrlStrategy();
  
  // Initialize app configuration
  await AppConfig.initialize();
  
  // Setup service locator
  await ServiceLocator.setup();
  
  runApp(
    ProviderScope(
      child: ToolRegistrationApp(),
    ),
  );
}