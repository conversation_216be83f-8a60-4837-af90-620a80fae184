import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/theme/app_colors.dart';

class LoadingAnimations {
  // Pulsing dots animation
  static Widget pulsingDots({
    Color color = AppColors.primary,
    double size = 8.0,
    Duration duration = const Duration(milliseconds: 1200),
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return Container(
          width: size,
          height: size,
          margin: EdgeInsets.symmetric(horizontal: size * 0.25),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        )
            .animate(
              onPlay: (controller) => controller.repeat(),
            )
            .scale(
              begin: const Offset(0.5, 0.5),
              end: const Offset(1.2, 1.2),
              duration: duration,
              delay: Duration(milliseconds: index * 200),
            )
            .then()
            .scale(
              begin: const Offset(1.2, 1.2),
              end: const Offset(0.5, 0.5),
              duration: duration,
            );
      }),
    );
  }
  
  // Spinning circle animation
  static Widget spinningCircle({
    Color color = AppColors.primary,
    double size = 24.0,
    double strokeWidth = 3.0,
    Duration duration = const Duration(milliseconds: 1000),
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(color),
      )
          .animate(
            onPlay: (controller) => controller.repeat(),
          )
          .rotate(
            duration: duration,
          ),
    );
  }
  
  // Wave animation
  static Widget wave({
    Color color = AppColors.primary,
    double width = 100.0,
    double height = 4.0,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return SizedBox(
      width: width,
      height: height,
      child: Row(
        children: List.generate(5, (index) {
          return Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 1),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(height / 2),
              ),
            )
                .animate(
                  onPlay: (controller) => controller.repeat(),
                )
                .scaleY(
                  begin: 0.3,
                  end: 1.0,
                  duration: duration,
                  delay: Duration(milliseconds: index * 100),
                )
                .then()
                .scaleY(
                  begin: 1.0,
                  end: 0.3,
                  duration: duration,
                ),
          );
        }),
      ),
    );
  }
  
  // Bouncing ball animation
  static Widget bouncingBall({
    Color color = AppColors.primary,
    double size = 16.0,
    Duration duration = const Duration(milliseconds: 800),
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    )
        .animate(
          onPlay: (controller) => controller.repeat(),
        )
        .moveY(
          begin: 0,
          end: -size,
          duration: duration,
          curve: Curves.easeOut,
        )
        .then()
        .moveY(
          begin: -size,
          end: 0,
          duration: duration,
          curve: Curves.easeIn,
        );
  }
  
  // Typing animation
  static Widget typing({
    Color color = AppColors.primary,
    double size = 6.0,
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return Container(
          width: size,
          height: size,
          margin: EdgeInsets.symmetric(horizontal: size * 0.3),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        )
            .animate(
              onPlay: (controller) => controller.repeat(),
            )
            .fadeIn(
              duration: duration,
              delay: Duration(milliseconds: index * 200),
            )
            .then()
            .fadeOut(
              duration: duration,
            );
      }),
    );
  }
  
  // Shimmer effect
  static Widget shimmer({
    required Widget child,
    Color baseColor = const Color(0xFFE0E0E0),
    Color highlightColor = const Color(0xFFF5F5F5),
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return child
        .animate(
          onPlay: (controller) => controller.repeat(),
        )
        .shimmer(
          duration: duration,
          color: highlightColor,
        );
  }
  
  // Progress bar animation
  static Widget progressBar({
    double width = 200.0,
    double height = 4.0,
    Color backgroundColor = const Color(0xFFE0E0E0),
    Color progressColor = AppColors.primary,
    double progress = 0.0,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Container(
          width: width * progress,
          height: height,
          decoration: BoxDecoration(
            color: progressColor,
            borderRadius: BorderRadius.circular(height / 2),
          ),
        )
            .animate()
            .scaleX(
              begin: 0.0,
              duration: duration,
              curve: Curves.easeOut,
            ),
      ),
    );
  }
  
  // Fade in animation
  static Widget fadeIn({
    required Widget child,
    Duration duration = const Duration(milliseconds: 300),
    Duration delay = Duration.zero,
    Curve curve = Curves.easeIn,
  }) {
    return child
        .animate()
        .fadeIn(
          duration: duration,
          delay: delay,
          curve: curve,
        );
  }
  
  // Slide in animation
  static Widget slideIn({
    required Widget child,
    Offset begin = const Offset(0.0, 1.0),
    Duration duration = const Duration(milliseconds: 300),
    Duration delay = Duration.zero,
    Curve curve = Curves.easeOut,
  }) {
    return child
        .animate()
        .slideX(
          begin: begin.dx,
          duration: duration,
          delay: delay,
          curve: curve,
        )
        .slideY(
          begin: begin.dy,
          duration: duration,
          delay: delay,
          curve: curve,
        );
  }
  
  // Scale in animation
  static Widget scaleIn({
    required Widget child,
    double begin = 0.0,
    Duration duration = const Duration(milliseconds: 300),
    Duration delay = Duration.zero,
    Curve curve = Curves.elasticOut,
  }) {
    return child
        .animate()
        .scale(
          begin: Offset(begin, begin),
          duration: duration,
          delay: delay,
          curve: curve,
        );
  }
  
  // Staggered list animation
  static List<Widget> staggeredList({
    required List<Widget> children,
    Duration staggerDelay = const Duration(milliseconds: 100),
    Duration itemDuration = const Duration(milliseconds: 300),
    Offset slideBegin = const Offset(0.0, 0.3),
  }) {
    return children.asMap().entries.map((entry) {
      final index = entry.key;
      final child = entry.value;
      
      return child
          .animate()
          .fadeIn(
            duration: itemDuration,
            delay: staggerDelay * index,
          )
          .slideY(
            begin: slideBegin.dy,
            duration: itemDuration,
            delay: staggerDelay * index,
            curve: Curves.easeOut,
          );
    }).toList();
  }
}

// Loading state widget with different animations
class AnimatedLoadingState extends StatelessWidget {
  final LoadingAnimationType type;
  final String? message;
  final Color? color;
  final double? size;
  
  const AnimatedLoadingState({
    super.key,
    this.type = LoadingAnimationType.pulsingDots,
    this.message,
    this.color,
    this.size,
  });
  
  @override
  Widget build(BuildContext context) {
    Widget animation;
    
    switch (type) {
      case LoadingAnimationType.pulsingDots:
        animation = LoadingAnimations.pulsingDots(
          color: color ?? AppColors.primary,
          size: size ?? 8.0,
        );
        break;
      case LoadingAnimationType.spinningCircle:
        animation = LoadingAnimations.spinningCircle(
          color: color ?? AppColors.primary,
          size: size ?? 24.0,
        );
        break;
      case LoadingAnimationType.wave:
        animation = LoadingAnimations.wave(
          color: color ?? AppColors.primary,
          width: size ?? 100.0,
        );
        break;
      case LoadingAnimationType.bouncingBall:
        animation = LoadingAnimations.bouncingBall(
          color: color ?? AppColors.primary,
          size: size ?? 16.0,
        );
        break;
      case LoadingAnimationType.typing:
        animation = LoadingAnimations.typing(
          color: color ?? AppColors.primary,
          size: size ?? 6.0,
        );
        break;
    }
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        animation,
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color ?? AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }
}

enum LoadingAnimationType {
  pulsingDots,
  spinningCircle,
  wave,
  bouncingBall,
  typing,
}
