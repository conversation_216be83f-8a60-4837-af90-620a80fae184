import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/theme/app_colors.dart';

class SuccessAnimations {
  // Checkmark animation
  static Widget checkmark({
    Color color = AppColors.success,
    double size = 48.0,
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: CheckmarkPainter(color: color),
      ),
    )
        .animate()
        .scale(
          begin: const Offset(0.0, 0.0),
          end: const Offset(1.2, 1.2),
          duration: duration * 0.6,
          curve: Curves.elasticOut,
        )
        .then()
        .scale(
          begin: const Offset(1.2, 1.2),
          end: const Offset(1.0, 1.0),
          duration: duration * 0.4,
          curve: Curves.easeOut,
        );
  }
  
  // Success badge with checkmark
  static Widget successBadge({
    Color backgroundColor = AppColors.success,
    Color checkColor = Colors.white,
    double size = 64.0,
    Duration duration = const Duration(milliseconds: 800),
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Icon(
        Icons.check,
        color: checkColor,
        size: size * 0.5,
      ),
    )
        .animate()
        .scale(
          begin: const Offset(0.0, 0.0),
          duration: duration * 0.7,
          curve: Curves.elasticOut,
        )
        .then()
        .shimmer(
          duration: duration * 0.3,
          color: Colors.white.withOpacity(0.5),
        );
  }
  
  // Confetti animation
  static Widget confetti({
    double width = 200.0,
    double height = 200.0,
    Duration duration = const Duration(milliseconds: 2000),
  }) {
    final colors = [
      AppColors.primary,
      AppColors.secondary,
      AppColors.success,
      Colors.orange,
      Colors.pink,
      Colors.purple,
    ];
    
    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        children: List.generate(20, (index) {
          final color = colors[index % colors.length];
          final left = (index % 5) * (width / 5);
          final delay = Duration(milliseconds: index * 100);
          
          return Positioned(
            left: left,
            top: 0,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            )
                .animate()
                .moveY(
                  begin: -20,
                  end: height + 20,
                  duration: duration,
                  delay: delay,
                  curve: Curves.easeIn,
                )
                .rotate(
                  begin: 0,
                  end: 4,
                  duration: duration,
                  delay: delay,
                )
                .fadeOut(
                  duration: duration * 0.3,
                  delay: delay + duration * 0.7,
                ),
          );
        }),
      ),
    );
  }
  
  // Ripple effect
  static Widget ripple({
    Color color = AppColors.success,
    double size = 100.0,
    Duration duration = const Duration(milliseconds: 1000),
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: List.generate(3, (index) {
          final delay = Duration(milliseconds: index * 300);
          
          return Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 2,
              ),
            ),
          )
              .animate()
              .scale(
                begin: const Offset(0.0, 0.0),
                end: const Offset(1.0, 1.0),
                duration: duration,
                delay: delay,
                curve: Curves.easeOut,
              )
              .fadeOut(
                duration: duration,
                delay: delay,
              );
        }),
      ),
    );
  }
  
  // Success message with slide in
  static Widget successMessage({
    required String message,
    TextStyle? textStyle,
    Duration duration = const Duration(milliseconds: 500),
  }) {
    return Text(
      message,
      style: textStyle,
      textAlign: TextAlign.center,
    )
        .animate()
        .slideY(
          begin: 0.5,
          duration: duration,
          curve: Curves.easeOut,
        )
        .fadeIn(
          duration: duration,
        );
  }
  
  // Celebration animation combining multiple effects
  static Widget celebration({
    required String title,
    String? subtitle,
    Color primaryColor = AppColors.success,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Confetti background
        confetti(
          duration: duration,
        ),
        
        const SizedBox(height: 20),
        
        // Success badge
        successBadge(
          backgroundColor: primaryColor,
          duration: duration * 0.6,
        ),
        
        const SizedBox(height: 24),
        
        // Title
        successMessage(
          message: title,
          textStyle: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
          duration: duration * 0.4,
        ),
        
        if (subtitle != null) ...[
          const SizedBox(height: 8),
          successMessage(
            message: subtitle,
            textStyle: const TextStyle(
              fontSize: 16,
              color: AppColors.onSurfaceVariant,
            ),
            duration: duration * 0.4,
          ),
        ],
        
        const SizedBox(height: 32),
        
        // Ripple effect
        ripple(
          color: primaryColor,
          duration: duration,
        ),
      ],
    );
  }
  
  // Progress completion animation
  static Widget progressComplete({
    double progress = 1.0,
    Color backgroundColor = const Color(0xFFE0E0E0),
    Color progressColor = AppColors.success,
    double width = 200.0,
    double height = 8.0,
    Duration duration = const Duration(milliseconds: 800),
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Container(
          height: height,
          decoration: BoxDecoration(
            color: progressColor,
            borderRadius: BorderRadius.circular(height / 2),
          ),
        )
            .animate()
            .scaleX(
              begin: 0.0,
              end: progress,
              duration: duration,
              curve: Curves.easeOut,
            )
            .then()
            .shimmer(
              duration: const Duration(milliseconds: 500),
              color: Colors.white.withOpacity(0.5),
            ),
      ),
    );
  }
}

// Custom painter for checkmark
class CheckmarkPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  
  CheckmarkPainter({
    required this.color,
    this.strokeWidth = 3.0,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;
    
    final path = Path();
    
    // Draw checkmark
    path.moveTo(size.width * 0.2, size.height * 0.5);
    path.lineTo(size.width * 0.45, size.height * 0.7);
    path.lineTo(size.width * 0.8, size.height * 0.3);
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Success dialog with animation
class AnimatedSuccessDialog extends StatelessWidget {
  final String title;
  final String? message;
  final VoidCallback? onConfirm;
  final String confirmText;
  
  const AnimatedSuccessDialog({
    super.key,
    required this.title,
    this.message,
    this.onConfirm,
    this.confirmText = 'OK',
  });
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SuccessAnimations.successBadge(),
            
            const SizedBox(height: 24),
            
            SuccessAnimations.successMessage(
              message: title,
              textStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            if (message != null) ...[
              const SizedBox(height: 16),
              SuccessAnimations.successMessage(
                message: message!,
                textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
            ],
            
            const SizedBox(height: 32),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onConfirm ?? () => Navigator.of(context).pop(),
                child: Text(confirmText),
              ),
            )
                .animate()
                .slideY(
                  begin: 0.3,
                  duration: const Duration(milliseconds: 400),
                  delay: const Duration(milliseconds: 600),
                )
                .fadeIn(
                  duration: const Duration(milliseconds: 400),
                  delay: const Duration(milliseconds: 600),
                ),
          ],
        ),
      ),
    )
        .animate()
        .scale(
          begin: const Offset(0.8, 0.8),
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        )
        .fadeIn(
          duration: const Duration(milliseconds: 300),
        );
  }
}
