import 'package:flutter/material.dart';
import 'package:animations/animations.dart';

class PageTransitions {
  static const Duration _defaultDuration = Duration(milliseconds: 300);
  
  // Fade through transition for main navigation
  static Widget fadeThrough({
    required Widget child,
    Duration duration = _defaultDuration,
  }) {
    return PageTransitionSwitcher(
      duration: duration,
      transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
        return FadeThroughTransition(
          animation: primaryAnimation,
          secondaryAnimation: secondaryAnimation,
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Shared axis transition for hierarchical navigation
  static Widget sharedAxisHorizontal({
    required Widget child,
    Duration duration = _defaultDuration,
    bool reverse = false,
  }) {
    return PageTransitionSwitcher(
      duration: duration,
      reverse: reverse,
      transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
        return SharedAxisTransition(
          animation: primaryAnimation,
          secondaryAnimation: secondaryAnimation,
          transitionType: SharedAxisTransitionType.horizontal,
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Shared axis vertical transition for modal-like navigation
  static Widget sharedAxisVertical({
    required Widget child,
    Duration duration = _defaultDuration,
    bool reverse = false,
  }) {
    return PageTransitionSwitcher(
      duration: duration,
      reverse: reverse,
      transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
        return SharedAxisTransition(
          animation: primaryAnimation,
          secondaryAnimation: secondaryAnimation,
          transitionType: SharedAxisTransitionType.vertical,
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Container transform for element-to-page transitions
  static Widget containerTransform({
    required Widget child,
    Duration duration = _defaultDuration,
  }) {
    return PageTransitionSwitcher(
      duration: duration,
      transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
        return FadeScaleTransition(
          animation: primaryAnimation,
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Custom slide transition
  static Widget slideTransition({
    required Widget child,
    Duration duration = _defaultDuration,
    Offset begin = const Offset(1.0, 0.0),
    Offset end = Offset.zero,
    Curve curve = Curves.easeInOut,
  }) {
    return PageTransitionSwitcher(
      duration: duration,
      transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
        final slideAnimation = Tween<Offset>(
          begin: begin,
          end: end,
        ).animate(CurvedAnimation(
          parent: primaryAnimation,
          curve: curve,
        ));
        
        return SlideTransition(
          position: slideAnimation,
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Scale transition with fade
  static Widget scaleTransition({
    required Widget child,
    Duration duration = _defaultDuration,
    double begin = 0.8,
    double end = 1.0,
    Curve curve = Curves.easeOutCubic,
  }) {
    return PageTransitionSwitcher(
      duration: duration,
      transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
        final scaleAnimation = Tween<double>(
          begin: begin,
          end: end,
        ).animate(CurvedAnimation(
          parent: primaryAnimation,
          curve: curve,
        ));
        
        final fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: primaryAnimation,
          curve: Curves.easeIn,
        ));
        
        return FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
      child: child,
    );
  }
}

// Custom route for page transitions
class CustomPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final PageTransitionType transitionType;
  final Duration duration;
  final Curve curve;
  
  CustomPageRoute({
    required this.child,
    this.transitionType = PageTransitionType.fadeThrough,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          settings: settings,
        );
  
  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    switch (transitionType) {
      case PageTransitionType.fadeThrough:
        return FadeThroughTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
        );
      case PageTransitionType.sharedAxisHorizontal:
        return SharedAxisTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          transitionType: SharedAxisTransitionType.horizontal,
          child: child,
        );
      case PageTransitionType.sharedAxisVertical:
        return SharedAxisTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          transitionType: SharedAxisTransitionType.vertical,
          child: child,
        );
      case PageTransitionType.fadeScale:
        return FadeScaleTransition(
          animation: animation,
          child: child,
        );
      case PageTransitionType.slide:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: curve,
          )),
          child: child,
        );
      case PageTransitionType.scale:
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.8,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: curve,
          )),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
    }
  }
}

enum PageTransitionType {
  fadeThrough,
  sharedAxisHorizontal,
  sharedAxisVertical,
  fadeScale,
  slide,
  scale,
}

// Extension for easy route creation
extension PageTransitionExtension on Widget {
  Route<T> createRoute<T>({
    PageTransitionType transitionType = PageTransitionType.fadeThrough,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    RouteSettings? settings,
  }) {
    return CustomPageRoute<T>(
      child: this,
      transitionType: transitionType,
      duration: duration,
      curve: curve,
      settings: settings,
    );
  }
}
