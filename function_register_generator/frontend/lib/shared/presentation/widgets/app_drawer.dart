import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/providers/theme_provider.dart';

class AppDrawer extends ConsumerWidget {
  const AppDrawer({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocation = GoRouterState.of(context).uri.path;
    
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.build_circle,
                      color: AppColors.onPrimary,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Tool Registry',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.onPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Discover and register tools',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          
          _DrawerItem(
            icon: Icons.home_outlined,
            selectedIcon: Icons.home,
            title: 'Home',
            isSelected: currentLocation == '/',
            onTap: () {
              Navigator.pop(context);
              context.goNamed('home');
            },
          ),
          
          _DrawerItem(
            icon: Icons.build_outlined,
            selectedIcon: Icons.build,
            title: 'Browse Tools',
            isSelected: currentLocation.startsWith('/tools'),
            onTap: () {
              Navigator.pop(context);
              context.goNamed('tools');
            },
          ),
          
          _DrawerItem(
            icon: Icons.search_outlined,
            selectedIcon: Icons.search,
            title: 'Search',
            isSelected: currentLocation.startsWith('/search'),
            onTap: () {
              Navigator.pop(context);
              context.goNamed('search');
            },
          ),
          
          const Divider(),
          
          _DrawerItem(
            icon: Icons.add_circle_outline,
            selectedIcon: Icons.add_circle,
            title: 'Register Tool',
            isSelected: currentLocation.startsWith('/register'),
            onTap: () {
              Navigator.pop(context);
              context.goNamed('register');
            },
          ),
          
          _DrawerItem(
            icon: Icons.drafts_outlined,
            selectedIcon: Icons.drafts,
            title: 'My Drafts',
            isSelected: currentLocation.startsWith('/drafts'),
            onTap: () {
              Navigator.pop(context);
              context.goNamed('drafts');
            },
          ),
          
          const Divider(),
          
          // Registration shortcuts
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Quick Register',
              style: AppTextStyles.labelMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          _DrawerItem(
            icon: Icons.code,
            title: 'From cURL',
            onTap: () {
              Navigator.pop(context);
              context.goNamed('register-curl');
            },
          ),
          
          _DrawerItem(
            icon: Icons.integration_instructions,
            title: 'Python Function',
            onTap: () {
              Navigator.pop(context);
              context.goNamed('register-python');
            },
          ),
          
          _DrawerItem(
            icon: Icons.extension,
            title: 'MCP Tool',
            onTap: () {
              Navigator.pop(context);
              context.goNamed('register-mcp');
            },
          ),
          
          _DrawerItem(
            icon: Icons.chat,
            title: 'Natural Language',
            onTap: () {
              Navigator.pop(context);
              context.goNamed('register-natural');
            },
          ),
          
          const Divider(),
          
          // Theme toggle
          Consumer(
            builder: (context, ref, child) {
              final isDark = ref.watch(isDarkModeProvider);
              return _DrawerItem(
                icon: isDark ? Icons.light_mode : Icons.dark_mode,
                title: isDark ? 'Light Mode' : 'Dark Mode',
                onTap: () {
                  ref.read(themeModeProvider.notifier).toggleTheme();
                },
              );
            },
          ),
          
          _DrawerItem(
            icon: Icons.settings,
            title: 'Settings',
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to settings
            },
          ),
          
          _DrawerItem(
            icon: Icons.help_outline,
            title: 'Help & Support',
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to help
            },
          ),
        ],
      ),
    );
  }
}

class _DrawerItem extends StatelessWidget {
  final IconData icon;
  final IconData? selectedIcon;
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  
  const _DrawerItem({
    required this.icon,
    this.selectedIcon,
    required this.title,
    this.isSelected = false,
    required this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(
        isSelected ? (selectedIcon ?? icon) : icon,
        color: isSelected ? AppColors.primary : null,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          color: isSelected ? AppColors.primary : null,
          fontWeight: isSelected ? FontWeight.w600 : null,
        ),
      ),
      selected: isSelected,
      selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
      onTap: onTap,
    );
  }
}