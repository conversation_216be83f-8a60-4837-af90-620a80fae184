import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';

class AppNavigationRail extends StatelessWidget {
  final bool extended;
  
  const AppNavigationRail({
    super.key,
    this.extended = false,
  });
  
  @override
  Widget build(BuildContext context) {
    final currentLocation = GoRouterState.of(context).uri.path;
    
    return NavigationRail(
      extended: extended,
      selectedIndex: _getSelectedIndex(currentLocation),
      onDestinationSelected: (index) => _onDestinationSelected(context, index),
      labelType: extended 
          ? NavigationRailLabelType.none 
          : NavigationRailLabelType.all,
      destinations: const [
        NavigationRailDestination(
          icon: Icon(Icons.home_outlined),
          selectedIcon: Icon(Icons.home),
          label: Text('Home'),
        ),
        NavigationRailDestination(
          icon: Icon(Icons.build_outlined),
          selectedIcon: Icon(Icons.build),
          label: Text('Tools'),
        ),
        NavigationRailDestination(
          icon: Icon(Icons.add_circle_outline),
          selectedIcon: Icon(Icons.add_circle),
          label: Text('Register'),
        ),
        NavigationRailDestination(
          icon: Icon(Icons.search_outlined),
          selectedIcon: Icon(Icons.search),
          label: Text('Search'),
        ),
        NavigationRailDestination(
          icon: Icon(Icons.drafts_outlined),
          selectedIcon: Icon(Icons.drafts),
          label: Text('Drafts'),
        ),
      ],
    );
  }
  
  int _getSelectedIndex(String location) {
    if (location.startsWith('/tools')) {
      return 1;
    } else if (location.startsWith('/register')) {
      return 2;
    } else if (location.startsWith('/search')) {
      return 3;
    } else if (location.startsWith('/drafts')) {
      return 4;
    } else {
      return 0; // Home
    }
  }
  
  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.goNamed('home');
        break;
      case 1:
        context.goNamed('tools');
        break;
      case 2:
        context.goNamed('register');
        break;
      case 3:
        context.goNamed('search');
        break;
      case 4:
        context.goNamed('drafts');
        break;
    }
  }
}