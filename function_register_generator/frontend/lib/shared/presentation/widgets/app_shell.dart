import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/responsive/responsive_layout.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/providers/theme_provider.dart';
import 'app_navigation_bar.dart';
import 'app_navigation_rail.dart';
import 'app_drawer.dart';

class AppShell extends ConsumerWidget {
  final Widget child;
  
  const AppShell({
    super.key,
    required this.child,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ResponsiveLayout(
      mobile: _MobileLayout(child: child),
      tablet: _TabletLayout(child: child),
      desktop: _DesktopLayout(child: child),
    );
  }
}

class _MobileLayout extends StatelessWidget {
  final Widget child;
  
  const _MobileLayout({required this.child});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      drawer: const AppDrawer(),
      body: child,
      bottomNavigationBar: const AppNavigationBar(),
    );
  }
}

class _TabletLayout extends StatelessWidget {
  final Widget child;
  
  const _TabletLayout({required this.child});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Row(
        children: [
          const AppNavigationRail(),
          Expanded(child: child),
        ],
      ),
    );
  }
}

class _DesktopLayout extends StatelessWidget {
  final Widget child;
  
  const _DesktopLayout({required this.child});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Row(
        children: [
          const AppNavigationRail(extended: true),
          Expanded(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: context.maxWidth,
              ),
              child: child,
            ),
          ),
        ],
      ),
    );
  }
}

PreferredSizeWidget _buildAppBar(BuildContext context) {
  return AppBar(
    title: Row(
      children: [
        Icon(
          Icons.build_circle,
          color: AppColors.primary,
          size: 28,
        ),
        const SizedBox(width: 8),
        Text(
          'Tool Registry',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
        ),
      ],
    ),
    actions: [
      // Search button
      if (context.isDesktop) ...[
        SizedBox(
          width: 300,
          child: TextField(
            decoration: InputDecoration(
              hintText: 'Search tools...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: AppColors.surfaceVariant,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
            ),
            onSubmitted: (query) {
              if (query.isNotEmpty) {
                context.goNamed('search', queryParameters: {'q': query});
              }
            },
          ),
        ),
        const SizedBox(width: 16),
      ] else ...[
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => context.goNamed('search'),
        ),
      ],
      
      // Theme toggle
      Consumer(
        builder: (context, ref, child) {
          final isDark = ref.watch(isDarkModeProvider);
          return IconButton(
            icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              ref.read(themeModeProvider.notifier).toggleTheme();
            },
          );
        },
      ),
      
      // Profile menu
      PopupMenuButton<String>(
        icon: const CircleAvatar(
          radius: 16,
          child: Icon(Icons.person, size: 20),
        ),
        onSelected: (value) {
          switch (value) {
            case 'profile':
              // TODO: Navigate to profile
              break;
            case 'settings':
              // TODO: Navigate to settings
              break;
            case 'logout':
              // TODO: Handle logout
              break;
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'profile',
            child: ListTile(
              leading: Icon(Icons.person),
              title: Text('Profile'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'settings',
            child: ListTile(
              leading: Icon(Icons.settings),
              title: Text('Settings'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuDivider(),
          const PopupMenuItem(
            value: 'logout',
            child: ListTile(
              leading: Icon(Icons.logout),
              title: Text('Logout'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
      
      const SizedBox(width: 8),
    ],
  );
}