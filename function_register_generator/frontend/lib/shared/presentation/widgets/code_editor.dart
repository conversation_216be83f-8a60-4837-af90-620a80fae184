import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class CodeEditor extends StatefulWidget {
  final TextEditingController controller;
  final String language;
  final String? placeholder;
  final bool readOnly;
  final Function(String)? onChanged;
  
  const CodeEditor({
    super.key,
    required this.controller,
    required this.language,
    this.placeholder,
    this.readOnly = false,
    this.onChanged,
  });
  
  @override
  State<CodeEditor> createState() => _CodeEditorState();
}

class _CodeEditorState extends State<CodeEditor> {
  final FocusNode _focusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  
  @override
  void dispose() {
    _focusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with language indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              border: Border(
                bottom: BorderSide(color: AppColors.outline),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getLanguageColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    widget.language.toUpperCase(),
                    style: AppTextStyles.labelSmall.copyWith(
                      color: _getLanguageColor(),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                if (!widget.readOnly) ...[
                  IconButton(
                    onPressed: _copyToClipboard,
                    icon: const Icon(Icons.copy, size: 16),
                    tooltip: 'Copy to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                  IconButton(
                    onPressed: _pasteFromClipboard,
                    icon: const Icon(Icons.paste, size: 16),
                    tooltip: 'Paste from clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ] else ...[
                  IconButton(
                    onPressed: _copyToClipboard,
                    icon: const Icon(Icons.copy, size: 16),
                    tooltip: 'Copy to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Code editor area
          Expanded(
            child: Stack(
              children: [
                // Line numbers
                if (widget.controller.text.isNotEmpty) ...[
                  Positioned(
                    left: 0,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: 40,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: AppColors.background,
                        border: Border(
                          right: BorderSide(color: AppColors.outline),
                        ),
                      ),
                      child: _buildLineNumbers(),
                    ),
                  ),
                ],
                
                // Text field
                Positioned(
                  left: widget.controller.text.isNotEmpty ? 40 : 0,
                  top: 0,
                  right: 0,
                  bottom: 0,
                  child: TextField(
                    controller: widget.controller,
                    focusNode: _focusNode,
                    readOnly: widget.readOnly,
                    maxLines: null,
                    expands: true,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                    decoration: InputDecoration(
                      hintText: widget.placeholder,
                      hintStyle: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.onSurfaceVariant,
                        fontFamily: 'monospace',
                        fontSize: 14,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(12),
                    ),
                    onChanged: (value) {
                      setState(() {}); // Rebuild to update line numbers
                      widget.onChanged?.call(value);
                    },
                    scrollController: _scrollController,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildLineNumbers() {
    final lines = widget.controller.text.split('\n');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: List.generate(lines.length, (index) {
        return Container(
          height: 20, // Approximate line height
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 8),
          child: Text(
            '${index + 1}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurfaceVariant,
              fontFamily: 'monospace',
              fontSize: 12,
            ),
          ),
        );
      }),
    );
  }
  
  Color _getLanguageColor() {
    switch (widget.language.toLowerCase()) {
      case 'python':
        return AppColors.transportPython;
      case 'javascript':
      case 'js':
        return const Color(0xFFF7DF1E);
      case 'json':
        return AppColors.primary;
      case 'bash':
      case 'shell':
        return AppColors.categoryApi;
      case 'yaml':
      case 'yml':
        return AppColors.secondary;
      default:
        return AppColors.onSurfaceVariant;
    }
  }
  
  Future<void> _copyToClipboard() async {
    await Clipboard.setData(ClipboardData(text: widget.controller.text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Code copied to clipboard'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
  
  Future<void> _pasteFromClipboard() async {
    if (widget.readOnly) return;
    
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData?.text != null) {
      final text = clipboardData!.text!;
      final selection = widget.controller.selection;
      
      if (selection.isValid) {
        final newText = widget.controller.text.replaceRange(
          selection.start,
          selection.end,
          text,
        );
        widget.controller.value = widget.controller.value.copyWith(
          text: newText,
          selection: TextSelection.collapsed(
            offset: selection.start + text.length,
          ),
        );
      } else {
        widget.controller.text = text;
      }
      
      widget.onChanged?.call(widget.controller.text);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Code pasted from clipboard'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }
}

// Syntax highlighting helper (basic implementation)
class SyntaxHighlighter {
  static TextSpan highlight(String code, String language) {
    // This is a very basic implementation
    // In a real app, you'd use a proper syntax highlighting library
    
    final style = AppTextStyles.bodyMedium.copyWith(
      fontFamily: 'monospace',
      fontSize: 14,
    );
    
    switch (language.toLowerCase()) {
      case 'python':
        return _highlightPython(code, style);
      case 'json':
        return _highlightJson(code, style);
      case 'bash':
        return _highlightBash(code, style);
      default:
        return TextSpan(text: code, style: style);
    }
  }
  
  static TextSpan _highlightPython(String code, TextStyle baseStyle) {
    // Very basic Python highlighting
    final keywords = ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except', 'import', 'from', 'return'];
    
    final spans = <TextSpan>[];
    final words = code.split(RegExp(r'(\s+)'));
    
    for (final word in words) {
      if (keywords.contains(word)) {
        spans.add(TextSpan(
          text: word,
          style: baseStyle.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
        ));
      } else if (word.startsWith('"') && word.endsWith('"')) {
        spans.add(TextSpan(
          text: word,
          style: baseStyle.copyWith(color: AppColors.success),
        ));
      } else {
        spans.add(TextSpan(text: word, style: baseStyle));
      }
    }
    
    return TextSpan(children: spans);
  }
  
  static TextSpan _highlightJson(String code, TextStyle baseStyle) {
    // Very basic JSON highlighting
    return TextSpan(text: code, style: baseStyle);
  }
  
  static TextSpan _highlightBash(String code, TextStyle baseStyle) {
    // Very basic Bash highlighting
    return TextSpan(text: code, style: baseStyle);
  }
}
