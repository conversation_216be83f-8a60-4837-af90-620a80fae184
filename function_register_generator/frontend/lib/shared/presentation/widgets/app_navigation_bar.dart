import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';

class AppNavigationBar extends StatelessWidget {
  const AppNavigationBar({super.key});
  
  @override
  Widget build(BuildContext context) {
    final currentLocation = GoRouterState.of(context).uri.path;
    
    return NavigationBar(
      selectedIndex: _getSelectedIndex(currentLocation),
      onDestinationSelected: (index) => _onDestinationSelected(context, index),
      destinations: const [
        NavigationDestination(
          icon: Icon(Icons.home_outlined),
          selectedIcon: Icon(Icons.home),
          label: 'Home',
        ),
        NavigationDestination(
          icon: Icon(Icons.build_outlined),
          selectedIcon: Icon(Icons.build),
          label: 'Tools',
        ),
        NavigationDestination(
          icon: Icon(Icons.add_circle_outline),
          selectedIcon: Icon(Icons.add_circle),
          label: 'Register',
        ),
        NavigationDestination(
          icon: Icon(Icons.drafts_outlined),
          selectedIcon: Icon(Icons.drafts),
          label: 'Drafts',
        ),
      ],
    );
  }
  
  int _getSelectedIndex(String location) {
    if (location.startsWith('/tools')) {
      return 1;
    } else if (location.startsWith('/register')) {
      return 2;
    } else if (location.startsWith('/drafts')) {
      return 3;
    } else {
      return 0; // Home
    }
  }
  
  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.goNamed('home');
        break;
      case 1:
        context.goNamed('tools');
        break;
      case 2:
        context.goNamed('register');
        break;
      case 3:
        context.goNamed('drafts');
        break;
    }
  }
}