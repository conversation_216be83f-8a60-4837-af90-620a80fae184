import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/responsive/responsive_layout.dart';

class NotFoundPage extends StatelessWidget {
  final String? error;
  
  const NotFoundPage({
    super.key,
    this.error,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: context.maxWidth * 0.8,
          ),
          padding: context.responsivePadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 404 Icon
              Icon(
                Icons.error_outline,
                size: context.isDesktop ? 120 : 80,
                color: AppColors.error.withValues(alpha: 0.6),
              ),
              
              SizedBox(height: context.responsiveSpacing),
              
              // Title
              Text(
                '404',
                style: context.isDesktop 
                    ? AppTextStyles.displayLarge
                    : AppTextStyles.displayMedium,
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Subtitle
              Text(
                'Page Not Found',
                style: (context.isDesktop
                    ? AppTextStyles.headlineMedium
                    : AppTextStyles.headlineSmall).copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              
              SizedBox(height: context.responsiveSpacing),
              
              // Description
              Text(
                'The page you are looking for might have been removed, '
                'had its name changed, or is temporarily unavailable.',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              
              if (error != null) ...[
                SizedBox(height: context.responsiveSpacing),
                
                // Error details (only in debug mode)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Error Details:',
                        style: AppTextStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.error,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.error,
                          fontFamily: 'Monaco',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              SizedBox(height: context.responsiveSpacing * 2),
              
              // Action buttons
              Wrap(
                spacing: 16,
                runSpacing: 16,
                alignment: WrapAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => context.goNamed('home'),
                    icon: const Icon(Icons.home),
                    label: const Text('Go Home'),
                  ),
                  
                  OutlinedButton.icon(
                    onPressed: () => context.goNamed('tools'),
                    icon: const Icon(Icons.build),
                    label: const Text('Browse Tools'),
                  ),
                  
                  TextButton.icon(
                    onPressed: () {
                      if (context.canPop()) {
                        context.pop();
                      } else {
                        context.goNamed('home');
                      }
                    },
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Go Back'),
                  ),
                ],
              ),
              
              SizedBox(height: context.responsiveSpacing * 2),
              
              // Search suggestion
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.search,
                      color: AppColors.primary,
                      size: 32,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Looking for something specific?',
                      style: AppTextStyles.titleMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Try searching for tools or browse our categories',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => context.goNamed('search'),
                      child: const Text('Search Tools'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}