import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class KeyboardService {
  static final Map<String, VoidCallback> _shortcuts = {};
  static final Map<String, String> _shortcutDescriptions = {};
  
  // Register a keyboard shortcut
  static void registerShortcut({
    required String id,
    required LogicalKeySet keySet,
    required VoidCallback callback,
    required String description,
  }) {
    _shortcuts[id] = callback;
    _shortcutDescriptions[id] = description;
  }
  
  // Unregister a keyboard shortcut
  static void unregisterShortcut(String id) {
    _shortcuts.remove(id);
    _shortcutDescriptions.remove(id);
  }
  
  // Get all registered shortcuts
  static Map<String, String> getAllShortcuts() {
    return Map.from(_shortcutDescriptions);
  }
  
  // Execute a shortcut by ID
  static void executeShortcut(String id) {
    _shortcuts[id]?.call();
  }
  
  // Common shortcuts
  static const LogicalKeySet ctrlS = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyS,
  );
  
  static const LogicalKeySet ctrlZ = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyZ,
  );
  
  static const LogicalKeySet ctrlY = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyY,
  );
  
  static const LogicalKeySet ctrlF = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyF,
  );
  
  static const LogicalKeySet ctrlN = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyN,
  );
  
  static const LogicalKeySet escape = LogicalKeySet(
    LogicalKeyboardKey.escape,
  );
  
  static const LogicalKeySet enter = LogicalKeySet(
    LogicalKeyboardKey.enter,
  );
  
  static const LogicalKeySet tab = LogicalKeySet(
    LogicalKeyboardKey.tab,
  );
  
  static const LogicalKeySet shiftTab = LogicalKeySet(
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.tab,
  );
  
  static const LogicalKeySet arrowUp = LogicalKeySet(
    LogicalKeyboardKey.arrowUp,
  );
  
  static const LogicalKeySet arrowDown = LogicalKeySet(
    LogicalKeyboardKey.arrowDown,
  );
  
  static const LogicalKeySet arrowLeft = LogicalKeySet(
    LogicalKeyboardKey.arrowLeft,
  );
  
  static const LogicalKeySet arrowRight = LogicalKeySet(
    LogicalKeyboardKey.arrowRight,
  );
}

// Widget that provides keyboard shortcuts
class KeyboardShortcuts extends StatefulWidget {
  final Widget child;
  final Map<LogicalKeySet, VoidCallback> shortcuts;
  final bool autofocus;
  
  const KeyboardShortcuts({
    super.key,
    required this.child,
    required this.shortcuts,
    this.autofocus = true,
  });
  
  @override
  State<KeyboardShortcuts> createState() => _KeyboardShortcutsState();
}

class _KeyboardShortcutsState extends State<KeyboardShortcuts> {
  final FocusNode _focusNode = FocusNode();
  
  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return FocusableActionDetector(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      shortcuts: widget.shortcuts,
      actions: widget.shortcuts.map(
        (key, callback) => MapEntry(
          key,
          CallbackAction<Intent>(onInvoke: (_) => callback()),
        ),
      ),
      child: widget.child,
    );
  }
}

// Global keyboard shortcuts widget
class GlobalKeyboardShortcuts extends StatefulWidget {
  final Widget child;
  
  const GlobalKeyboardShortcuts({
    super.key,
    required this.child,
  });
  
  @override
  State<GlobalKeyboardShortcuts> createState() => _GlobalKeyboardShortcutsState();
}

class _GlobalKeyboardShortcutsState extends State<GlobalKeyboardShortcuts> {
  @override
  Widget build(BuildContext context) {
    return KeyboardShortcuts(
      shortcuts: {
        // Global shortcuts
        KeyboardService.ctrlF: () => _showSearchDialog(context),
        KeyboardService.ctrlN: () => _navigateToNewTool(context),
        KeyboardService.escape: () => _handleEscape(context),
      },
      child: widget.child,
    );
  }
  
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SearchDialog(),
    );
  }
  
  void _navigateToNewTool(BuildContext context) {
    Navigator.of(context).pushNamed('/register');
  }
  
  void _handleEscape(BuildContext context) {
    // Close any open dialogs or modals
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }
}

// Search dialog with keyboard navigation
class SearchDialog extends StatefulWidget {
  const SearchDialog({super.key});
  
  @override
  State<SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<SearchDialog> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  int _selectedIndex = 0;
  List<String> _results = [];
  
  @override
  void initState() {
    super.initState();
    _focusNode.requestFocus();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(16),
        child: KeyboardShortcuts(
          shortcuts: {
            KeyboardService.arrowUp: _moveUp,
            KeyboardService.arrowDown: _moveDown,
            KeyboardService.enter: _selectCurrent,
            KeyboardService.escape: () => Navigator.of(context).pop(),
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _controller,
                focusNode: _focusNode,
                decoration: const InputDecoration(
                  hintText: 'Search tools...',
                  prefixIcon: Icon(Icons.search),
                ),
                onChanged: _onSearchChanged,
              ),
              
              const SizedBox(height: 16),
              
              if (_results.isNotEmpty) ...[
                ConstrainedBox(
                  constraints: const BoxConstraints(maxHeight: 300),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _results.length,
                    itemBuilder: (context, index) {
                      final isSelected = index == _selectedIndex;
                      return ListTile(
                        title: Text(_results[index]),
                        selected: isSelected,
                        onTap: () => _selectItem(index),
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
  
  void _onSearchChanged(String query) {
    setState(() {
      // Mock search results
      _results = [
        'Weather API',
        'Text Summarizer',
        'Database Query Builder',
        'File Converter',
      ].where((item) => item.toLowerCase().contains(query.toLowerCase())).toList();
      _selectedIndex = 0;
    });
  }
  
  void _moveUp() {
    if (_results.isNotEmpty) {
      setState(() {
        _selectedIndex = (_selectedIndex - 1) % _results.length;
      });
    }
  }
  
  void _moveDown() {
    if (_results.isNotEmpty) {
      setState(() {
        _selectedIndex = (_selectedIndex + 1) % _results.length;
      });
    }
  }
  
  void _selectCurrent() {
    if (_results.isNotEmpty && _selectedIndex < _results.length) {
      _selectItem(_selectedIndex);
    }
  }
  
  void _selectItem(int index) {
    final selectedItem = _results[index];
    Navigator.of(context).pop(selectedItem);
    // Navigate to selected tool
  }
}

// Keyboard navigation helper
class KeyboardNavigationHelper {
  static void handleArrowKeys({
    required RawKeyEvent event,
    required int currentIndex,
    required int itemCount,
    required Function(int) onIndexChanged,
    required VoidCallback onEnter,
  }) {
    if (event is RawKeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowUp:
          if (currentIndex > 0) {
            onIndexChanged(currentIndex - 1);
          }
          break;
        case LogicalKeyboardKey.arrowDown:
          if (currentIndex < itemCount - 1) {
            onIndexChanged(currentIndex + 1);
          }
          break;
        case LogicalKeyboardKey.enter:
          onEnter();
          break;
      }
    }
  }
  
  static Widget buildNavigableList({
    required List<Widget> children,
    required int selectedIndex,
    required Function(int) onSelectionChanged,
    required Function(int) onItemActivated,
  }) {
    return Focus(
      onKey: (node, event) {
        handleArrowKeys(
          event: event,
          currentIndex: selectedIndex,
          itemCount: children.length,
          onIndexChanged: onSelectionChanged,
          onEnter: () => onItemActivated(selectedIndex),
        );
        return KeyEventResult.handled;
      },
      child: ListView.builder(
        itemCount: children.length,
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              color: index == selectedIndex
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : null,
            ),
            child: children[index],
          );
        },
      ),
    );
  }
}

// Accessibility helper
class AccessibilityHelper {
  static Widget makeAccessible({
    required Widget child,
    required String semanticLabel,
    String? hint,
    bool isButton = false,
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: hint,
      button: isButton,
      selected: isSelected,
      onTap: onTap,
      child: child,
    );
  }
  
  static Widget announceChanges({
    required Widget child,
    required String announcement,
  }) {
    return Semantics(
      liveRegion: true,
      child: child,
    );
  }
  
  static void announceToScreenReader(String message) {
    // This would typically use a platform-specific implementation
    // For now, we'll use a simple approach
    SemanticsService.announce(message, TextDirection.ltr);
  }
}
