import 'dart:math';

class SuggestionService {
  static const int maxSuggestions = 8;
  static const double minSimilarity = 0.3;
  
  // Generate search suggestions based on query and available data
  static List<SearchSuggestion> generateSearchSuggestions({
    required String query,
    required List<Map<String, dynamic>> tools,
    List<String> recentSearches = const [],
    List<String> popularSearches = const [],
  }) {
    if (query.isEmpty) {
      return _getDefaultSuggestions(recentSearches, popularSearches);
    }
    
    final suggestions = <SearchSuggestion>[];
    final queryLower = query.toLowerCase();
    
    // Tool name matches
    for (final tool in tools) {
      final name = (tool['name'] as String).toLowerCase();
      if (name.contains(queryLower)) {
        suggestions.add(SearchSuggestion(
          text: tool['name'],
          type: SuggestionType.toolName,
          score: _calculateSimilarity(queryLower, name),
          metadata: {'tool_id': tool['id']},
        ));
      }
    }
    
    // Category matches
    final categories = tools
        .map((tool) => tool['category'] as String)
        .toSet()
        .where((category) => category.toLowerCase().contains(queryLower))
        .map((category) => SearchSuggestion(
              text: category,
              type: SuggestionType.category,
              score: _calculateSimilarity(queryLower, category.toLowerCase()),
            ));
    suggestions.addAll(categories);
    
    // Tag matches
    final tags = <String>{};
    for (final tool in tools) {
      final toolTags = tool['tags'] as List<String>? ?? [];
      for (final tag in toolTags) {
        if (tag.toLowerCase().contains(queryLower)) {
          tags.add(tag);
        }
      }
    }
    
    suggestions.addAll(tags.map((tag) => SearchSuggestion(
          text: tag,
          type: SuggestionType.tag,
          score: _calculateSimilarity(queryLower, tag.toLowerCase()),
        )));
    
    // Description matches (fuzzy)
    for (final tool in tools) {
      final description = (tool['description'] as String).toLowerCase();
      if (description.contains(queryLower)) {
        suggestions.add(SearchSuggestion(
          text: tool['name'],
          type: SuggestionType.description,
          score: _calculateSimilarity(queryLower, description) * 0.8, // Lower priority
          metadata: {
            'tool_id': tool['id'],
            'description': tool['description'],
          },
        ));
      }
    }
    
    // Recent searches that match
    for (final recent in recentSearches) {
      if (recent.toLowerCase().contains(queryLower)) {
        suggestions.add(SearchSuggestion(
          text: recent,
          type: SuggestionType.recent,
          score: _calculateSimilarity(queryLower, recent.toLowerCase()) + 0.1, // Boost recent
        ));
      }
    }
    
    // Sort by score and take top suggestions
    suggestions.sort((a, b) => b.score.compareTo(a.score));
    return suggestions
        .where((s) => s.score >= minSimilarity)
        .take(maxSuggestions)
        .toList();
  }
  
  // Generate auto-complete suggestions for form fields
  static List<String> generateAutoComplete({
    required String query,
    required List<String> options,
    int maxResults = 5,
  }) {
    if (query.isEmpty) return options.take(maxResults).toList();
    
    final queryLower = query.toLowerCase();
    final matches = options
        .where((option) => option.toLowerCase().contains(queryLower))
        .map((option) => {
              'text': option,
              'score': _calculateSimilarity(queryLower, option.toLowerCase()),
            })
        .toList();
    
    matches.sort((a, b) => (b['score'] as double).compareTo(a['score'] as double));
    
    return matches
        .take(maxResults)
        .map((match) => match['text'] as String)
        .toList();
  }
  
  // Generate smart suggestions based on context
  static List<ContextualSuggestion> generateContextualSuggestions({
    required String currentField,
    required Map<String, dynamic> formData,
    required List<Map<String, dynamic>> tools,
  }) {
    final suggestions = <ContextualSuggestion>[];
    
    switch (currentField) {
      case 'name':
        suggestions.addAll(_generateNameSuggestions(formData, tools));
        break;
      case 'description':
        suggestions.addAll(_generateDescriptionSuggestions(formData, tools));
        break;
      case 'tags':
        suggestions.addAll(_generateTagSuggestions(formData, tools));
        break;
      case 'category':
        suggestions.addAll(_generateCategorySuggestions(formData, tools));
        break;
    }
    
    return suggestions;
  }
  
  static List<SearchSuggestion> _getDefaultSuggestions(
    List<String> recentSearches,
    List<String> popularSearches,
  ) {
    final suggestions = <SearchSuggestion>[];
    
    // Recent searches
    suggestions.addAll(recentSearches.take(3).map((search) => SearchSuggestion(
          text: search,
          type: SuggestionType.recent,
          score: 1.0,
        )));
    
    // Popular searches
    suggestions.addAll(popularSearches.take(5).map((search) => SearchSuggestion(
          text: search,
          type: SuggestionType.popular,
          score: 0.9,
        )));
    
    return suggestions.take(maxSuggestions).toList();
  }
  
  static List<ContextualSuggestion> _generateNameSuggestions(
    Map<String, dynamic> formData,
    List<Map<String, dynamic>> tools,
  ) {
    final suggestions = <ContextualSuggestion>[];
    final category = formData['category'] as String?;
    
    if (category != null) {
      // Suggest names based on category
      final categoryTools = tools.where((tool) => tool['category'] == category);
      final commonWords = _extractCommonWords(
        categoryTools.map((tool) => tool['name'] as String).toList(),
      );
      
      for (final word in commonWords.take(3)) {
        suggestions.add(ContextualSuggestion(
          text: word,
          reason: 'Common in $category tools',
          confidence: 0.8,
        ));
      }
    }
    
    return suggestions;
  }
  
  static List<ContextualSuggestion> _generateDescriptionSuggestions(
    Map<String, dynamic> formData,
    List<Map<String, dynamic>> tools,
  ) {
    final suggestions = <ContextualSuggestion>[];
    final category = formData['category'] as String?;
    
    if (category != null) {
      final templates = _getDescriptionTemplates(category);
      for (final template in templates) {
        suggestions.add(ContextualSuggestion(
          text: template,
          reason: 'Template for $category tools',
          confidence: 0.7,
        ));
      }
    }
    
    return suggestions;
  }
  
  static List<ContextualSuggestion> _generateTagSuggestions(
    Map<String, dynamic> formData,
    List<Map<String, dynamic>> tools,
  ) {
    final suggestions = <ContextualSuggestion>[];
    final category = formData['category'] as String?;
    
    if (category != null) {
      // Get popular tags for this category
      final categoryTools = tools.where((tool) => tool['category'] == category);
      final tagCounts = <String, int>{};
      
      for (final tool in categoryTools) {
        final tags = tool['tags'] as List<String>? ?? [];
        for (final tag in tags) {
          tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
        }
      }
      
      final popularTags = tagCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      for (final entry in popularTags.take(5)) {
        suggestions.add(ContextualSuggestion(
          text: entry.key,
          reason: 'Popular in ${category} (${entry.value} tools)',
          confidence: min(1.0, entry.value / 10.0),
        ));
      }
    }
    
    return suggestions;
  }
  
  static List<ContextualSuggestion> _generateCategorySuggestions(
    Map<String, dynamic> formData,
    List<Map<String, dynamic>> tools,
  ) {
    final suggestions = <ContextualSuggestion>[];
    final name = formData['name'] as String?;
    final description = formData['description'] as String?;
    
    if (name != null || description != null) {
      final text = '${name ?? ''} ${description ?? ''}'.toLowerCase();
      
      // Simple keyword-based category suggestion
      final categoryKeywords = {
        'api': ['api', 'endpoint', 'rest', 'http', 'request'],
        'data': ['data', 'database', 'query', 'storage', 'analytics'],
        'ai': ['ai', 'ml', 'machine learning', 'neural', 'model'],
        'utility': ['utility', 'tool', 'helper', 'convert', 'format'],
        'communication': ['email', 'message', 'chat', 'notification'],
      };
      
      for (final entry in categoryKeywords.entries) {
        final matches = entry.value.where((keyword) => text.contains(keyword)).length;
        if (matches > 0) {
          suggestions.add(ContextualSuggestion(
            text: entry.key,
            reason: 'Based on keywords: ${entry.value.where((k) => text.contains(k)).join(', ')}',
            confidence: min(1.0, matches / entry.value.length),
          ));
        }
      }
    }
    
    return suggestions;
  }
  
  static double _calculateSimilarity(String query, String target) {
    if (target.startsWith(query)) return 1.0;
    if (target.contains(query)) return 0.8;
    
    // Simple Levenshtein distance-based similarity
    final distance = _levenshteinDistance(query, target);
    final maxLength = max(query.length, target.length);
    return 1.0 - (distance / maxLength);
  }
  
  static int _levenshteinDistance(String s1, String s2) {
    final matrix = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );
    
    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }
    
    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce(min);
      }
    }
    
    return matrix[s1.length][s2.length];
  }
  
  static List<String> _extractCommonWords(List<String> texts) {
    final wordCounts = <String, int>{};
    
    for (final text in texts) {
      final words = text.toLowerCase().split(RegExp(r'\W+'));
      for (final word in words) {
        if (word.length > 2) {
          wordCounts[word] = (wordCounts[word] ?? 0) + 1;
        }
      }
    }
    
    final sortedWords = wordCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedWords.map((e) => e.key).toList();
  }
  
  static List<String> _getDescriptionTemplates(String category) {
    switch (category) {
      case 'api':
        return [
          'A REST API tool that provides...',
          'HTTP endpoint for accessing...',
          'API integration for...',
        ];
      case 'data':
        return [
          'Data processing tool for...',
          'Database utility that...',
          'Analytics tool for...',
        ];
      case 'ai':
        return [
          'AI-powered tool for...',
          'Machine learning model that...',
          'Intelligent assistant for...',
        ];
      default:
        return [
          'A useful tool for...',
          'Utility that helps with...',
          'Tool designed to...',
        ];
    }
  }
}

class SearchSuggestion {
  final String text;
  final SuggestionType type;
  final double score;
  final Map<String, dynamic> metadata;
  
  SearchSuggestion({
    required this.text,
    required this.type,
    required this.score,
    this.metadata = const {},
  });
}

class ContextualSuggestion {
  final String text;
  final String reason;
  final double confidence;
  
  ContextualSuggestion({
    required this.text,
    required this.reason,
    required this.confidence,
  });
}

enum SuggestionType {
  toolName,
  category,
  tag,
  description,
  recent,
  popular,
}
