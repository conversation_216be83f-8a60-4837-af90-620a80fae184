import 'package:flutter/material.dart';

class ActionService {
  static final List<UndoableAction> _actionHistory = [];
  static int _currentIndex = -1;
  static const int maxHistorySize = 50;
  
  // Execute an action and add it to history
  static Future<bool> executeAction(UndoableAction action) async {
    try {
      final success = await action.execute();
      if (success) {
        _addToHistory(action);
      }
      return success;
    } catch (e) {
      return false;
    }
  }
  
  // Undo the last action
  static Future<bool> undo() async {
    if (!canUndo()) return false;
    
    final action = _actionHistory[_currentIndex];
    try {
      final success = await action.undo();
      if (success) {
        _currentIndex--;
      }
      return success;
    } catch (e) {
      return false;
    }
  }
  
  // Redo the next action
  static Future<bool> redo() async {
    if (!canRedo()) return false;
    
    _currentIndex++;
    final action = _actionHistory[_currentIndex];
    try {
      final success = await action.execute();
      if (!success) {
        _currentIndex--;
      }
      return success;
    } catch (e) {
      _currentIndex--;
      return false;
    }
  }
  
  // Check if undo is possible
  static bool canUndo() {
    return _currentIndex >= 0;
  }
  
  // Check if redo is possible
  static bool canRedo() {
    return _currentIndex < _actionHistory.length - 1;
  }
  
  // Get the description of the last action
  static String? getLastActionDescription() {
    if (canUndo()) {
      return _actionHistory[_currentIndex].description;
    }
    return null;
  }
  
  // Get the description of the next action
  static String? getNextActionDescription() {
    if (canRedo()) {
      return _actionHistory[_currentIndex + 1].description;
    }
    return null;
  }
  
  // Clear action history
  static void clearHistory() {
    _actionHistory.clear();
    _currentIndex = -1;
  }
  
  // Add action to history
  static void _addToHistory(UndoableAction action) {
    // Remove any actions after current index (when adding new action after undo)
    if (_currentIndex < _actionHistory.length - 1) {
      _actionHistory.removeRange(_currentIndex + 1, _actionHistory.length);
    }
    
    // Add new action
    _actionHistory.add(action);
    _currentIndex++;
    
    // Limit history size
    if (_actionHistory.length > maxHistorySize) {
      _actionHistory.removeAt(0);
      _currentIndex--;
    }
  }
}

// Base class for undoable actions
abstract class UndoableAction {
  final String description;
  
  UndoableAction(this.description);
  
  Future<bool> execute();
  Future<bool> undo();
}

// Confirmation dialog service
class ConfirmationService {
  static Future<bool> confirm({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool isDestructive = false,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        isDestructive: isDestructive,
      ),
    );
    
    return result ?? false;
  }
  
  static Future<bool> confirmDelete({
    required BuildContext context,
    required String itemName,
    String? additionalMessage,
  }) async {
    return confirm(
      context: context,
      title: 'Delete $itemName',
      message: 'Are you sure you want to delete "$itemName"? This action cannot be undone.'
          '${additionalMessage != null ? '\n\n$additionalMessage' : ''}',
      confirmText: 'Delete',
      isDestructive: true,
    );
  }
  
  static Future<bool> confirmDiscard({
    required BuildContext context,
    String itemName = 'changes',
  }) async {
    return confirm(
      context: context,
      title: 'Discard Changes',
      message: 'You have unsaved $itemName. Are you sure you want to discard them?',
      confirmText: 'Discard',
      isDestructive: true,
    );
  }
}

// Confirmation dialog widget
class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final bool isDestructive;
  
  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText = 'Confirm',
    this.cancelText = 'Cancel',
    this.isDestructive = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(cancelText),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: isDestructive
              ? ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                )
              : null,
          child: Text(confirmText),
        ),
      ],
    );
  }
}

// Snackbar service for undo notifications
class UndoSnackbarService {
  static void showUndoSnackbar({
    required BuildContext context,
    required String message,
    required VoidCallback onUndo,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration,
        action: SnackBarAction(
          label: 'Undo',
          onPressed: onUndo,
        ),
      ),
    );
  }
  
  static void showActionSnackbar({
    required BuildContext context,
    required String message,
    String? actionLabel,
    VoidCallback? onAction,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration,
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                onPressed: onAction,
              )
            : null,
      ),
    );
  }
}

// Example undoable actions
class DeleteToolAction extends UndoableAction {
  final String toolId;
  final Map<String, dynamic> toolData;
  final Future<bool> Function(String) deleteFunction;
  final Future<bool> Function(Map<String, dynamic>) restoreFunction;
  
  DeleteToolAction({
    required this.toolId,
    required this.toolData,
    required this.deleteFunction,
    required this.restoreFunction,
  }) : super('Delete tool "${toolData['name']}"');
  
  @override
  Future<bool> execute() async {
    return await deleteFunction(toolId);
  }
  
  @override
  Future<bool> undo() async {
    return await restoreFunction(toolData);
  }
}

class UpdateToolAction extends UndoableAction {
  final String toolId;
  final Map<String, dynamic> oldData;
  final Map<String, dynamic> newData;
  final Future<bool> Function(String, Map<String, dynamic>) updateFunction;
  
  UpdateToolAction({
    required this.toolId,
    required this.oldData,
    required this.newData,
    required this.updateFunction,
  }) : super('Update tool "${newData['name']}"');
  
  @override
  Future<bool> execute() async {
    return await updateFunction(toolId, newData);
  }
  
  @override
  Future<bool> undo() async {
    return await updateFunction(toolId, oldData);
  }
}

class FavoriteToolAction extends UndoableAction {
  final String toolId;
  final bool isFavorited;
  final Future<bool> Function(String, bool) favoriteFunction;
  
  FavoriteToolAction({
    required this.toolId,
    required this.isFavorited,
    required this.favoriteFunction,
  }) : super(isFavorited ? 'Add to favorites' : 'Remove from favorites');
  
  @override
  Future<bool> execute() async {
    return await favoriteFunction(toolId, isFavorited);
  }
  
  @override
  Future<bool> undo() async {
    return await favoriteFunction(toolId, !isFavorited);
  }
}

// Action history widget
class ActionHistoryWidget extends StatefulWidget {
  const ActionHistoryWidget({super.key});
  
  @override
  State<ActionHistoryWidget> createState() => _ActionHistoryWidgetState();
}

class _ActionHistoryWidgetState extends State<ActionHistoryWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            IconButton(
              onPressed: ActionService.canUndo()
                  ? () async {
                      await ActionService.undo();
                      setState(() {});
                    }
                  : null,
              icon: const Icon(Icons.undo),
              tooltip: ActionService.canUndo()
                  ? 'Undo: ${ActionService.getLastActionDescription()}'
                  : 'Nothing to undo',
            ),
            IconButton(
              onPressed: ActionService.canRedo()
                  ? () async {
                      await ActionService.redo();
                      setState(() {});
                    }
                  : null,
              icon: const Icon(Icons.redo),
              tooltip: ActionService.canRedo()
                  ? 'Redo: ${ActionService.getNextActionDescription()}'
                  : 'Nothing to redo',
            ),
          ],
        ),
      ],
    );
  }
}
