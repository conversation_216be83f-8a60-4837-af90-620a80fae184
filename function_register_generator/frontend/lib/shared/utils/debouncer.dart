import 'dart:async';

class Debouncer {
  final Duration delay;
  Timer? _timer;
  
  Debouncer({
    this.delay = const Duration(milliseconds: 300),
  });
  
  void call(VoidCallback callback) {
    _timer?.cancel();
    _timer = Timer(delay, callback);
  }
  
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }
  
  void dispose() {
    cancel();
  }
}

class SearchDebouncer {
  final Duration delay;
  final Function(String) onSearch;
  Timer? _timer;
  
  SearchDebouncer({
    required this.onSearch,
    this.delay = const Duration(milliseconds: 500),
  });
  
  void search(String query) {
    _timer?.cancel();
    _timer = Timer(delay, () => onSearch(query));
  }
  
  void searchImmediate(String query) {
    _timer?.cancel();
    onSearch(query);
  }
  
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }
  
  void dispose() {
    cancel();
  }
}

class ThrottledFunction {
  final Duration duration;
  final VoidCallback function;
  Timer? _timer;
  bool _isThrottled = false;
  
  ThrottledFunction({
    required this.function,
    this.duration = const Duration(milliseconds: 100),
  });
  
  void call() {
    if (!_isThrottled) {
      function();
      _isThrottled = true;
      _timer = Timer(duration, () {
        _isThrottled = false;
      });
    }
  }
  
  void dispose() {
    _timer?.cancel();
  }
}

// Extension for easy debouncing
extension DebouncedCallback on VoidCallback {
  VoidCallback debounce([Duration delay = const Duration(milliseconds: 300)]) {
    Timer? timer;
    return () {
      timer?.cancel();
      timer = Timer(delay, this);
    };
  }
  
  VoidCallback throttle([Duration duration = const Duration(milliseconds: 100)]) {
    Timer? timer;
    bool isThrottled = false;
    
    return () {
      if (!isThrottled) {
        this();
        isThrottled = true;
        timer = Timer(duration, () {
          isThrottled = false;
        });
      }
    };
  }
}
