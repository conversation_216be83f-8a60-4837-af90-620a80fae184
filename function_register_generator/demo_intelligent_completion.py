#!/usr/bin/env python3
"""
智能补全服务演示

这个脚本演示了如何使用智能补全服务来自动生成工具定义，
集成了 cURL 解析器、Python 解析器和 LLM 服务。
"""

import asyncio
import json
import sys
import os

# 添加 backend 目录到 Python 路径
sys.path.append('backend')

from services.intelligent_completion_service import (
    IntelligentCompletionService,
    CompletionRequest,
    get_intelligent_completion_service
)
from services.llm_service import LMStudioService


class MockLLMService:
    """模拟 LLM 服务（用于演示）"""
    
    async def complete_tool_metadata(self, tool_info):
        """模拟元数据补全"""
        return {
            "success": True,
            "completions": {
                "category": "api" if tool_info.get("transport") == "http" else "utility",
                "descriptionUser": f"这是一个{tool_info.get('displayName', '工具')}",
                "descriptionDev": f"Technical description for {tool_info.get('displayName', 'tool')}",
                "capabilities": ["sync", "public"],
                "aliases": [tool_info.get('displayName', 'tool').lower()]
            }
        }
    
    async def generate_tool_from_description(self, description, examples=None):
        """模拟工具生成"""
        return {
            "success": True,
            "tool_definition": {
                "toolId": "generated.tool.example",
                "displayName": "生成的工具",
                "category": "utility",
                "transport": "python",
                "runtime": {
                    "transport": "python",
                    "entry_function": "main"
                },
                "inputsDeveloperSchema": {
                    "type": "object",
                    "required": ["input"],
                    "properties": {
                        "input": {"type": "string", "description": "输入参数"}
                    }
                },
                "outputsSchema": {
                    "type": "object",
                    "properties": {
                        "result": {"type": "string", "description": "处理结果"}
                    }
                },
                "descriptionUser": description
            }
        }
    
    async def generate_examples(self, tool_name, input_schema):
        """模拟示例生成"""
        return [
            {
                "userQuery": f"使用{tool_name}处理数据",
                "parsedInputs": {"input": "示例输入"}
            }
        ]


async def demo_curl_completion():
    """演示从 cURL 命令补全工具定义"""
    print("=" * 60)
    print("演示 1: 从 cURL 命令补全工具定义")
    print("=" * 60)
    
    # 创建智能补全服务（使用模拟 LLM）
    mock_llm = MockLLMService()
    completion_service = IntelligentCompletionService(mock_llm)
    
    # 测试 cURL 命令
    curl_command = '''curl -X GET "https://api.github.com/users/octocat" \
  -H "Accept: application/json" \
  -H "User-Agent: MyApp/1.0"'''
    
    print("输入的 cURL 命令:")
    print(curl_command)
    
    request = CompletionRequest(
        tool_name="GitHub 用户信息",
        curl_command=curl_command
    )
    
    try:
        result = await completion_service.complete_tool_definition(request)
        
        print(f"\n补全结果:")
        print(f"成功: {result.success}")
        print(f"来源: {result.source}")
        print(f"置信度: {result.confidence}")
        
        if result.success:
            print(f"\n生成的工具定义:")
            print(json.dumps(result.tool_definition, indent=2, ensure_ascii=False))
            
            print(f"\n改进建议:")
            for suggestion in result.suggestions:
                print(f"- {suggestion}")
        else:
            print(f"错误: {result.error}")
            
    except Exception as e:
        print(f"演示过程中出现错误: {e}")


async def demo_python_completion():
    """演示从 Python 代码补全工具定义"""
    print("\n" + "=" * 60)
    print("演示 2: 从 Python 代码补全工具定义")
    print("=" * 60)
    
    mock_llm = MockLLMService()
    completion_service = IntelligentCompletionService(mock_llm)
    
    python_code = '''
def calculate_bmi(weight: float, height: float) -> dict:
    """
    计算身体质量指数 (BMI)
    
    Args:
        weight (float): 体重（公斤）
        height (float): 身高（米）
    
    Returns:
        dict: 包含BMI值和健康状态的字典
    """
    if height <= 0 or weight <= 0:
        raise ValueError("身高和体重必须大于0")
    
    bmi = weight / (height ** 2)
    
    if bmi < 18.5:
        status = "偏瘦"
    elif bmi < 24:
        status = "正常"
    elif bmi < 28:
        status = "偏胖"
    else:
        status = "肥胖"
    
    return {
        "bmi": round(bmi, 2),
        "status": status,
        "weight": weight,
        "height": height
    }
'''
    
    print("输入的 Python 代码:")
    print(python_code)
    
    request = CompletionRequest(
        tool_name="BMI 计算器",
        python_code=python_code,
        function_name="calculate_bmi"
    )
    
    try:
        result = await completion_service.complete_tool_definition(request)
        
        print(f"\n补全结果:")
        print(f"成功: {result.success}")
        print(f"来源: {result.source}")
        print(f"置信度: {result.confidence}")
        
        if result.success:
            print(f"\n生成的工具定义:")
            print(json.dumps(result.tool_definition, indent=2, ensure_ascii=False))
            
            print(f"\n改进建议:")
            for suggestion in result.suggestions:
                print(f"- {suggestion}")
        else:
            print(f"错误: {result.error}")
            
    except Exception as e:
        print(f"演示过程中出现错误: {e}")


async def demo_description_completion():
    """演示从自然语言描述补全工具定义"""
    print("\n" + "=" * 60)
    print("演示 3: 从自然语言描述补全工具定义")
    print("=" * 60)
    
    mock_llm = MockLLMService()
    completion_service = IntelligentCompletionService(mock_llm)
    
    description = "创建一个工具来生成随机密码，可以指定密码长度和是否包含特殊字符"
    test_inputs = {
        "length": 12,
        "include_special": True
    }
    test_outputs = {
        "password": "Abc123!@#xyz",
        "strength": "strong"
    }
    
    print(f"自然语言描述: {description}")
    print(f"测试输入: {json.dumps(test_inputs, ensure_ascii=False)}")
    print(f"测试输出: {json.dumps(test_outputs, ensure_ascii=False)}")
    
    request = CompletionRequest(
        description=description,
        test_inputs=test_inputs,
        test_outputs=test_outputs
    )
    
    try:
        result = await completion_service.complete_tool_definition(request)
        
        print(f"\n补全结果:")
        print(f"成功: {result.success}")
        print(f"来源: {result.source}")
        print(f"置信度: {result.confidence}")
        
        if result.success:
            print(f"\n生成的工具定义:")
            print(json.dumps(result.tool_definition, indent=2, ensure_ascii=False))
            
            print(f"\n改进建议:")
            for suggestion in result.suggestions:
                print(f"- {suggestion}")
        else:
            print(f"错误: {result.error}")
            
    except Exception as e:
        print(f"演示过程中出现错误: {e}")


async def demo_partial_completion():
    """演示从部分信息补全工具定义"""
    print("\n" + "=" * 60)
    print("演示 4: 从部分信息补全工具定义")
    print("=" * 60)
    
    mock_llm = MockLLMService()
    completion_service = IntelligentCompletionService(mock_llm)
    
    request = CompletionRequest(
        tool_name="天气查询 API",
        transport="http",
        endpoint="https://api.openweathermap.org/data/2.5/weather",
        description="查询指定城市的当前天气信息"
    )
    
    print("输入的部分信息:")
    print(f"工具名称: {request.tool_name}")
    print(f"传输方式: {request.transport}")
    print(f"端点: {request.endpoint}")
    print(f"描述: {request.description}")
    
    try:
        result = await completion_service.complete_tool_definition(request)
        
        print(f"\n补全结果:")
        print(f"成功: {result.success}")
        print(f"来源: {result.source}")
        print(f"置信度: {result.confidence}")
        
        if result.success:
            print(f"\n生成的工具定义:")
            print(json.dumps(result.tool_definition, indent=2, ensure_ascii=False))
            
            print(f"\n改进建议:")
            for suggestion in result.suggestions:
                print(f"- {suggestion}")
        else:
            print(f"错误: {result.error}")
            
    except Exception as e:
        print(f"演示过程中出现错误: {e}")


async def demo_utility_functions():
    """演示工具函数"""
    print("\n" + "=" * 60)
    print("演示 5: 工具函数展示")
    print("=" * 60)
    
    completion_service = IntelligentCompletionService()
    
    # 测试工具ID生成
    print("工具ID生成:")
    test_cases = [
        ("GitHub API", "http"),
        ("calculate_area", "python"),
        ("File Manager", "mcp"),
        ("Test-Tool@123", "http")
    ]
    
    for name, transport in test_cases:
        tool_id = completion_service._generate_tool_id(name, transport)
        print(f"  {name} ({transport}) -> {tool_id}")
    
    # 测试URL名称提取
    print("\nURL名称提取:")
    urls = [
        "https://api.github.com/users/octocat",
        "https://httpbin.org/get",
        "https://www.example.com/api/v1/data"
    ]
    
    for url in urls:
        name = completion_service._extract_name_from_url(url)
        print(f"  {url} -> {name}")
    
    # 测试数据类型推断
    print("\n数据类型推断:")
    test_data = {
        "name": "John Doe",
        "age": 30,
        "active": True,
        "scores": [85, 90, 78],
        "profile": {"city": "Beijing", "country": "China"},
        "balance": 1234.56
    }
    
    schema = completion_service._infer_schema_from_data(test_data)
    print("  测试数据:", json.dumps(test_data, ensure_ascii=False))
    print("  推断的Schema:")
    print(json.dumps(schema, indent=4, ensure_ascii=False))


async def main():
    """主函数"""
    print("智能补全服务演示")
    print("这个演示展示了如何使用智能补全服务自动生成工具定义")
    
    try:
        await demo_curl_completion()
        await demo_python_completion()
        await demo_description_completion()
        await demo_partial_completion()
        await demo_utility_functions()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\n智能补全服务的主要功能:")
        print("✓ 从 cURL 命令自动生成 HTTP API 工具定义")
        print("✓ 从 Python 代码自动生成脚本工具定义")
        print("✓ 从自然语言描述生成完整工具定义")
        print("✓ 从部分信息智能补全工具定义")
        print("✓ 集成 LLM 服务提供智能建议")
        print("✓ 自动生成使用示例和改进建议")
        print("✓ 支持多种传输方式和工具类型")
        print("✓ 提供置信度评估和错误处理")
        
        print("\n支持的补全模式:")
        print("• cURL 模式: 解析 cURL 命令 -> HTTP API 工具")
        print("• Python 模式: 分析 Python 函数 -> 脚本工具")
        print("• LLM 模式: 自然语言描述 -> 完整工具定义")
        print("• 混合模式: 部分信息 + LLM 增强 -> 补全工具定义")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())