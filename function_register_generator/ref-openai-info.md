太好了，这里给你一份**落地方案 + 最小可执行样例**，围绕你已有的 ES 索引（`mcp_tools_guangfan`）做一套“公司内工具注册平台”，覆盖你提的 6 点诉求：

1. 表单/JSON 注册；2) 仅填关键信息，其他交给大模型补全 + 沙盒验证；3) 支持 MCP server 导入与注册；4) 面向零代码用户的问答式生成与注册；5) 关键词/语义检索；6) 大模型友好。

---

# 总览：一张图的流程

**输入（表单/JSON/cURL/脚本/描述） → 解析/补全（LLM） → 沙盒试跑（curl/python/MCP） → 抽取 I/O Schema → 生成元数据（content/emb） → 写 ES → 搜索/调用**

---

# A. “最小必填”清单（降门槛）

## 1) HTTP 工具（从 cURL 或表单）

* `displayName`
* `runtime.transport="http"`
* `runtime.endpoint`（URL）
* `runtime.httpMethod`（GET/POST）
* `inputsDeveloperSchema`（至少把**必填**参数列出）
* `metadata.user_facing=false`
* `metadata.has_variables_dependencies=false`

> 任选其一：用户可只给**一条可调用示例**（cURL 或最短 Python requests），平台自动抽取并补全其它字段。

## 2) 本地 Python 脚本

* `displayName`
* `runtime.transport="python"`
* （二选一）`runtime.script_path` **或** `runtime.script_content(base64)`
* `runtime.entry_function`
* `inputsDeveloperSchema`
* `metadata.user_facing=false`
* `metadata.has_variables_dependencies=false`

## 3) MCP Server（导入注册）

* `mcp.transport`：`stdio` / `http`
* `mcp.command` + `args`（若 stdio）或 `mcp.url`（若 http）
* （可选）`env` / `headers`
* 平台连上 MCP，拉取 tools 列表 & 各自的 JSON Schema → 映射为 ES 文档

---

# B. 后端接口设计（最小 MVP）

## 1) 注册（表单/JSON）

```
POST /tools/register
Body: { mode: "http"|"python", source: "form"|"curl"|"python", payload: {...} }
→ 解析/补全/沙盒试跑 → ES 写入 → 返回 {doc_id, toolId}
```

## 2) 由 cURL 注册（免表单）

```
POST /tools/register-from-curl
Body: { curl: "curl -X POST https://... -H 'Content-Type: application/json' -d '{...}'" }
→ 抽取 endpoint/method/headers/body → 生成 inputs schema（LLM/启发式）→ 试跑 → 写 ES
```

## 3) 由 Python 脚本注册

```
POST /tools/register-python
Body: {
  displayName, entry_function,
  script_path? | script_b64?,
  sample_call?: {"args": {...}},
  minimal_inputs_schema?: {...}
}
→ 沙盒执行 -> 抽取 outputs schema -> 写 ES
```

## 4) MCP 导入注册

```
POST /tools/import-mcp
Body: { transport: "stdio"|"http", command?, args?, url?, env?, headers? }
→ 握手 -> tools.list -> for each tool: 映射为 ES 文档 -> 批量写入
```

## 5) 搜索

```
GET /tools/search?q=天气&transport=http&user_facing=false
→ 关键词 + （可选）向量检索
```

---

# C. LLM 补全与沙盒验证

## 1) 补全策略（示例）

* **从 cURL 抽取**：`endpoint`、`httpMethod`、`headers`、`body` 字段；body 里每个 key → 生成 `inputsDeveloperSchema.properties`；如果 body 有值 → 作为默认值或示例值；将 `msg` 这类文案参数标注为 `required`。
* **从 Python 函数抽取**：解析函数签名（参数名、缺省值、类型注释）；无法判定类型时降级为 `string`。
* **从 MCP 工具抽取**：MCP 通常自带 JSON Schema（输入/输出），直接映射。
* **自动生成**：`toolId`（displayName 归一化）/ `content`（聚合 name/desc/aliases）/ 缺省 `visibility=public`/ `runtime.fault_tolerance=low` 等。

## 2) 沙盒

* **HTTP**：在受限网段执行 `curl` / `requests`，白名单域名、超时（如 10s）、最大响应体（如 256KB），不透传生产密钥；支持 TLS 验证开关（等价 `-k`）。
* **Python**：在容器/微 VM 跑独立解释器，限制文件、网络、CPU/内存；仅允许导入标准库或白名单包。
* **MCP**：独立进程/容器握手，超时与重启策略，拉取工具清单后关闭。

---

# D. 你能“现在就用”的后端最小样例（FastAPI + requests）

> 下面两段：**从 cURL 注册 HTTP 工具**、**从 Python 脚本注册时间工具**。它们会把文档写到 `mcp_tools_guangfan`。把 `ES_HOST/ES_USER/ES_PASS` 替成你的即可。

### 1) 从 cURL 注册（真实获取天气：Open-Meteo）

```python
# file: register_from_curl.py
import re, json, shlex, requests
from requests.auth import HTTPBasicAuth

ES_HOST, ES_USER, ES_PASS = "http://ip:9200", "elastic", "密码"
INDEX = "mcp_tools_guangfan"

def parse_curl(curl: str):
    # 仅处理最常见：-X/-H/-d，JSON body
    tokens = shlex.split(curl)
    url, method, headers, body = None, "GET", {}, None
    it = iter(range(len(tokens)))
    for i in it:
        t = tokens[i]
        if t.lower() == "curl": continue
        if t == "-X": method = tokens[next(it)]
        elif t == "-H":
            h = tokens[next(it)]
            k,v = h.split(":",1); headers[k.strip()] = v.strip()
        elif t in ("-d","--data","--data-raw","--data-binary"):
            body = tokens[next(it)]
        elif t.startswith("http"):
            url = t
    return url, method or "GET", headers, json.loads(body) if body else {}

def build_inputs_schema_from_body(body: dict):
    props, required = {}, []
    for k,v in body.items():
        typ = "string"
        if isinstance(v, bool): typ = "boolean"
        elif isinstance(v, (int,float)): typ = "number"
        props[k] = {"type": typ}
        if k in ("msg","latitude","longitude"): required.append(k)
    return {"type":"object","required": required or list(body.keys()), "properties": props}

def register_http_tool_from_curl(display_name: str, curl: str):
    url, method, headers, body = parse_curl(curl)
    doc = {
        "toolId": re.sub(r"[^a-z0-9]+",".",display_name.lower()).strip("."),
        "displayName": display_name,
        "descriptionUser": f"从 cURL 自动注册的 HTTP 工具：{url}",
        "runtime": {
            "transport": "http",
            "endpoint": url,
            "httpMethod": method,
            "auth": {"type": "none"},
            "fault_tolerance": "low"
        },
        "metadata": {"user_facing": False, "has_variables_dependencies": False},
        "inputsDeveloperSchema": build_inputs_schema_from_body(body),
        "quickstarts": [
            {"kind":"curl","title":"示例调用","content":curl}
        ],
        "outputsSchema": {"type":"object"}
    }
    r = requests.post(f"{ES_HOST}/{INDEX}/_doc", auth=HTTPBasicAuth(ES_USER, ES_PASS),
                      headers={"Content-Type":"application/json"}, json=doc, timeout=15)
    print(r.status_code, r.text)

if __name__ == "__main__":
    register_http_tool_from_curl(
        "天气查询（Open-Meteo）",
        "curl -X GET 'https://api.open-meteo.com/v1/forecast?latitude=25.0478&longitude=121.5319&hourly=temperature_2m'"
    )
```

### 2) 从 Python 脚本注册（获取当前时间）

```python
# file: register_python_now.py
import base64, json, re, requests
from requests.auth import HTTPBasicAuth

ES_HOST, ES_USER, ES_PASS = "http://ip:9200", "elastic", "密码"
INDEX = "mcp_tools_guangfan"

PY_CODE = r"""
def now(tz: str = "Asia/Taipei"):
    import datetime, zoneinfo
    z = zoneinfo.ZoneInfo(tz)
    return {"now": datetime.datetime.now(z).isoformat()}
"""

def register_python_tool(display_name: str, entry="now", code=PY_CODE):
    b64 = base64.b64encode(code.encode()).decode()
    tool_id = re.sub(r"[^a-z0-9]+",".",display_name.lower()).strip(".")
    doc = {
        "toolId": tool_id,
        "displayName": display_name,
        "descriptionUser": "返回当前时间；IANA 时区可选。",
        "runtime": {
            "transport": "python",
            "script_content": b64,
            "entry_function": entry,
            "fault_tolerance": "high"
        },
        "metadata": {"user_facing": False, "has_variables_dependencies": False},
        "inputsDeveloperSchema": {
            "type":"object",
            "required": [],
            "properties": { "tz": { "type":"string", "description":"Asia/Taipei 等" } }
        },
        "outputsSchema": { "type":"object", "required":["now"], "properties":{"now":{"type":"string"}} },
        "quickstarts":[{"kind":"python","title":"示例调用","content":"result = now('Asia/Taipei')"}]
    }
    r = requests.post(f"{ES_HOST}/{INDEX}/_doc", auth=HTTPBasicAuth(ES_USER, ES_PASS),
                      headers={"Content-Type":"application/json"}, json=doc, timeout=15)
    print(r.status_code, r.text)

if __name__ == "__main__":
    register_python_tool("当前时间（本地脚本）")
```

---

# E. MCP Server 导入（思路与映射）

* **连接**：

  * `stdio`：拉起进程 `command + args`；
  * `http`：对 `url` 发 MCP 握手/能力探测请求。
* **获取工具**：调用 MCP 的工具枚举（例如 `tools/list`）。
* **映射**：

  * `toolId` ← MCP 工具名
  * `displayName/descriptionUser` ← MCP 描述
  * `inputsDeveloperSchema` / `outputsSchema` ← MCP 的 JSON Schema
  * `runtime.transport` ← `"stdio"` 或 `"http"`（也可单独放 `mcp.*` 字段）
* **批量写 ES**：把每个 MCP 工具保存为一条文档；`quickstarts` 写入建议的调用方式（比如 stdio 启动命令）。

> 需要我给一个最小的“`/tools/import-mcp` FastAPI 端点”示例骨架也可以加上。

---

# F. 面向零代码用户的注册向导（No-code Flow）

1. **问题描述**：用户用自然语言说明“这个工具做什么”；可贴一条“能调用的 cURL/脚本”。
2. **生成草稿**（LLM）：产出 `displayName`、`toolId`、`descriptionUser`、`inputsDeveloperSchema`、`runtime.*`（若能从 cURL/脚本推断）。
3. **一键试跑（沙盒）**：使用给的示例入参执行，抓取返回样例，自动推断 `outputsSchema`（按 JSON shape 生成）。
4. **确认**：展示“工具卡片预览 + 可调用示例（curl/python）”。
5. **写 ES**：将文档落库；如果成功，弹出“复制调用示例”。

---

# G. 搜索（关键词 + 语义）

* 关键词：`multi_match` 命中 `displayName^3 + descriptionUser^2 + content`。
* 筛选：`runtime.transport`、`category`、`visibility`、`metadata.user_facing` 等。
* 语义（可选）：若你已有向量，`descriptionEmb` 的 kNN 召回 + 关键词 rerank（我们之前给了 cURL）。

---

# H. 大模型友好（Function-Calling 口径）

* **JSON Schema 完整**：`type/required/properties/enum/range` 尽量齐全；
* **示例**：`examples[*]` 提供 1–3 条“用户问法 → 解析入参 → 示例调用”；
* **错误**：`errors[*]` 至少放常见 2–3 条（如超时、鉴权、参数缺失）；
* **quickstarts**：各保留一条 `curl` 与 `python`；
* **content**：把名称/别名/用户描述拼入，便于 BM25 兜底和向量摘要生成。

---

需要的话，我可以把上面的**四个后端端点**（`/tools/register`、`/tools/register-from-curl`、`/tools/register-python`、`/tools/search`）做成一份**可运行的 FastAPI 最小项目**骨架（含 ES 写入、简易沙盒 stub），你拿去直接起服务就能用。
