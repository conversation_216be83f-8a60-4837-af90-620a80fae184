# 调试指南 - 工具注册平台

## 🔍 快速调试检查清单

### 启动前检查
- [ ] 数据库服务是否运行 (PostgreSQL)
- [ ] Redis 服务是否运行
- [ ] 环境变量是否正确配置
- [ ] 依赖包是否已安装
- [ ] 端口 8000 (后端) 和 8080 (前端) 是否可用

### 常见问题排查

#### 🔴 后端启动失败
```bash
# 检查数据库连接
python -c "import psycopg2; psycopg2.connect('postgresql://user:pass@localhost/db')"

# 检查依赖
cd backend && pip install -r requirements.txt

# 检查配置
python -c "from app.core.config import settings; print(settings.DATABASE_URL)"

# 启动调试模式
cd backend && python -m uvicorn main:app --reload --log-level debug
```

#### 🔴 前端构建失败
```bash
# 清理缓存
cd frontend && flutter clean && flutter pub get

# 检查 Flutter 版本
flutter --version

# 检查依赖冲突
flutter pub deps

# 构建调试版本
flutter run -d web-server --web-port 8080
```

#### 🔴 数据库连接问题
```bash
# 检查 PostgreSQL 状态
sudo systemctl status postgresql

# 测试连接
psql -h localhost -U username -d database_name

# 检查权限
sudo -u postgres psql -c "\du"
```

## 🛠️ 调试工具和技巧

### 后端调试

#### 1. 日志调试
```python
# 在代码中添加调试日志
import logging
logger = logging.getLogger(__name__)

logger.debug("Debug message")
logger.info("Info message")
logger.error("Error message")
```

#### 2. 断点调试
```python
# 使用 pdb 调试器
import pdb; pdb.set_trace()

# 或使用 ipdb (更友好的界面)
import ipdb; ipdb.set_trace()
```

#### 3. API 测试
```bash
# 使用 curl 测试 API
curl -X GET "http://localhost:8000/api/v1/tools" -H "Content-Type: application/json"

# 使用 httpie (更友好)
http GET localhost:8000/api/v1/tools

# 测试 Function Calling
curl -X POST "http://localhost:8000/api/v1/function-calling/call" \
  -H "Content-Type: application/json" \
  -d '{"tool_id": "test-tool", "arguments": {"input": "test"}}'
```

### 前端调试

#### 1. Flutter DevTools
```bash
# 启动 DevTools
flutter pub global activate devtools
flutter pub global run devtools

# 在浏览器中打开 DevTools
# 通常在 http://localhost:9100
```

#### 2. 浏览器开发者工具
- **Console**: 查看 JavaScript 错误和日志
- **Network**: 监控 API 请求和响应
- **Performance**: 分析渲染性能
- **Application**: 检查本地存储和缓存

#### 3. Flutter 调试命令
```bash
# 热重载
r

# 热重启
R

# 显示性能覆盖
p

# 显示 widget 检查器
w
```

### 数据库调试

#### 1. 查询分析
```sql
-- 启用查询日志
ALTER SYSTEM SET log_statement = 'all';
SELECT pg_reload_conf();

-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 分析查询计划
EXPLAIN ANALYZE SELECT * FROM tools WHERE category = 'api';
```

#### 2. 连接监控
```sql
-- 查看当前连接
SELECT * FROM pg_stat_activity;

-- 查看数据库大小
SELECT pg_size_pretty(pg_database_size('database_name'));
```

## 🧪 测试调试

### 运行特定测试
```bash
# 运行单个测试文件
python -m pytest tests/test_specific.py -v

# 运行特定测试函数
python -m pytest tests/test_file.py::test_function -v

# 运行带标记的测试
python -m pytest -m "slow" -v

# 运行失败的测试
python -m pytest --lf -v
```

### 测试覆盖率
```bash
# 生成覆盖率报告
python -m pytest --cov=app --cov-report=html

# 查看未覆盖的行
python -m pytest --cov=app --cov-report=term-missing
```

### 前端测试调试
```bash
# 运行特定测试
flutter test test/specific_test.dart

# 运行测试并显示详细输出
flutter test --verbose

# 运行测试并生成覆盖率
flutter test --coverage
```

## 🔧 性能调试

### 后端性能
```python
# 使用 cProfile 分析性能
python -m cProfile -o profile.stats main.py

# 分析结果
python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(10)"

# 使用 line_profiler
@profile
def slow_function():
    # 代码
    pass

# 运行: kernprof -l -v script.py
```

### 前端性能
```bash
# Flutter 性能分析
flutter run --profile

# 生成性能报告
flutter build web --profile --dart-define=FLUTTER_WEB_USE_SKIA=true
```

### 数据库性能
```sql
-- 查看索引使用情况
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename = 'tools';

-- 查看表统计信息
SELECT * FROM pg_stat_user_tables WHERE relname = 'tools';
```

## 🚨 错误处理和监控

### 错误日志分析
```bash
# 查看应用日志
tail -f logs/app.log

# 过滤错误日志
grep "ERROR" logs/app.log | tail -20

# 分析错误模式
awk '/ERROR/ {print $0}' logs/app.log | sort | uniq -c | sort -nr
```

### 健康检查
```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查数据库连接
curl http://localhost:8000/health/db

# 检查 Redis 连接
curl http://localhost:8000/health/redis
```

## 📊 监控和告警

### 应用监控
```python
# 添加自定义指标
from prometheus_client import Counter, Histogram

REQUEST_COUNT = Counter('requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('request_duration_seconds', 'Request latency')

@REQUEST_LATENCY.time()
def process_request():
    REQUEST_COUNT.labels(method='GET', endpoint='/api/v1/tools').inc()
    # 处理请求
```

### 数据库监控
```sql
-- 监控连接数
SELECT count(*) FROM pg_stat_activity;

-- 监控锁等待
SELECT * FROM pg_locks WHERE NOT granted;

-- 监控复制延迟
SELECT * FROM pg_stat_replication;
```

## 🔍 故障排除流程

### 1. 问题识别
- 收集错误信息和日志
- 确定问题的范围和影响
- 记录重现步骤

### 2. 初步诊断
- 检查服务状态
- 验证配置设置
- 查看资源使用情况

### 3. 深入分析
- 分析日志文件
- 检查数据库状态
- 监控网络连接

### 4. 解决方案
- 应用临时修复
- 实施永久解决方案
- 验证修复效果

### 5. 预防措施
- 更新监控规则
- 改进错误处理
- 添加自动化测试

## 📞 获取帮助

### 内部资源
- 查看 `DEBUG_PLAN.md` 了解详细调试计划
- 运行 `python run_tests.py --help` 查看测试选项
- 查看 API 文档: http://localhost:8000/docs

### 外部资源
- [FastAPI 调试指南](https://fastapi.tiangolo.com/tutorial/debugging/)
- [Flutter 调试指南](https://docs.flutter.dev/testing/debugging)
- [PostgreSQL 性能调优](https://wiki.postgresql.org/wiki/Performance_Optimization)

### 社区支持
- GitHub Issues: 报告 bug 和功能请求
- Stack Overflow: 技术问题讨论
- Discord/Slack: 实时技术支持

## 🎯 最佳实践

### 开发环境
- 使用虚拟环境隔离依赖
- 配置 IDE 调试器
- 启用自动代码格式化
- 使用版本控制钩子

### 测试策略
- 编写测试先于实现 (TDD)
- 保持高测试覆盖率
- 定期运行完整测试套件
- 使用持续集成

### 监控和日志
- 结构化日志格式
- 合理的日志级别
- 定期清理日志文件
- 设置告警阈值

### 性能优化
- 定期性能基准测试
- 监控关键指标
- 优化数据库查询
- 使用缓存策略
