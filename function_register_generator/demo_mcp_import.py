#!/usr/bin/env python3
"""
MCP 导入功能演示脚本
"""
import asyncio
import json
from datetime import datetime

async def demo_mcp_import():
    """演示 MCP 导入功能"""
    print("=== MCP 工具导入平台演示 ===")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 导入必要的模块
    try:
        from backend.routers.tools import (
            import_mcp_server_advanced, discover_mcp_tools,
            get_mcp_import_progress, MCPImportAdvancedRequest,
            _filter_tools_for_import, _create_mcp_tool_definition,
            _format_description, _generate_mcp_import_suggestions
        )
        print("✅ 成功导入 MCP 导入模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    print("\n" + "="*60)
    print("1. MCP 工具发现演示")
    print("="*60)
    
    # 1. 工具发现
    discovery_requests = [
        {
            "name": "文件系统 MCP 服务器 (stdio)",
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="uvx",
                args=["mcp-server-filesystem"],
                env={"ALLOWED_DIRECTORIES": "/tmp,/home/<USER>/documents"}
            )
        },
        {
            "name": "GitHub MCP 服务器 (HTTP)",
            "request": MCPImportAdvancedRequest(
                transport="http",
                url="http://localhost:3000/mcp"
            )
        },
        {
            "name": "数据库 MCP 服务器 (stdio)",
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="python",
                args=["-m", "mcp_server_sqlite"],
                env={"DATABASE_PATH": "/tmp/test.db"}
            )
        }
    ]
    
    for case in discovery_requests:
        print(f"\n📡 发现 {case['name']} 的工具:")
        try:
            result = await discover_mcp_tools(case["request"])
            print(f"   ✅ 连接成功")
            print(f"   服务器: {result['server_info'].get('server_name', 'Unknown')}")
            print(f"   发现工具数量: {result['total_tools']}")
            
            for tool in result["discovered_tools"][:3]:  # 只显示前3个
                print(f"   - {tool['name']}: {tool['description']}")
                
            if result['total_tools'] > 3:
                print(f"   ... 还有 {result['total_tools'] - 3} 个工具")
                
        except Exception as e:
            print(f"   ❌ 发现失败: {e}")
    
    print("\n" + "="*60)
    print("2. 完整 MCP 导入演示")
    print("="*60)
    
    # 2. 完整导入
    full_import_request = MCPImportAdvancedRequest(
        transport="stdio",
        command="uvx",
        args=["mcp-server-filesystem"],
        env={"ALLOWED_DIRECTORIES": "/tmp"},
        import_all=True,
        tool_prefix="fs",
        category_override="filesystem",
        description_template="文件系统工具: {description}",
        validate_tools=True,
        generate_examples=True,
        optimize_schemas=True,
        async_import=False
    )
    
    try:
        print("🚀 开始完整 MCP 导入...")
        result = await import_mcp_server_advanced(full_import_request)
        
        print(f"✅ 导入完成:")
        print(f"   导入ID: {result.import_id}")
        print(f"   状态: {result.progress.status}")
        print(f"   进度: {result.progress.progress * 100:.1f}%")
        print(f"   发现工具: {result.total_discovered} 个")
        print(f"   成功导入: {result.total_imported} 个")
        print(f"   导入失败: {result.total_failed} 个")
        print(f"   连接时间: {result.connection_time_ms}ms")
        
        if result.imported_tools:
            print(f"\n   成功导入的工具:")
            for tool in result.imported_tools[:5]:
                print(f"   ✅ {tool['tool_id']}: {tool['name']}")
        
        if result.failed_tools:
            print(f"\n   导入失败的工具:")
            for tool in result.failed_tools:
                print(f"   ❌ {tool['name']}: {tool['error']}")
        
        if result.suggestions:
            print(f"\n   改进建议:")
            for suggestion in result.suggestions:
                print(f"   💡 {suggestion}")
        
        if result.warnings:
            print(f"\n   警告信息:")
            for warning in result.warnings:
                print(f"   ⚠️ {warning}")
                
    except Exception as e:
        print(f"❌ 完整导入失败: {e}")
    
    print("\n" + "="*60)
    print("3. 选择性 MCP 导入演示")
    print("="*60)
    
    # 3. 选择性导入
    selective_import_request = MCPImportAdvancedRequest(
        transport="stdio",
        command="uvx",
        args=["mcp-server-git"],
        import_all=False,
        selected_tools=["git_status", "git_log", "git_diff"],
        tool_prefix="git",
        category_override="version_control",
        description_template="Git 工具: {description}",
        validate_tools=False,  # 跳过验证以加快演示
        generate_examples=True,
        optimize_schemas=False
    )
    
    try:
        print("🎯 开始选择性 MCP 导入...")
        result = await import_mcp_server_advanced(selective_import_request)
        
        print(f"✅ 选择性导入完成:")
        print(f"   选择导入: {len(selective_import_request.selected_tools)} 个工具")
        print(f"   实际导入: {result.total_imported} 个")
        print(f"   工具前缀: {selective_import_request.tool_prefix}")
        print(f"   自定义分类: {selective_import_request.category_override}")
        
        for tool in result.imported_tools:
            print(f"   📦 {tool['tool_id']}")
            
    except Exception as e:
        print(f"❌ 选择性导入失败: {e}")
    
    print("\n" + "="*60)
    print("4. 工具过滤和配置演示")
    print("="*60)
    
    # 4. 工具过滤演示
    mock_discovered_tools = [
        {"name": "read_file", "description": "读取文件内容"},
        {"name": "write_file", "description": "写入文件内容"},
        {"name": "list_directory", "description": "列出目录内容"},
        {"name": "delete_file", "description": "删除文件"},
        {"name": "create_directory", "description": "创建目录"}
    ]
    
    filter_scenarios = [
        {
            "name": "导入全部工具",
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="test",
                import_all=True
            )
        },
        {
            "name": "只导入读写操作",
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="test",
                import_all=False,
                selected_tools=["read_file", "write_file"]
            )
        },
        {
            "name": "导入安全操作（排除删除）",
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="test",
                import_all=False,
                selected_tools=["read_file", "write_file", "list_directory", "create_directory"]
            )
        }
    ]
    
    for scenario in filter_scenarios:
        filtered_tools = _filter_tools_for_import(mock_discovered_tools, scenario["request"])
        print(f"\n🔍 {scenario['name']}:")
        print(f"   原始工具数: {len(mock_discovered_tools)}")
        print(f"   过滤后数量: {len(filtered_tools)}")
        print(f"   工具列表: {', '.join(tool['name'] for tool in filtered_tools)}")
    
    print("\n" + "="*60)
    print("5. 工具定义生成演示")
    print("="*60)
    
    # 5. 工具定义生成
    sample_tool_info = {
        "name": "search_files",
        "description": "在指定目录中搜索文件",
        "input_schema": {
            "type": "object",
            "properties": {
                "directory": {"type": "string", "description": "搜索目录"},
                "pattern": {"type": "string", "description": "搜索模式"},
                "recursive": {"type": "boolean", "description": "是否递归搜索", "default": True}
            },
            "required": ["directory", "pattern"]
        }
    }
    
    customization_cases = [
        {
            "name": "基础配置",
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="mcp-server-filesystem"
            ),
            "server_info": {"transport": "stdio"}
        },
        {
            "name": "自定义前缀和分类",
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="mcp-server-filesystem",
                tool_prefix="fs_advanced",
                category_override="file_management"
            ),
            "server_info": {"transport": "stdio"}
        },
        {
            "name": "自定义描述模板",
            "request": MCPImportAdvancedRequest(
                transport="http",
                url="http://localhost:3000/mcp",
                description_template="🔧 高级工具: {description}"
            ),
            "server_info": {"transport": "http"}
        }
    ]
    
    for case in customization_cases:
        print(f"\n⚙️ {case['name']}:")
        try:
            tool_def = await _create_mcp_tool_definition(
                sample_tool_info, 
                case["request"], 
                case["server_info"]
            )
            
            print(f"   工具ID: {tool_def['toolId']}")
            print(f"   显示名称: {tool_def['displayName']}")
            print(f"   分类: {tool_def['category']}")
            print(f"   描述: {tool_def['descriptionUser']}")
            print(f"   传输方式: {tool_def['transport']}")
            print(f"   别名: {', '.join(tool_def['aliases'])}")
            
        except Exception as e:
            print(f"   ❌ 生成失败: {e}")
    
    print("\n" + "="*60)
    print("6. 描述格式化演示")
    print("="*60)
    
    # 6. 描述格式化
    format_cases = [
        {
            "description": "读取文件内容",
            "template": None,
            "expected": "读取文件内容"
        },
        {
            "description": "写入文件内容",
            "template": "📁 文件操作: {description}",
            "expected": "📁 文件操作: 写入文件内容"
        },
        {
            "description": "执行数据库查询",
            "template": "🗄️ 数据库工具: {description} | 安全级别: 高",
            "expected": "🗄️ 数据库工具: 执行数据库查询 | 安全级别: 高"
        }
    ]
    
    for case in format_cases:
        result = _format_description(case["description"], case["template"])
        status = "✅" if result == case["expected"] else "❌"
        print(f"   {status} 原始: '{case['description']}'")
        print(f"      模板: {case['template'] or '无'}")
        print(f"      结果: '{result}'")
    
    print("\n" + "="*60)
    print("7. 导入建议生成演示")
    print("="*60)
    
    # 7. 建议生成
    suggestion_scenarios = [
        {
            "name": "完美导入",
            "imported": [{"name": f"tool{i}"} for i in range(5)],
            "failed": [],
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="test",
                validate_tools=True,
                generate_examples=True
            )
        },
        {
            "name": "部分失败",
            "imported": [{"name": f"tool{i}"} for i in range(3)],
            "failed": [{"name": "failed_tool1"}, {"name": "failed_tool2"}],
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="test",
                validate_tools=False,
                generate_examples=False
            )
        },
        {
            "name": "大量工具导入",
            "imported": [{"name": f"tool{i}"} for i in range(15)],
            "failed": [],
            "request": MCPImportAdvancedRequest(
                transport="stdio",
                command="test",
                validate_tools=True,
                generate_examples=True
            )
        }
    ]
    
    for scenario in suggestion_scenarios:
        suggestions = _generate_mcp_import_suggestions(
            scenario["imported"],
            scenario["failed"],
            scenario["request"]
        )
        
        print(f"\n💡 {scenario['name']}:")
        print(f"   成功: {len(scenario['imported'])}, 失败: {len(scenario['failed'])}")
        if suggestions:
            for suggestion in suggestions:
                print(f"   - {suggestion}")
        else:
            print("   - 无特殊建议")
    
    print("\n" + "="*60)
    print("8. 异步导入和进度跟踪演示")
    print("="*60)
    
    # 8. 异步导入演示（模拟）
    print("🔄 异步导入演示:")
    print("   1. 提交异步导入请求")
    print("   2. 获取导入ID: mcp_import_abc12345")
    print("   3. 轮询导入进度...")
    
    # 模拟进度状态
    progress_states = [
        {"status": "pending", "progress": 0.0, "step": "等待开始"},
        {"status": "connecting", "progress": 0.1, "step": "连接 MCP 服务器"},
        {"status": "discovering", "progress": 0.3, "step": "发现可用工具"},
        {"status": "importing", "progress": 0.5, "step": "导入工具: read_file"},
        {"status": "importing", "progress": 0.7, "step": "导入工具: write_file"},
        {"status": "importing", "progress": 0.9, "step": "导入工具: list_directory"},
        {"status": "completed", "progress": 1.0, "step": "导入完成"}
    ]
    
    for i, state in enumerate(progress_states):
        print(f"   [{i+1}/7] {state['status'].upper()}: {state['progress']*100:.0f}% - {state['step']}")
        if i < len(progress_states) - 1:
            await asyncio.sleep(0.2)  # 模拟时间间隔
    
    print("\n" + "="*60)
    print("演示完成")
    print("="*60)
    print("✅ MCP 工具导入功能演示完成")
    print("🎯 主要特性:")
    print("   - 支持 stdio 和 HTTP 两种 MCP 传输方式")
    print("   - 智能工具发现和批量导入")
    print("   - 灵活的工具过滤和选择")
    print("   - 自定义工具配置和命名")
    print("   - 完整的进度跟踪和状态管理")
    print("   - 自动验证和示例生成")
    print("   - 详细的错误处理和建议")
    print("📝 注意: 实际使用时需要配置真实的 MCP 服务器")

async def demo_advanced_mcp_features():
    """演示高级 MCP 功能"""
    print("\n" + "="*60)
    print("高级 MCP 功能演示")
    print("="*60)
    
    # 复杂 MCP 配置示例
    complex_configs = [
        {
            "name": "多环境文件系统服务器",
            "config": {
                "transport": "stdio",
                "command": "uvx",
                "args": ["mcp-server-filesystem"],
                "env": {
                    "ALLOWED_DIRECTORIES": "/home/<USER>/projects,/tmp,/var/log",
                    "MAX_FILE_SIZE": "10MB",
                    "ENABLE_WRITE": "true",
                    "LOG_LEVEL": "INFO"
                }
            }
        },
        {
            "name": "认证的 HTTP MCP 服务器",
            "config": {
                "transport": "http",
                "url": "https://api.example.com/mcp",
                "env": {
                    "API_KEY": "your-api-key-here",
                    "TIMEOUT": "30"
                }
            }
        },
        {
            "name": "数据库 MCP 服务器",
            "config": {
                "transport": "stdio",
                "command": "python",
                "args": ["-m", "mcp_server_database"],
                "env": {
                    "DB_TYPE": "postgresql",
                    "DB_HOST": "localhost",
                    "DB_PORT": "5432",
                    "DB_NAME": "myapp",
                    "READONLY": "false"
                }
            }
        }
    ]
    
    print("复杂 MCP 配置示例:")
    for config in complex_configs:
        print(f"\n📋 {config['name']}:")
        print(f"   传输方式: {config['config']['transport']}")
        if config['config']['transport'] == 'stdio':
            print(f"   命令: {config['config']['command']} {' '.join(config['config'].get('args', []))}")
        else:
            print(f"   URL: {config['config']['url']}")
        
        if config['config'].get('env'):
            print(f"   环境变量:")
            for key, value in config['config']['env'].items():
                print(f"     {key}={value}")
    
    # 批量导入策略
    print(f"\n批量导入策略:")
    strategies = [
        "按服务器类型分组导入",
        "按工具功能分类导入", 
        "按安全级别分批导入",
        "按依赖关系顺序导入"
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"   {i}. {strategy}")
    
    # 错误恢复机制
    print(f"\n错误恢复机制:")
    recovery_mechanisms = [
        "连接超时自动重试",
        "部分失败继续导入其他工具",
        "工具验证失败时提供警告但继续导入",
        "Schema 解析失败时使用默认 Schema",
        "导入进度持久化，支持断点续传"
    ]
    
    for mechanism in recovery_mechanisms:
        print(f"   🔧 {mechanism}")

if __name__ == "__main__":
    print("启动 MCP 导入演示...")
    asyncio.run(demo_mcp_import())
    asyncio.run(demo_advanced_mcp_features())