#!/usr/bin/env python3
"""
搜索功能演示脚本
"""
import asyncio
import json
from datetime import datetime, timedelta

async def demo_search_functionality():
    """演示搜索功能"""
    print("=== 工具搜索功能演示 ===")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 导入必要的模块
    try:
        from backend.routers.search import (
            search_tools, advanced_search_tools, get_search_suggestions,
            get_search_filters, get_popular_searches, get_recent_searches,
            AdvancedSearchRequest, _search_mock_data, _calculate_relevance_score,
            _generate_search_suggestions, _build_search_query, _get_field_boost,
            _build_sort_config, _calculate_suggestion_score
        )
        print("✅ 成功导入搜索模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    print("\n" + "="*70)
    print("1. 基础关键词搜索演示")
    print("="*70)
    
    # 1. 基础搜索
    basic_search_cases = [
        {
            "name": "天气工具搜索",
            "params": {
                "q": "天气",
                "category": "weather",
                "page": 1,
                "page_size": 5
            }
        },
        {
            "name": "API工具搜索",
            "params": {
                "q": "API",
                "transport": "http",
                "visibility": "public",
                "page": 1,
                "page_size": 10
            }
        },
        {
            "name": "Python工具搜索",
            "params": {
                "q": "python",
                "transport": "python",
                "sort_by": "name",
                "sort_order": "asc",
                "page": 1,
                "page_size": 8
            }
        }
    ]
    
    for case in basic_search_cases:
        print(f"\n🔍 {case['name']}:")
        try:
            result = await search_tools(**case["params"])
            
            print(f"   ✅ 搜索成功")
            print(f"   查询: '{result.query}'")
            print(f"   总结果: {result.total} 个")
            print(f"   当前页: {result.page}/{result.total_pages}")
            print(f"   耗时: {result.took}ms" if result.took else "   耗时: N/A")
            
            # 显示前几个结果
            for i, tool in enumerate(result.results[:3]):
                print(f"   [{i+1}] {tool.get('displayName', 'N/A')}")
                print(f"       分类: {tool.get('category', 'N/A')}")
                print(f"       传输: {tool.get('transport', 'N/A')}")
                if tool.get('_score'):
                    print(f"       相关性: {tool['_score']:.2f}")
            
            if len(result.results) > 3:
                print(f"   ... 还有 {len(result.results) - 3} 个结果")
            
            # 显示筛选条件
            active_filters = [k for k, v in result.filters.items() if v]
            if active_filters:
                print(f"   筛选条件: {', '.join(active_filters)}")
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
    
    print("\n" + "="*70)
    print("2. 高级搜索演示")
    print("="*70)
    
    # 2. 高级搜索
    advanced_search_cases = [
        {
            "name": "多分类搜索",
            "request": AdvancedSearchRequest(
                query="数据处理",
                categories=["data", "utility", "analytics"],
                transports=["python", "http"],
                fuzzy=True,
                boost_exact_match=True,
                sort_by="relevance",
                page=1,
                page_size=10
            )
        },
        {
            "name": "标签和时间范围搜索",
            "request": AdvancedSearchRequest(
                query="API工具",
                tags=["api", "web", "service"],
                visibility="public",
                created_after=datetime.now() - timedelta(days=30),
                sort_by="date",
                sort_order="desc",
                page=1,
                page_size=15
            )
        },
        {
            "name": "特定字段搜索",
            "request": AdvancedSearchRequest(
                query="邮件发送",
                search_fields=["displayName", "descriptionUser", "aliases"],
                fuzzy=False,
                boost_exact_match=True,
                sort_by="name",
                sort_order="asc",
                page=1,
                page_size=12
            )
        }
    ]
    
    for case in advanced_search_cases:
        print(f"\n🎯 {case['name']}:")
        try:
            result = await advanced_search_tools(case["request"])
            
            print(f"   ✅ 高级搜索成功")
            print(f"   查询: '{result.query}'")
            print(f"   总结果: {result.total} 个")
            print(f"   搜索字段: {', '.join(case['request'].search_fields)}")
            print(f"   模糊搜索: {'启用' if case['request'].fuzzy else '禁用'}")
            print(f"   排序方式: {case['request'].sort_by} ({case['request'].sort_order})")
            
            # 显示筛选条件
            filters = []
            if case["request"].categories:
                filters.append(f"分类: {', '.join(case['request'].categories)}")
            if case["request"].transports:
                filters.append(f"传输: {', '.join(case['request'].transports)}")
            if case["request"].tags:
                filters.append(f"标签: {', '.join(case['request'].tags)}")
            if case["request"].visibility:
                filters.append(f"可见性: {case['request'].visibility}")
            
            if filters:
                print(f"   筛选条件: {'; '.join(filters)}")
            
            # 显示结果
            for i, tool in enumerate(result.results[:2]):
                print(f"   [{i+1}] {tool.get('displayName', 'N/A')}")
                print(f"       描述: {tool.get('descriptionUser', 'N/A')[:50]}...")
                
        except Exception as e:
            print(f"   ❌ 高级搜索失败: {e}")
    
    print("\n" + "="*70)
    print("3. 搜索建议演示")
    print("="*70)
    
    # 3. 搜索建议
    suggestion_queries = ["天", "API", "数据", "文件", "用户"]
    
    for query in suggestion_queries:
        print(f"\n💡 '{query}' 的搜索建议:")
        try:
            suggestions = await get_search_suggestions(q=query, limit=5)
            
            if suggestions:
                for i, suggestion in enumerate(suggestions):
                    print(f"   [{i+1}] {suggestion.text} ({suggestion.type})")
                    if suggestion.score:
                        print(f"       分数: {suggestion.score:.1f}")
                    if suggestion.metadata:
                        print(f"       元数据: {suggestion.metadata}")
            else:
                print(f"   无建议")
                
        except Exception as e:
            print(f"   ❌ 获取建议失败: {e}")
    
    print("\n" + "="*70)
    print("4. 搜索筛选选项演示")
    print("="*70)
    
    # 4. 搜索筛选选项
    print("\n🔧 可用筛选选项:")
    try:
        filters_result = await get_search_filters()
        
        if filters_result["success"]:
            filters = filters_result["filters"]
            
            print(f"   📂 分类 ({len(filters.get('categories', []))}):")
            for category in filters.get("categories", [])[:5]:
                print(f"     - {category['name']}: {category['count']} 个工具")
            
            print(f"   🚀 传输方式 ({len(filters.get('transports', []))}):")
            for transport in filters.get("transports", []):
                print(f"     - {transport['name']}: {transport['count']} 个工具")
            
            print(f"   👁️ 可见性 ({len(filters.get('visibilities', []))}):")
            for visibility in filters.get("visibilities", []):
                print(f"     - {visibility['name']}: {visibility['count']} 个工具")
            
            print(f"   🏷️ 标签 ({len(filters.get('tags', []))}):")
            for tag in filters.get("tags", [])[:8]:
                print(f"     - {tag['name']}: {tag['count']} 次使用")
        else:
            print("   ❌ 获取筛选选项失败")
            
    except Exception as e:
        print(f"   ❌ 获取筛选选项异常: {e}")
    
    print("\n" + "="*70)
    print("5. 热门和最近搜索演示")
    print("="*70)
    
    # 5. 热门搜索
    print("\n🔥 热门搜索:")
    try:
        popular_result = await get_popular_searches(limit=8)
        
        if popular_result["success"]:
            for i, search in enumerate(popular_result["popular_searches"]):
                print(f"   [{i+1}] '{search['query']}' - {search['count']} 次搜索")
        else:
            print("   ❌ 获取热门搜索失败")
            
    except Exception as e:
        print(f"   ❌ 获取热门搜索异常: {e}")
    
    # 最近搜索
    print("\n🕒 最近搜索:")
    try:
        recent_result = await get_recent_searches(limit=5)
        
        if recent_result["success"]:
            for i, search in enumerate(recent_result["recent_searches"]):
                timestamp = datetime.fromisoformat(search["timestamp"])
                time_str = timestamp.strftime("%H:%M:%S")
                print(f"   [{i+1}] '{search['query']}' - {time_str}")
        else:
            print("   ❌ 获取最近搜索失败")
            
    except Exception as e:
        print(f"   ❌ 获取最近搜索异常: {e}")
    
    print("\n" + "="*70)
    print("6. 搜索算法演示")
    print("="*70)
    
    # 6. 相关性分数计算
    print("\n📊 相关性分数计算:")
    test_tool = {
        "displayName": "天气查询API工具",
        "descriptionUser": "获取实时天气信息和预报数据",
        "aliases": ["weather", "天气", "weather_api"],
        "tags": ["weather", "api", "data"],
        "category": "weather"
    }
    
    test_queries = ["天气查询API工具", "天气", "weather", "API", "数据", "无关内容"]
    
    for query in test_queries:
        score = _calculate_relevance_score(test_tool, query)
        print(f"   '{query}': {score:.1f} 分")
    
    # 字段权重演示
    print("\n⚖️ 字段权重配置:")
    fields = ["displayName", "aliases", "descriptionUser", "category", "tags", "content"]
    for field in fields:
        boost = _get_field_boost(field)
        print(f"   {field}: {boost}x")
    
    # 排序配置演示
    print("\n📈 排序配置:")
    sort_types = ["relevance", "name", "date", "created", "popularity"]
    for sort_type in sort_types:
        config = _build_sort_config(sort_type, "desc")
        print(f"   {sort_type}: {config}")
    
    print("\n" + "="*70)
    print("7. 搜索建议算法演示")
    print("="*70)
    
    # 7. 建议分数计算
    print("\n🎯 建议分数计算:")
    test_texts = ["天气查询", "天气API", "实时天气", "天气预报工具", "无关内容"]
    query = "天气"
    
    for text in test_texts:
        score = _calculate_suggestion_score(text, query)
        print(f"   '{text}' vs '{query}': {score:.1f} 分")
    
    # 建议生成演示
    print("\n💭 智能建议生成:")
    suggestion_cases = [
        {"query": "天", "types": ["tool_name", "category"]},
        {"query": "API", "types": ["tool_name", "alias"]},
        {"query": "数据", "types": ["category", "tag"]}
    ]
    
    for case in suggestion_cases:
        try:
            suggestions = await _generate_search_suggestions(
                case["query"], 
                5, 
                case["types"]
            )
            
            print(f"   '{case['query']}' ({', '.join(case['types'])}):")
            for suggestion in suggestions:
                print(f"     - {suggestion.text} ({suggestion.type}, {suggestion.score:.1f})")
                
        except Exception as e:
            print(f"   ❌ 生成建议失败: {e}")
    
    print("\n" + "="*70)
    print("8. 搜索查询构建演示")
    print("="*70)
    
    # 8. 查询构建
    print("\n🔨 搜索查询构建:")
    complex_request = AdvancedSearchRequest(
        query="Python数据处理工具",
        categories=["data", "analytics"],
        transports=["python"],
        visibility="public",
        tags=["data", "processing"],
        search_fields=["displayName", "descriptionUser", "tags"],
        fuzzy=True,
        boost_exact_match=True,
        sort_by="relevance",
        sort_order="desc",
        page=2,
        page_size=15,
        created_after=datetime.now() - timedelta(days=7)
    )
    
    try:
        query_body = await _build_search_query(complex_request)
        
        print(f"   ✅ 查询构建成功")
        print(f"   查询类型: bool 查询")
        print(f"   should 子句: {len(query_body['query']['bool']['should'])} 个")
        print(f"   filter 子句: {len(query_body['query']['bool']['filter'])} 个")
        print(f"   分页: from={query_body['from']}, size={query_body['size']}")
        
        if "sort" in query_body:
            print(f"   排序: {query_body['sort']}")
        
        # 显示部分查询结构
        print(f"   查询结构预览:")
        print(f"     minimum_should_match: {query_body['query']['bool']['minimum_should_match']}")
        
        filters = query_body['query']['bool']['filter']
        for i, filter_clause in enumerate(filters[:3]):
            if 'terms' in filter_clause:
                field = list(filter_clause['terms'].keys())[0]
                values = filter_clause['terms'][field]
                print(f"     filter[{i}]: {field} in {values}")
            elif 'term' in filter_clause:
                field = list(filter_clause['term'].keys())[0]
                value = filter_clause['term'][field]
                print(f"     filter[{i}]: {field} = {value}")
            elif 'range' in filter_clause:
                field = list(filter_clause['range'].keys())[0]
                range_val = filter_clause['range'][field]
                print(f"     filter[{i}]: {field} range {range_val}")
                
    except Exception as e:
        print(f"   ❌ 查询构建失败: {e}")
    
    print("\n" + "="*70)
    print("9. Mock 数据搜索演示")
    print("="*70)
    
    # 9. Mock 数据搜索
    print("\n🎭 Mock 数据搜索测试:")
    mock_search_cases = [
        {
            "name": "简单关键词搜索",
            "request": AdvancedSearchRequest(
                query="工具",
                page=1,
                page_size=5
            )
        },
        {
            "name": "分类筛选搜索",
            "request": AdvancedSearchRequest(
                query="API",
                categories=["api", "web"],
                page=1,
                page_size=3
            )
        },
        {
            "name": "排序搜索",
            "request": AdvancedSearchRequest(
                query="数据",
                sort_by="name",
                sort_order="asc",
                page=1,
                page_size=4
            )
        }
    ]
    
    for case in mock_search_cases:
        print(f"\n   🔍 {case['name']}:")
        try:
            results, total, took = await _search_mock_data(case["request"])
            
            print(f"     ✅ 搜索完成")
            print(f"     总结果: {total} 个")
            print(f"     返回结果: {len(results)} 个")
            print(f"     耗时: {took}ms")
            
            for i, tool in enumerate(results):
                score_info = f" (分数: {tool.get('_score', 0):.1f})" if tool.get('_score') else ""
                print(f"     [{i+1}] {tool.get('displayName', 'N/A')}{score_info}")
                
        except Exception as e:
            print(f"     ❌ Mock 搜索失败: {e}")
    
    print("\n" + "="*70)
    print("演示完成")
    print("="*70)
    print("✅ 工具搜索功能演示完成")
    print("🎯 主要特性:")
    print("   - 多字段关键词搜索")
    print("   - 模糊匹配和精确匹配")
    print("   - 多维度筛选条件")
    print("   - 灵活的排序选项")
    print("   - 智能搜索建议")
    print("   - 相关性分数计算")
    print("   - 搜索结果缓存")
    print("   - 分页和聚合查询")
    print("   - 热门和历史搜索")
    print("📝 注意: 实际使用时需要配置 Elasticsearch 以获得最佳性能")

async def demo_search_performance():
    """演示搜索性能"""
    print("\n" + "="*70)
    print("搜索性能演示")
    print("="*70)
    
    # 性能测试场景
    performance_scenarios = [
        "大量数据搜索 (10万+ 工具)",
        "复杂查询优化 (多字段 + 多筛选)",
        "搜索结果缓存效果",
        "并发搜索处理能力",
        "实时搜索建议响应"
    ]
    
    print("\n⚡ 性能优化特性:")
    for i, scenario in enumerate(performance_scenarios, 1):
        print(f"   {i}. {scenario}")
    
    # 缓存策略
    print("\n💾 缓存策略:")
    cache_strategies = [
        "搜索结果缓存 (5分钟TTL)",
        "热门查询预缓存",
        "筛选选项缓存 (1小时TTL)",
        "搜索建议缓存 (30分钟TTL)"
    ]
    
    for strategy in cache_strategies:
        print(f"   🔧 {strategy}")
    
    # 索引优化
    print("\n📊 索引优化:")
    index_optimizations = [
        "多字段复合索引",
        "分词器优化配置",
        "相关性评分调优",
        "聚合查询优化"
    ]
    
    for optimization in index_optimizations:
        print(f"   ⚙️ {optimization}")

if __name__ == "__main__":
    print("启动搜索功能演示...")
    asyncio.run(demo_search_functionality())
    asyncio.run(demo_search_performance())