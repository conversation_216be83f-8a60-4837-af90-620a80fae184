#!/usr/bin/env python3
"""
工具注册平台演示脚本
展示后端 API 的各种功能和 mock 数据
"""

import requests
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print('='*60)

def print_response(response: requests.Response, title: str = ""):
    """格式化打印响应"""
    if title:
        print(f"\n📋 {title}")
        print("-" * 40)
    
    print(f"状态码: {response.status_code}")
    try:
        data = response.json()
        print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
    except:
        print(f"响应: {response.text}")

def demo_basic_apis():
    """演示基础 API"""
    print_section("基础 API 演示")
    
    # 健康检查
    response = requests.get(f"{BASE_URL}/health")
    print_response(response, "健康检查")
    
    # 根路径
    response = requests.get(f"{BASE_URL}/")
    print_response(response, "根路径信息")

def demo_mock_tools():
    """演示 Mock 工具数据"""
    print_section("Mock 工具数据演示")
    
    # 获取工具列表
    response = requests.get(f"{BASE_URL}/api/v1/tools/")
    print_response(response, "工具列表 (前5个)")
    
    if response.status_code == 200:
        tools = response.json().get("tools", [])
        if tools:
            # 获取第一个工具的详情
            tool_id = tools[0]["toolId"]
            response = requests.get(f"{BASE_URL}/api/v1/tools/{tool_id}")
            print_response(response, f"工具详情: {tool_id}")

def demo_search():
    """演示搜索功能"""
    print_section("搜索功能演示")
    
    # 关键词搜索
    search_queries = ["天气", "时间", "翻译", "weather", "python"]
    
    for query in search_queries:
        response = requests.get(f"{BASE_URL}/api/v1/search/", params={"q": query, "limit": 3})
        print_response(response, f"搜索: '{query}'")
        time.sleep(0.5)  # 避免请求过快
    
    # 获取分类
    response = requests.get(f"{BASE_URL}/api/v1/search/categories")
    print_response(response, "工具分类")
    
    # 获取统计信息
    response = requests.get(f"{BASE_URL}/api/v1/search/stats")
    print_response(response, "搜索统计")

def demo_tool_registration():
    """演示工具注册功能"""
    print_section("工具注册演示")
    
    # 1. 基础表单注册
    basic_tool = {
        "displayName": "演示工具",
        "category": "demo",
        "transport": "http",
        "endpoint": "https://httpbin.org/get",
        "httpMethod": "GET",
        "description": "这是一个演示用的工具"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/tools/register", json=basic_tool)
    print_response(response, "基础工具注册")
    
    # 2. 从 cURL 注册
    curl_tool = {
        "displayName": "cURL 演示工具",
        "curl": "curl -X GET 'https://httpbin.org/json'",
        "description": "从 cURL 命令自动生成的工具"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/tools/register-from-curl", json=curl_tool)
    print_response(response, "cURL 工具注册")
    
    # 3. Python 脚本注册
    python_script = '''
def hello_world(name: str = "World"):
    """简单的问候函数"""
    return {
        "message": f"Hello, {name}!",
        "timestamp": "2024-01-01T00:00:00Z"
    }
'''
    
    python_tool = {
        "displayName": "Python 问候工具",
        "script_content": python_script,
        "entry_function": "hello_world",
        "description": "Python 脚本演示工具"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/tools/register-python", json=python_tool)
    print_response(response, "Python 工具注册")
    
    # 4. MCP 导入演示
    mcp_config = {
        "transport": "stdio",
        "command": "uvx",
        "args": ["mcp-server-filesystem", "--path", "/tmp"]
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/tools/import-mcp", json=mcp_config)
    print_response(response, "MCP 工具导入")

def demo_sandbox():
    """演示沙盒验证功能"""
    print_section("沙盒验证演示")
    
    # 获取沙盒状态
    response = requests.get(f"{BASE_URL}/api/v1/sandbox/status")
    print_response(response, "沙盒状态")
    
    # HTTP 工具验证
    http_request = {
        "url": "https://httpbin.org/json",
        "method": "GET",
        "timeout": 10
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/sandbox/validate-http", json=http_request)
    print_response(response, "HTTP 工具验证")
    
    # Python 脚本验证
    python_request = {
        "script_content": '''
def get_current_time():
    import datetime
    return {
        "current_time": datetime.datetime.now().isoformat(),
        "message": "Hello from sandbox!"
    }
''',
        "entry_function": "get_current_time",
        "timeout": 10
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/sandbox/validate-python", json=python_request)
    print_response(response, "Python 脚本验证")

def demo_advanced_features():
    """演示高级功能"""
    print_section("高级功能演示")
    
    # 搜索建议
    response = requests.get(f"{BASE_URL}/api/v1/search/suggestions", params={"q": "天"})
    print_response(response, "搜索建议")
    
    # 高级搜索
    advanced_search = {
        "query": "API 工具",
        "category": "api",
        "limit": 5
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/search/", json=advanced_search)
    print_response(response, "高级搜索")
    
    # 按分类筛选
    response = requests.get(f"{BASE_URL}/api/v1/tools/", params={"category": "weather", "limit": 3})
    print_response(response, "按分类筛选工具")

def main():
    """主演示函数"""
    print("🎯 工具注册平台后端演示")
    print("=" * 60)
    print("确保后端服务已启动: docker-compose up -d")
    print("API 文档地址: http://localhost:8000/docs")
    
    try:
        # 等待服务启动
        print("\n⏳ 检查服务状态...")
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未就绪")
            return
        print("✅ 后端服务已就绪")
        
        # 运行演示
        demo_basic_apis()
        demo_mock_tools()
        demo_search()
        demo_tool_registration()
        demo_sandbox()
        demo_advanced_features()
        
        print_section("演示完成")
        print("🎉 所有功能演示完成！")
        print("💡 你可以访问 http://localhost:8000/docs 查看完整的 API 文档")
        print("🔧 使用 docker-compose logs -f backend 查看后端日志")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务")
        print("请确保运行了: docker-compose up -d")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()