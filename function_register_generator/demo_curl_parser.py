#!/usr/bin/env python3
"""
cURL 解析器演示脚本
展示强大的 cURL 命令解析和 Schema 推断功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from parsers.curl_parser import parse_curl, curl_to_schema, CurlParser, CurlSchemaInferrer
import json

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print('='*60)

def print_parse_result(result, title: str = ""):
    """格式化打印解析结果"""
    if title:
        print(f"\n📋 {title}")
        print("-" * 40)
    
    print(f"URL: {result.url}")
    print(f"方法: {result.method.value}")
    
    if result.headers:
        print(f"请求头: {json.dumps(result.headers, ensure_ascii=False, indent=2)}")
    
    if result.query_params:
        print(f"查询参数: {json.dumps(result.query_params, ensure_ascii=False, indent=2)}")
    
    if result.body:
        print(f"请求体: {json.dumps(result.body, ensure_ascii=False, indent=2)}")
        print(f"请求体类型: {result.body_type}")
    
    if result.auth:
        print(f"认证: {json.dumps(result.auth, ensure_ascii=False, indent=2)}")
    
    if result.cookies:
        print(f"Cookies: {json.dumps(result.cookies, ensure_ascii=False, indent=2)}")
    
    if result.timeout:
        print(f"超时: {result.timeout}秒")
    
    print(f"跟随重定向: {result.follow_redirects}")
    print(f"验证SSL: {result.verify_ssl}")

def print_schema(schema, title: str = ""):
    """格式化打印 Schema"""
    if title:
        print(f"\n📋 {title}")
        print("-" * 40)
    
    print(json.dumps(schema, ensure_ascii=False, indent=2))

def demo_basic_parsing():
    """演示基础解析功能"""
    print_section("基础 cURL 解析演示")
    
    examples = [
        {
            "name": "简单 GET 请求",
            "curl": "curl https://api.github.com/users/octocat"
        },
        {
            "name": "带查询参数的 GET",
            "curl": "curl 'https://api.example.com/search?q=python&page=1&limit=10'"
        },
        {
            "name": "POST JSON 数据",
            "curl": '''curl -X POST https://api.example.com/users \
                      -H "Content-Type: application/json" \
                      -d '{"name": "John Doe", "email": "<EMAIL>", "age": 30}' '''
        },
        {
            "name": "表单数据提交",
            "curl": "curl -X POST https://api.example.com/login -d 'username=admin&password=secret123&remember=true'"
        }
    ]
    
    for example in examples:
        try:
            result = parse_curl(example["curl"])
            print_parse_result(result, example["name"])
        except Exception as e:
            print(f"❌ {example['name']} 解析失败: {e}")

def demo_advanced_parsing():
    """演示高级解析功能"""
    print_section("高级 cURL 解析演示")
    
    examples = [
        {
            "name": "复杂的 GitHub API 调用",
            "curl": '''curl -X POST 'https://api.github.com/repos/owner/repo/issues' \
                      -H 'Accept: application/vnd.github.v3+json' \
                      -H 'Authorization: token ghp_xxxxxxxxxxxxxxxxxxxx' \
                      -H 'Content-Type: application/json' \
                      -d '{
                        "title": "Found a bug",
                        "body": "Something is not working correctly",
                        "labels": ["bug", "urgent"],
                        "assignees": ["maintainer"]
                      }' '''
        },
        {
            "name": "带认证的请求",
            "curl": "curl -u admin:password123 https://api.example.com/secure/data"
        },
        {
            "name": "带 Cookies 的请求",
            "curl": "curl -b 'session=abc123; user_id=456; theme=dark' https://api.example.com/profile"
        },
        {
            "name": "多行 cURL 命令",
            "curl": """curl -X PUT \\
                      https://api.example.com/users/123 \\
                      -H "Authorization: Bearer token123" \\
                      -H "Content-Type: application/json" \\
                      -d '{"name": "Updated Name", "status": "active"}' """
        }
    ]
    
    for example in examples:
        try:
            result = parse_curl(example["curl"])
            print_parse_result(result, example["name"])
        except Exception as e:
            print(f"❌ {example['name']} 解析失败: {e}")

def demo_schema_inference():
    """演示 Schema 推断功能"""
    print_section("Schema 推断演示")
    
    examples = [
        {
            "name": "用户注册 API",
            "curl": '''curl -X POST https://api.example.com/users/register \
                      -H "Content-Type: application/json" \
                      -d '{
                        "username": "johndoe",
                        "email": "<EMAIL>",
                        "password": "secret123",
                        "age": 25,
                        "newsletter": true
                      }' '''
        },
        {
            "name": "搜索 API",
            "curl": "curl 'https://api.example.com/search?q=python&category=programming&page=1&per_page=20'"
        },
        {
            "name": "文件上传 API",
            "curl": '''curl -X POST https://api.example.com/upload \
                      -H "Authorization: Bearer token123" \
                      -F "file=@document.pdf" \
                      -F "description=Important document" '''
        },
        {
            "name": "RESTful 资源操作",
            "curl": "curl -X PUT https://api.example.com/posts/{postId}/comments/{commentId} -d 'content=Updated comment'"
        }
    ]
    
    for example in examples:
        try:
            result = curl_to_schema(example["curl"])
            
            print(f"\n🔍 {example['name']}")
            print("=" * 50)
            
            print_schema(result["input_schema"], "输入参数 Schema")
            print_schema(result["output_schema"], "输出响应 Schema")
            
        except Exception as e:
            print(f"❌ {example['name']} Schema 推断失败: {e}")

def demo_real_world_examples():
    """演示真实世界的 cURL 示例"""
    print_section("真实世界 cURL 示例")
    
    examples = [
        {
            "name": "OpenAI API 调用",
            "curl": '''curl https://api.openai.com/v1/chat/completions \
                      -H "Content-Type: application/json" \
                      -H "Authorization: Bearer sk-..." \
                      -d '{
                        "model": "gpt-3.5-turbo",
                        "messages": [{"role": "user", "content": "Hello!"}],
                        "max_tokens": 100
                      }' '''
        },
        {
            "name": "Stripe 支付 API",
            "curl": '''curl https://api.stripe.com/v1/charges \
                      -u sk_test_...: \
                      -d amount=2000 \
                      -d currency=usd \
                      -d source=tok_visa \
                      -d description="<NAME_EMAIL>" '''
        },
        {
            "name": "Slack Webhook",
            "curl": '''curl -X POST ***************************************************************************** \
                      -H 'Content-type: application/json' \
                      -d '{
                        "text": "Hello, World!",
                        "channel": "#general",
                        "username": "webhookbot"
                      }' '''
        },
        {
            "name": "Docker Registry API",
            "curl": '''curl -X GET https://registry.hub.docker.com/v2/repositories/library/nginx/tags/ \
                      -H "Authorization: Bearer token123" '''
        }
    ]
    
    for example in examples:
        try:
            result = curl_to_schema(example["curl"])
            
            print(f"\n🌐 {example['name']}")
            print("=" * 50)
            
            # 显示解析结果摘要
            parse_result = result["parse_result"]
            print(f"方法: {parse_result['method']}")
            print(f"URL: {parse_result['url']}")
            print(f"请求头数量: {len(parse_result['headers'])}")
            
            if parse_result['body']:
                print(f"请求体类型: {parse_result['body_type']}")
            
            # 显示推断的 Schema 摘要
            input_schema = result["input_schema"]
            output_schema = result["output_schema"]
            
            print(f"输入参数数量: {len(input_schema['properties'])}")
            print(f"必填参数数量: {len(input_schema.get('required', []))}")
            print(f"输出字段数量: {len(output_schema['properties'])}")
            
        except Exception as e:
            print(f"❌ {example['name']} 处理失败: {e}")

def demo_edge_cases():
    """演示边界情况处理"""
    print_section("边界情况处理演示")
    
    examples = [
        {
            "name": "文件引用",
            "curl": "curl -X POST https://api.example.com/upload -d @data.json"
        },
        {
            "name": "特殊字符处理",
            "curl": '''curl -X POST https://api.example.com/data \
                      -d '{"message": "Hello, 世界! 🌍", "emoji": "😀"}' '''
        },
        {
            "name": "复杂的查询参数",
            "curl": "curl 'https://api.example.com/search?q=python%20programming&tags[]=web&tags[]=api&sort=date&order=desc'"
        },
        {
            "name": "没有协议的 URL",
            "curl": "curl api.example.com/users -H 'Accept: application/json'"
        },
        {
            "name": "长选项格式",
            "curl": '''curl --request POST \
                      --url https://api.example.com/data \
                      --header "Content-Type: application/json" \
                      --data '{"test": true}' \
                      --user-agent "MyApp/1.0" \
                      --connect-timeout 30 '''
        }
    ]
    
    for example in examples:
        try:
            result = parse_curl(example["curl"])
            print_parse_result(result, example["name"])
        except Exception as e:
            print(f"❌ {example['name']} 解析失败: {e}")

def demo_performance_test():
    """演示性能测试"""
    print_section("性能测试演示")
    
    import time
    
    # 准备测试数据
    test_curls = [
        "curl https://api.example.com/users",
        "curl -X POST https://api.example.com/data -d '{\"test\": true}'",
        '''curl -X PUT https://api.example.com/users/123 \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer token" \
          -d '{"name": "Updated", "active": true}' ''',
        "curl 'https://api.example.com/search?q=test&page=1&limit=10'",
        "curl -u admin:pass https://api.example.com/secure"
    ] * 20  # 重复20次，总共100个测试
    
    print(f"测试 {len(test_curls)} 个 cURL 命令的解析性能...")
    
    start_time = time.time()
    success_count = 0
    
    for curl in test_curls:
        try:
            result = parse_curl(curl)
            success_count += 1
        except:
            pass
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"✅ 总耗时: {total_time:.3f} 秒")
    print(f"✅ 成功解析: {success_count}/{len(test_curls)}")
    print(f"✅ 平均每个: {total_time/len(test_curls)*1000:.2f} 毫秒")
    print(f"✅ 每秒处理: {len(test_curls)/total_time:.0f} 个")

def main():
    """主演示函数"""
    print("🔧 cURL 解析器功能演示")
    print("=" * 60)
    print("展示强大的 cURL 命令解析和 Schema 推断能力")
    
    try:
        demo_basic_parsing()
        demo_advanced_parsing()
        demo_schema_inference()
        demo_real_world_examples()
        demo_edge_cases()
        demo_performance_test()
        
        print_section("演示完成")
        print("🎉 cURL 解析器功能演示完成！")
        
        print("\n💡 核心特性:")
        print("- 🔍 智能解析：支持各种 cURL 格式和选项")
        print("- 📊 Schema 推断：自动生成输入输出 JSON Schema")
        print("- 🛡️ 错误处理：优雅处理各种边界情况")
        print("- ⚡ 高性能：毫秒级解析速度")
        print("- 🌐 真实场景：支持主流 API 服务的 cURL 格式")
        print("- 🔧 易于集成：简洁的 API 设计")
        
        print("\n🚀 应用场景:")
        print("- 📝 API 文档生成：从 cURL 自动生成 API 文档")
        print("- 🔧 工具注册：智能解析 cURL 生成工具定义")
        print("- 🧪 测试用例：从 cURL 生成自动化测试")
        print("- 📊 API 分析：分析和理解 API 调用模式")
        print("- 🔄 格式转换：cURL 转换为其他格式")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()