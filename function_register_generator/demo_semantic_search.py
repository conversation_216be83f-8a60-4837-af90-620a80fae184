#!/usr/bin/env python3
"""
语义搜索功能演示脚本
"""
import asyncio
import numpy as np
from datetime import datetime

async def demo_semantic_search():
    """演示语义搜索功能"""
    print("=== 语义搜索功能演示 ===")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 导入必要的模块
    try:
        from backend.services.vector_search_service import VectorSearchService, get_vector_search_service
        from backend.routers.search import (
            semantic_search_tools, hybrid_search_tools, get_similar_tools,
            index_tool_for_search, get_vector_search_stats
        )
        print("✅ 成功导入语义搜索模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    print("\n" + "="*70)
    print("1. 向量搜索服务初始化演示")
    print("="*70)
    
    # 1. 初始化向量搜索服务
    print("\n🚀 初始化向量搜索服务:")
    try:
        vector_service = await get_vector_search_service()
        
        print(f"   ✅ 服务初始化成功")
        print(f"   模型: {vector_service.embedding_model}")
        print(f"   向量维度: {vector_service.dimension}")
        print(f"   缓存大小: {len(vector_service.embeddings_cache)}")
        print(f"   索引大小: {len(vector_service.vector_index)}")
        
    except Exception as e:
        print(f"   ❌ 服务初始化失败: {e}")
        return
    
    print("\n" + "="*70)
    print("2. 文本嵌入向量生成演示")
    print("="*70)
    
    # 2. 嵌入向量生成
    test_texts = [
        "天气查询API工具",
        "获取实时天气信息",
        "文件管理系统",
        "数据库连接工具",
        "用户认证服务",
        "邮件发送功能"
    ]
    
    print("\n🧠 文本嵌入向量生成:")
    embeddings = {}
    
    for text in test_texts:
        try:
            embedding = await vector_service.generate_embedding(text)
            embeddings[text] = embedding
            
            print(f"   ✅ '{text}'")
            print(f"      向量维度: {len(embedding)}")
            print(f"      向量范数: {np.linalg.norm(embedding):.3f}")
            print(f"      向量类型: {embedding.dtype}")
            
        except Exception as e:
            print(f"   ❌ '{text}': {e}")
    
    # 计算文本间相似度
    print("\n📊 文本相似度矩阵:")
    if len(embeddings) >= 2:
        text_list = list(embeddings.keys())
        print("   " + " ".join([f"{i:>2}" for i in range(len(text_list))]))
        
        for i, text1 in enumerate(text_list):
            similarities = []
            for j, text2 in enumerate(text_list):
                if text1 in embeddings and text2 in embeddings:
                    sim = vector_service._cosine_similarity(embeddings[text1], embeddings[text2])
                    similarities.append(f"{sim:.2f}")
                else:
                    similarities.append("N/A")
            print(f"{i:>2} " + " ".join([f"{s:>4}" for s in similarities]) + f" {text1[:20]}...")
    
    print("\n" + "="*70)
    print("3. 工具索引创建演示")
    print("="*70)
    
    # 3. 工具索引
    sample_tools = [
        {
            "toolId": "weather.api.v1",
            "displayName": "天气查询API",
            "category": "weather",
            "transport": "http",
            "descriptionUser": "获取全球城市的实时天气信息，包括温度、湿度、风速等详细数据",
            "aliases": ["weather", "天气", "weather_api"],
            "tags": ["weather", "api", "data", "realtime"]
        },
        {
            "toolId": "weather.forecast.v1",
            "displayName": "天气预报工具",
            "category": "weather",
            "transport": "python",
            "descriptionUser": "提供未来7天的天气预报信息，支持多种预报模型",
            "aliases": ["forecast", "预报", "weather_forecast"],
            "tags": ["weather", "forecast", "prediction"]
        },
        {
            "toolId": "file.manager.v1",
            "displayName": "文件管理器",
            "category": "file",
            "transport": "python",
            "descriptionUser": "管理本地和远程文件系统，支持文件上传、下载、删除等操作",
            "aliases": ["file", "文件", "file_manager"],
            "tags": ["file", "system", "management", "storage"]
        },
        {
            "toolId": "db.connector.v1",
            "displayName": "数据库连接器",
            "category": "database",
            "transport": "python",
            "descriptionUser": "连接各种数据库系统，执行SQL查询和数据操作",
            "aliases": ["database", "数据库", "db_connector"],
            "tags": ["database", "sql", "data", "connection"]
        },
        {
            "toolId": "email.sender.v1",
            "displayName": "邮件发送工具",
            "category": "communication",
            "transport": "http",
            "descriptionUser": "发送电子邮件，支持HTML格式、附件和批量发送",
            "aliases": ["email", "邮件", "mail_sender"],
            "tags": ["email", "communication", "notification"]
        }
    ]
    
    print("\n📚 创建工具索引:")
    indexed_tools = []
    
    for tool in sample_tools:
        try:
            success = await vector_service.index_tool(tool["toolId"], tool)
            if success:
                indexed_tools.append(tool)
                print(f"   ✅ {tool['displayName']} ({tool['toolId']})")
                
                # 显示工具文本表示
                text_content = vector_service._build_tool_text(tool)
                print(f"      文本内容: {text_content[:80]}...")
            else:
                print(f"   ❌ {tool['displayName']}: 索引创建失败")
                
        except Exception as e:
            print(f"   ❌ {tool['displayName']}: {e}")
    
    print(f"\n   📊 索引统计: 成功索引 {len(indexed_tools)} 个工具")
    
    print("\n" + "="*70)
    print("4. 语义搜索演示")
    print("="*70)
    
    # 4. 语义搜索
    search_queries = [
        {
            "query": "天气信息查询",
            "description": "查找天气相关的工具"
        },
        {
            "query": "文件操作管理",
            "description": "查找文件处理相关的工具"
        },
        {
            "query": "数据存储访问",
            "description": "查找数据库相关的工具"
        },
        {
            "query": "消息通知发送",
            "description": "查找通信相关的工具"
        },
        {
            "query": "实时数据获取",
            "description": "查找实时数据相关的工具"
        }
    ]
    
    for search_case in search_queries:
        print(f"\n🔍 语义搜索: '{search_case['query']}'")
        print(f"   目标: {search_case['description']}")
        
        try:
            results = await vector_service.semantic_search(
                query=search_case["query"],
                top_k=3,
                similarity_threshold=0.1
            )
            
            if results:
                print(f"   ✅ 找到 {len(results)} 个相关工具:")
                for i, result in enumerate(results):
                    tool_data = result["tool_data"]
                    similarity = result["similarity"]
                    print(f"   [{i+1}] {tool_data['displayName']} (相似度: {similarity:.3f})")
                    print(f"       分类: {tool_data['category']}")
                    print(f"       描述: {tool_data['descriptionUser'][:60]}...")
            else:
                print(f"   ❌ 未找到相关工具")
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
    
    print("\n" + "="*70)
    print("5. 相似工具推荐演示")
    print("="*70)
    
    # 5. 相似工具推荐
    if indexed_tools:
        target_tool = indexed_tools[0]  # 使用第一个工具作为目标
        print(f"\n🎯 为工具 '{target_tool['displayName']}' 推荐相似工具:")
        
        try:
            similar_tools = await vector_service.get_similar_tools(
                tool_id=target_tool["toolId"],
                top_k=3
            )
            
            if similar_tools:
                print(f"   ✅ 找到 {len(similar_tools)} 个相似工具:")
                for i, similar in enumerate(similar_tools):
                    tool_data = similar["tool_data"]
                    similarity = similar["similarity"]
                    print(f"   [{i+1}] {tool_data['displayName']} (相似度: {similarity:.3f})")
                    print(f"       分类: {tool_data['category']}")
                    print(f"       共同标签: {set(target_tool.get('tags', [])) & set(tool_data.get('tags', []))}")
            else:
                print(f"   ❌ 未找到相似工具")
                
        except Exception as e:
            print(f"   ❌ 推荐失败: {e}")
    
    print("\n" + "="*70)
    print("6. 混合搜索演示")
    print("="*70)
    
    # 6. 混合搜索
    print("\n🔀 混合搜索演示:")
    
    # 模拟关键词搜索结果
    mock_keyword_results = [
        {**indexed_tools[0], "_score": 8.5},
        {**indexed_tools[1], "_score": 6.2},
        {**indexed_tools[2], "_score": 4.8}
    ] if len(indexed_tools) >= 3 else indexed_tools
    
    hybrid_test_cases = [
        {
            "query": "天气数据API",
            "semantic_weight": 0.3,
            "keyword_weight": 0.7,
            "description": "偏重关键词匹配"
        },
        {
            "query": "实时信息获取",
            "semantic_weight": 0.7,
            "keyword_weight": 0.3,
            "description": "偏重语义理解"
        },
        {
            "query": "文件系统管理",
            "semantic_weight": 0.5,
            "keyword_weight": 0.5,
            "description": "平衡权重"
        }
    ]
    
    for case in hybrid_test_cases:
        print(f"\n   🎭 查询: '{case['query']}' ({case['description']})")
        print(f"      权重: 语义 {case['semantic_weight']:.1f} | 关键词 {case['keyword_weight']:.1f}")
        
        try:
            hybrid_results = await vector_service.hybrid_search(
                query=case["query"],
                keyword_results=mock_keyword_results,
                semantic_weight=case["semantic_weight"],
                keyword_weight=case["keyword_weight"],
                top_k=3
            )
            
            if hybrid_results:
                print(f"      ✅ 混合搜索结果:")
                for i, result in enumerate(hybrid_results):
                    print(f"      [{i+1}] {result['displayName']}")
                    print(f"          综合分数: {result.get('_hybrid_score', 0):.3f}")
                    print(f"          关键词分数: {result.get('_keyword_score', 0):.3f}")
                    print(f"          语义分数: {result.get('_semantic_score', 0):.3f}")
            else:
                print(f"      ❌ 无混合搜索结果")
                
        except Exception as e:
            print(f"      ❌ 混合搜索失败: {e}")
    
    print("\n" + "="*70)
    print("7. 向量搜索统计演示")
    print("="*70)
    
    # 7. 统计信息
    print("\n📊 向量搜索统计信息:")
    try:
        stats = await vector_service.get_index_stats()
        
        print(f"   ✅ 统计信息获取成功:")
        print(f"   索引工具数: {stats.get('total_tools', 0)}")
        print(f"   缓存大小: {stats.get('cache_size', 0)}")
        print(f"   向量维度: {stats.get('dimension', 0)}")
        print(f"   嵌入模型: {stats.get('model', 'N/A')}")
        print(f"   内存使用: {stats.get('memory_usage', 'N/A')}")
        
    except Exception as e:
        print(f"   ❌ 统计信息获取失败: {e}")
    
    print("\n" + "="*70)
    print("8. 缓存管理演示")
    print("="*70)
    
    # 8. 缓存管理
    print("\n🗄️ 缓存管理:")
    
    # 显示当前缓存状态
    print(f"   当前缓存大小: {len(vector_service.embeddings_cache)}")
    
    # 生成一些嵌入向量以填充缓存
    cache_test_texts = ["缓存测试1", "缓存测试2", "缓存测试3"]
    for text in cache_test_texts:
        await vector_service.generate_embedding(text)
    
    print(f"   填充后缓存大小: {len(vector_service.embeddings_cache)}")
    
    # 清除缓存
    try:
        success = await vector_service.clear_cache()
        if success:
            print(f"   ✅ 缓存清除成功")
            print(f"   清除后缓存大小: {len(vector_service.embeddings_cache)}")
        else:
            print(f"   ❌ 缓存清除失败")
    except Exception as e:
        print(f"   ❌ 缓存清除异常: {e}")
    
    print("\n" + "="*70)
    print("9. API 端点演示")
    print("="*70)
    
    # 9. API 端点测试
    print("\n🌐 API 端点测试:")
    
    # 语义搜索 API
    try:
        result = await semantic_search_tools(
            q="天气查询工具",
            top_k=3,
            similarity_threshold=0.1
        )
        
        print(f"   ✅ 语义搜索 API:")
        print(f"      查询: {result.query}")
        print(f"      结果数: {len(result.results)}")
        print(f"      总数: {result.total}")
        
    except Exception as e:
        print(f"   ❌ 语义搜索 API 失败: {e}")
    
    # 相似工具 API
    if indexed_tools:
        try:
            result = await get_similar_tools(
                tool_id=indexed_tools[0]["toolId"],
                top_k=2,
                similarity_threshold=0.1
            )
            
            print(f"   ✅ 相似工具 API:")
            print(f"      目标工具: {result['tool_id']}")
            print(f"      相似工具数: {result['total']}")
            
        except Exception as e:
            print(f"   ❌ 相似工具 API 失败: {e}")
    
    # 统计信息 API
    try:
        result = await get_vector_search_stats()
        
        print(f"   ✅ 统计信息 API:")
        print(f"      成功: {result['success']}")
        print(f"      工具数: {result['stats'].get('total_tools', 0)}")
        
    except Exception as e:
        print(f"   ❌ 统计信息 API 失败: {e}")
    
    print("\n" + "="*70)
    print("演示完成")
    print("="*70)
    print("✅ 语义搜索功能演示完成")
    print("🎯 主要特性:")
    print("   - 文本嵌入向量生成")
    print("   - 余弦相似度计算")
    print("   - 语义搜索和排序")
    print("   - 相似工具推荐")
    print("   - 混合搜索（关键词+语义）")
    print("   - 向量索引管理")
    print("   - 缓存优化")
    print("   - 完整的 API 接口")
    print("📝 注意: 当前使用模拟嵌入模型，生产环境建议使用专业模型")

async def demo_advanced_semantic_features():
    """演示高级语义搜索功能"""
    print("\n" + "="*70)
    print("高级语义搜索功能演示")
    print("="*70)
    
    # 语义搜索优化技术
    print("\n🚀 语义搜索优化技术:")
    optimization_techniques = [
        "多模型集成 - 结合多个嵌入模型提高准确性",
        "动态权重调整 - 根据查询类型自动调整权重",
        "上下文感知 - 考虑用户历史和偏好",
        "领域适应 - 针对特定领域优化嵌入",
        "实时学习 - 根据用户反馈持续优化"
    ]
    
    for i, technique in enumerate(optimization_techniques, 1):
        print(f"   {i}. {technique}")
    
    # 性能优化策略
    print("\n⚡ 性能优化策略:")
    performance_strategies = [
        "向量量化 - 减少存储空间和计算时间",
        "近似最近邻 - 使用 FAISS 等库加速搜索",
        "分层索引 - 多级索引提高搜索效率",
        "异步处理 - 并行计算嵌入向量",
        "智能缓存 - 缓存热门查询结果"
    ]
    
    for strategy in performance_strategies:
        print(f"   🔧 {strategy}")
    
    # 应用场景
    print("\n🎯 应用场景:")
    use_cases = [
        "智能工具推荐 - 基于用户行为推荐相关工具",
        "自动分类 - 根据描述自动分类新工具",
        "重复检测 - 识别功能相似的重复工具",
        "知识图谱 - 构建工具间的语义关系",
        "个性化搜索 - 根据用户偏好调整搜索结果"
    ]
    
    for use_case in use_cases:
        print(f"   📊 {use_case}")

if __name__ == "__main__":
    print("启动语义搜索演示...")
    asyncio.run(demo_semantic_search())
    asyncio.run(demo_advanced_semantic_features())