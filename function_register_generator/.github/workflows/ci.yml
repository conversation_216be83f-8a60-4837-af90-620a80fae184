name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.11'
  FLUTTER_VERSION: '3.16.0'
  NODE_VERSION: '18'

jobs:
  # Code Quality Checks
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy bandit safety
        cd backend && pip install -r requirements.txt
    
    - name: Code formatting check (Black)
      run: |
        black --check backend/
    
    - name: Import sorting check (isort)
      run: |
        isort --check-only backend/
    
    - name: Linting (flake8)
      run: |
        flake8 backend/ --max-line-length=88 --extend-ignore=E203,W503
    
    - name: Type checking (mypy)
      run: |
        mypy backend/app --ignore-missing-imports
    
    - name: Security check (bandit)
      run: |
        bandit -r backend/app -f json -o bandit-report.json
    
    - name: Dependency security check (safety)
      run: |
        safety check --json --output safety-report.json
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # Backend Tests
  backend-tests:
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        cd backend
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio pytest-mock
    
    - name: Run unit tests
      env:
        DATABASE_URL: postgresql://testuser:testpass@localhost:5432/testdb
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        cd backend
        python -m pytest tests/ -v --cov=app --cov-report=xml --cov-report=html --junit-xml=junit.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage.xml
        flags: backend
        name: backend-coverage
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-test-results
        path: |
          backend/coverage.xml
          backend/htmlcov/
          backend/junit.xml

  # Frontend Tests
  frontend-tests:
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
    
    - name: Cache Flutter dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.pub-cache
          frontend/.dart_tool
        key: ${{ runner.os }}-flutter-${{ hashFiles('frontend/pubspec.yaml') }}
        restore-keys: |
          ${{ runner.os }}-flutter-
    
    - name: Install dependencies
      run: |
        cd frontend
        flutter pub get
    
    - name: Analyze code
      run: |
        cd frontend
        flutter analyze
    
    - name: Run tests
      run: |
        cd frontend
        flutter test --coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: frontend-test-results
        path: frontend/coverage/

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        cd backend && pip install -r requirements.txt
        cd ../frontend && flutter pub get
        pip install pytest pytest-asyncio requests
    
    - name: Start backend server
      env:
        DATABASE_URL: postgresql://testuser:testpass@localhost:5432/testdb
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        cd backend
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 &
        sleep 10
    
    - name: Build and serve frontend
      run: |
        cd frontend
        flutter build web
        python -m http.server 8080 --directory build/web &
        sleep 5
    
    - name: Run integration tests
      run: |
        python -m pytest tests/integration/ -v
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: tests/integration/

  # E2E Tests
  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
    
    - name: Install Chrome
      uses: browser-actions/setup-chrome@latest
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        cd backend && pip install -r requirements.txt
        cd ../frontend && flutter pub get
        pip install selenium pytest webdriver-manager
    
    - name: Start services
      run: |
        # Start backend
        cd backend
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 &
        
        # Build and serve frontend
        cd ../frontend
        flutter build web
        python -m http.server 8080 --directory build/web &
        
        # Wait for services to start
        sleep 15
    
    - name: Run E2E tests
      run: |
        python -m pytest tests/e2e/ -v --headless
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: tests/e2e/

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        cd backend && pip install -r requirements.txt
        pip install locust pytest
    
    - name: Start backend server
      run: |
        cd backend
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 &
        sleep 10
    
    - name: Run performance tests
      run: |
        python -m pytest tests/performance/ -v -s
    
    - name: Run load tests
      run: |
        cd tests/performance
        locust -f test_api_performance.py --headless --users 50 --spawn-rate 5 --run-time 60s --host http://localhost:8000 --html load_test_report.html
    
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: |
          tests/performance/
          load_test_report.html

  # Build and Deploy
  build-and-deploy:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, integration-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/function-register-backend:latest
          ${{ secrets.DOCKER_USERNAME }}/function-register-backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
    
    - name: Build frontend
      run: |
        cd frontend
        flutter pub get
        flutter build web --release
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/function-register-frontend:latest
          ${{ secrets.DOCKER_USERNAME }}/function-register-frontend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Deploy to staging
      if: github.ref == 'refs/heads/develop'
      run: |
        echo "Deploying to staging environment..."
        # Add staging deployment commands here
    
    - name: Deploy to production
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to production environment..."
        # Add production deployment commands here

  # Notification
  notify:
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.build-and-deploy.result == 'success'
      run: |
        echo "✅ Pipeline completed successfully!"
        # Add notification logic (Slack, email, etc.)
    
    - name: Notify on failure
      if: needs.build-and-deploy.result == 'failure'
      run: |
        echo "❌ Pipeline failed!"
        # Add failure notification logic
