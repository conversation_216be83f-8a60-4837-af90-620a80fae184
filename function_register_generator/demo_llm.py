#!/usr/bin/env python3
"""
LM Studio 集成演示脚本
展示本地大语言模型的智能补全功能
"""

import requests
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000"
LM_STUDIO_URL = "http://localhost:1234"

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🤖 {title}")
    print('='*60)

def print_response(response: requests.Response, title: str = ""):
    """格式化打印响应"""
    if title:
        print(f"\n📋 {title}")
        print("-" * 40)
    
    print(f"状态码: {response.status_code}")
    try:
        data = response.json()
        print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
    except:
        print(f"响应: {response.text}")

def check_lm_studio():
    """检查 LM Studio 服务状态"""
    print_section("检查 LM Studio 服务")
    
    try:
        response = requests.get(f"{LM_STUDIO_URL}/v1/models", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print("✅ LM Studio 服务正常")
            print(f"可用模型: {json.dumps(models, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"❌ LM Studio 服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到 LM Studio: {e}")
        print("💡 请确保 LM Studio 已启动并加载了模型 qwen/qwen3-30b-a3b-2507")
        return False

def demo_smart_completion():
    """演示智能补全功能"""
    print_section("智能补全演示")
    
    # 测试工具信息
    tool_info = {
        "displayName": "GitHub 用户信息查询",
        "transport": "http",
        "endpoint": "https://api.github.com/users/{username}",
        "description": "获取 GitHub 用户的详细信息"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/tools/smart-complete", json=tool_info)
    print_response(response, "智能补全结果")

def demo_curl_analysis():
    """演示 cURL 智能分析"""
    print_section("cURL 智能分析演示")
    
    curl_examples = [
        {
            "displayName": "GitHub API 用户查询",
            "curl": "curl -H 'Accept: application/vnd.github.v3+json' https://api.github.com/users/octocat",
            "description": "获取 GitHub 用户信息"
        },
        {
            "displayName": "天气查询 API",
            "curl": "curl 'https://api.open-meteo.com/v1/forecast?latitude=39.9042&longitude=116.4074&hourly=temperature_2m'",
            "description": "获取北京的天气预报"
        },
        {
            "displayName": "JSON 占位符 API",
            "curl": "curl -X POST https://jsonplaceholder.typicode.com/posts -H 'Content-Type: application/json' -d '{\"title\": \"foo\", \"body\": \"bar\", \"userId\": 1}'",
            "description": "创建新的文章"
        }
    ]
    
    for example in curl_examples:
        response = requests.post(f"{BASE_URL}/api/v1/tools/register-from-curl", json=example)
        print_response(response, f"cURL 分析: {example['displayName']}")
        time.sleep(1)  # 避免请求过快

def demo_natural_language_generation():
    """演示自然语言工具生成"""
    print_section("自然语言工具生成演示")
    
    nl_examples = [
        {
            "description": "我需要一个工具来计算两个数字的和，输入两个数字，返回它们的和",
            "test_inputs": {"a": 5, "b": 3},
            "test_outputs": {"sum": 8},
            "category": "math"
        },
        {
            "description": "创建一个文本处理工具，统计文本中的字符数、单词数和行数",
            "test_inputs": {"text": "Hello World\nThis is a test"},
            "test_outputs": {"characters": 26, "words": 5, "lines": 2},
            "category": "text"
        },
        {
            "description": "需要一个时间转换工具，将时间戳转换为可读的日期格式",
            "test_inputs": {"timestamp": 1640995200},
            "test_outputs": {"date": "2022-01-01 00:00:00", "format": "YYYY-MM-DD HH:mm:ss"},
            "category": "utility"
        }
    ]
    
    for example in nl_examples:
        response = requests.post(f"{BASE_URL}/api/v1/tools/generate-from-description", json=example)
        print_response(response, f"自然语言生成: {example['description'][:30]}...")
        time.sleep(2)  # LLM 需要更多时间

def demo_generated_tools_search():
    """演示生成工具的搜索"""
    print_section("生成工具搜索演示")
    
    # 搜索生成的工具
    search_queries = ["计算", "文本", "时间", "math", "generated"]
    
    for query in search_queries:
        response = requests.get(f"{BASE_URL}/api/v1/search/", params={"q": query, "limit": 3})
        print_response(response, f"搜索生成的工具: '{query}'")
        time.sleep(0.5)

def demo_tool_validation():
    """演示工具验证功能"""
    print_section("工具验证演示")
    
    # 验证一个简单的 HTTP 工具
    http_validation = {
        "url": "https://httpbin.org/json",
        "method": "GET",
        "timeout": 10
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/sandbox/validate-http", json=http_validation)
    print_response(response, "HTTP 工具验证")
    
    # 验证一个 Python 脚本
    python_validation = {
        "script_content": '''
def add_numbers(a: int, b: int) -> dict:
    """计算两个数字的和"""
    return {
        "sum": a + b,
        "operation": "addition",
        "inputs": {"a": a, "b": b}
    }
''',
        "entry_function": "add_numbers",
        "args": {"a": 10, "b": 20},
        "timeout": 10
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/sandbox/validate-python", json=python_validation)
    print_response(response, "Python 脚本验证")

def main():
    """主演示函数"""
    print("🤖 LM Studio 集成功能演示")
    print("=" * 60)
    print("本演示展示工具注册平台与本地大语言模型的集成")
    print("模型: qwen/qwen3-30b-a3b-2507")
    
    # 检查服务状态
    print("\n⏳ 检查服务状态...")
    
    # 检查后端服务
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务已就绪")
        else:
            print("❌ 后端服务未就绪")
            return
    except:
        print("❌ 无法连接到后端服务")
        print("请确保运行了: docker-compose up -d")
        return
    
    # 检查 LM Studio
    lm_studio_available = check_lm_studio()
    
    if not lm_studio_available:
        print("\n⚠️  LM Studio 不可用，将使用基础功能演示")
        print("如需完整体验，请:")
        print("1. 启动 LM Studio")
        print("2. 加载模型 qwen/qwen3-30b-a3b-2507")
        print("3. 启动本地服务器 (默认端口 1234)")
    
    try:
        # 运行演示
        demo_smart_completion()
        demo_curl_analysis()
        demo_natural_language_generation()
        demo_generated_tools_search()
        demo_tool_validation()
        
        print_section("演示完成")
        print("🎉 LM Studio 集成功能演示完成！")
        
        if lm_studio_available:
            print("✨ 本地大语言模型功能正常工作")
        else:
            print("⚠️  部分功能使用了基础实现（LM Studio 不可用）")
        
        print("\n💡 功能亮点:")
        print("- 🧠 智能元数据补全")
        print("- 🔍 cURL 命令自动分析")
        print("- 💬 自然语言工具生成")
        print("- 🔧 沙盒环境验证")
        print("- 🔍 智能搜索和发现")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()