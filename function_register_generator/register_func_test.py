import requests
from requests.auth import HTTPBasicAuth


## 使用 env 文件的结果
ES_HOST = "http://ip:9200"          # ← 改成你的 ES 地址
ES_USER = "elastic"                 # ← 账号
ES_PASS = "密码"                     # ← 密码
INDEX   = ""

def register_local_time_tool():
    doc = {
        "toolId": "local.now",
        "displayName": "当前时间",
        "descriptionUser": "返回当前时间；可选传入 IANA 时区。",
        "runtime": {
            "transport": "python",
            # 可选：如果你有落盘脚本与入口函数，也可加上：
            # "script_path": "/opt/tools/now.py",
            # "entry_function": "now",
            "fault_tolerance": "high"
        },
        "metadata": {
            "user_facing": False,
            "has_variables_dependencies": False
        },
        "inputsDeveloperSchema": {
            "type": "object",
            "required": [],
            "properties": {
                "tz": {"type": "string", "description": "可选：IANA 时区，如 Asia/Taipei"}
            }
        },
        "outputsSchema": {"type": "object"}
    }
    r = requests.post(f"{ES_HOST}/{INDEX}/_doc",
                      auth=HTTPBasicAuth(ES_USER, ES_PASS),
                      headers={"Content-Type": "application/json"},
                      json=doc, timeout=15)
    print(r.status_code, r.text)

if __name__ == "__main__":
    register_local_time_tool()
