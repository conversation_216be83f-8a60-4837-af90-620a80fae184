#!/usr/bin/env python3
"""
HTTP 工具沙盒演示

这个脚本演示了如何使用 HTTP 沙盒服务来安全地验证和测试 HTTP 工具，
包括网络安全检查、响应解析、Schema 推断和代码生成等功能。
"""

import asyncio
import json
import sys
import os

# 添加 backend 目录到 Python 路径
sys.path.append('backend')

from services.http_sandbox_service import (
    HTTPSandboxService,
    HTTPSandboxRequest,
    HTTPSandboxConfig,
    get_http_sandbox_service
)


async def demo_basic_http_validation():
    """演示基本的 HTTP 工具验证"""
    print("=" * 60)
    print("演示 1: 基本 HTTP 工具验证")
    print("=" * 60)
    
    # 创建 HTTP 沙盒服务
    async with HTTPSandboxService() as sandbox:
        # 测试 GET 请求
        request = HTTPSandboxRequest(
            url="https://httpbin.org/get",
            method="GET",
            params={"test": "value", "limit": 10}
        )
        
        print("测试 GET 请求:")
        print(f"URL: {request.url}")
        print(f"方法: {request.method}")
        print(f"参数: {request.params}")
        
        try:
            result = await sandbox.validate_http_tool(request)
            
            print(f"\n验证结果:")
            print(f"成功: {result.success}")
            if result.success:
                print(f"状态码: {result.status_code}")
                print(f"延迟: {result.latency_ms}ms")
                print(f"响应大小: {result.response_size} bytes")
                print(f"内容类型: {result.content_type}")
                
                print(f"\n示例响应:")
                print(json.dumps(result.sample_response, indent=2, ensure_ascii=False)[:500] + "...")
                
                print(f"\n推断的输出 Schema:")
                print(json.dumps(result.inferred_output_schema, indent=2, ensure_ascii=False)[:300] + "...")
                
                if result.security_warnings:
                    print(f"\n安全警告:")
                    for warning in result.security_warnings:
                        print(f"⚠️  {warning}")
            else:
                print(f"错误: {result.error}")
                
        except Exception as e:
            print(f"演示过程中出现错误: {e}")


async def demo_post_request_validation():
    """演示 POST 请求验证"""
    print("\n" + "=" * 60)
    print("演示 2: POST 请求验证")
    print("=" * 60)
    
    async with HTTPSandboxService() as sandbox:
        # 测试 POST 请求
        request = HTTPSandboxRequest(
            url="https://httpbin.org/post",
            method="POST",
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer test-token"
            },
            data={
                "name": "测试用户",
                "email": "<EMAIL>",
                "age": 25,
                "active": True
            }
        )
        
        print("测试 POST 请求:")
        print(f"URL: {request.url}")
        print(f"方法: {request.method}")
        print(f"请求头: {json.dumps(request.headers, indent=2, ensure_ascii=False)}")
        print(f"请求体: {json.dumps(request.data, indent=2, ensure_ascii=False)}")
        
        try:
            result = await sandbox.validate_http_tool(request)
            
            print(f"\n验证结果:")
            print(f"成功: {result.success}")
            if result.success:
                print(f"状态码: {result.status_code}")
                print(f"延迟: {result.latency_ms}ms")
                
                print(f"\n快速开始代码:")
                if result.quickstart:
                    print("\ncURL 命令:")
                    print(result.quickstart["curl"])
                    
                    print("\nPython 代码:")
                    print(result.quickstart["python"][:400] + "...")
                    
                    print("\nJavaScript 代码:")
                    print(result.quickstart["javascript"][:400] + "...")
            else:
                print(f"错误: {result.error}")
                
        except Exception as e:
            print(f"演示过程中出现错误: {e}")


async def demo_security_checks():
    """演示安全检查功能"""
    print("\n" + "=" * 60)
    print("演示 3: 安全检查功能")
    print("=" * 60)
    
    sandbox = HTTPSandboxService()
    
    # 测试不同类型的 URL
    test_urls = [
        "https://api.github.com/users/octocat",  # 安全的公共 API
        "http://localhost:8080/api",  # 本地地址
        "https://***********/admin",  # 私有IP
        "http://127.0.0.1:3000/debug",  # 回环地址
        "ftp://example.com/file",  # 不支持的协议
    ]
    
    print("测试不同 URL 的安全检查:")
    
    for url in test_urls:
        print(f"\n测试 URL: {url}")
        try:
            warnings = await sandbox._security_check(url)
            if warnings:
                print("⚠️  安全警告:")
                for warning in warnings:
                    print(f"   - {warning}")
            else:
                print("✅ 安全检查通过")
        except Exception as e:
            print(f"❌ 检查失败: {e}")


async def demo_schema_inference():
    """演示 Schema 推断功能"""
    print("\n" + "=" * 60)
    print("演示 4: Schema 推断功能")
    print("=" * 60)
    
    sandbox = HTTPSandboxService()
    
    # 测试不同类型的响应数据
    test_responses = [
        {
            "name": "简单对象",
            "data": {
                "id": 123,
                "name": "John Doe",
                "email": "<EMAIL>",
                "active": True
            }
        },
        {
            "name": "复杂嵌套对象",
            "data": {
                "user": {
                    "id": 456,
                    "profile": {
                        "name": "Jane Smith",
                        "avatar": "https://example.com/avatar.jpg",
                        "created_at": "2023-12-25T10:30:00Z"
                    }
                },
                "posts": [
                    {"id": 1, "title": "First Post", "likes": 10},
                    {"id": 2, "title": "Second Post", "likes": 5}
                ],
                "meta": {
                    "total": 2,
                    "page": 1,
                    "per_page": 10
                }
            }
        },
        {
            "name": "数组响应",
            "data": [
                {"id": 1, "name": "Item 1", "price": 19.99},
                {"id": 2, "name": "Item 2", "price": 29.99}
            ]
        }
    ]
    
    print("测试不同响应数据的 Schema 推断:")
    
    for test_case in test_responses:
        print(f"\n{test_case['name']}:")
        print("原始数据:")
        print(json.dumps(test_case['data'], indent=2, ensure_ascii=False))
        
        schema = sandbox._infer_output_schema(test_case['data'])
        print("推断的 Schema:")
        print(json.dumps(schema, indent=2, ensure_ascii=False))


async def demo_code_generation():
    """演示代码生成功能"""
    print("\n" + "=" * 60)
    print("演示 5: 代码生成功能")
    print("=" * 60)
    
    sandbox = HTTPSandboxService()
    
    # 创建一个复杂的请求
    request = HTTPSandboxRequest(
        url="https://api.example.com/users",
        method="POST",
        headers={
            "Authorization": "Bearer your-token-here",
            "Content-Type": "application/json",
            "X-API-Version": "v1"
        },
        params={
            "include": "profile,posts",
            "limit": 20
        },
        data={
            "name": "新用户",
            "email": "<EMAIL>",
            "age": 28,
            "preferences": {
                "theme": "dark",
                "notifications": True
            }
        }
    )
    
    print("基于复杂请求生成代码:")
    print(f"URL: {request.url}")
    print(f"方法: {request.method}")
    
    # 生成不同语言的代码
    quickstart = sandbox._generate_quickstart(request)
    
    print("\n生成的 cURL 命令:")
    print(quickstart["curl"])
    
    print("\n生成的 Python 代码:")
    print(quickstart["python"])
    
    print("\n生成的 JavaScript 代码:")
    print(quickstart["javascript"])


async def demo_error_handling():
    """演示错误处理"""
    print("\n" + "=" * 60)
    print("演示 6: 错误处理")
    print("=" * 60)
    
    print("测试错误处理:")
    
    # 测试无效 URL
    try:
        HTTPSandboxRequest(url="not-a-valid-url")
        print("❌ 未能捕获无效 URL")
    except Exception as e:
        print(f"✅ 正确捕获无效 URL: URL 验证失败")
    
    # 测试不支持的方法
    try:
        HTTPSandboxRequest(url="https://example.com", method="INVALID")
        print("❌ 未能捕获无效方法")
    except Exception as e:
        print(f"✅ 正确捕获无效方法: 不支持的 HTTP 方法")
    
    # 测试超时验证
    try:
        HTTPSandboxRequest(url="https://example.com", timeout=200)
        print("❌ 未能捕获无效超时")
    except Exception as e:
        print(f"✅ 正确捕获无效超时: 超时时间超出限制")
    
    # 测试不支持的协议
    try:
        HTTPSandboxRequest(url="ftp://example.com/file")
        print("❌ 未能捕获不支持的协议")
    except Exception as e:
        print(f"✅ 正确捕获不支持的协议: 不支持的协议")


async def demo_utility_functions():
    """演示工具函数"""
    print("\n" + "=" * 60)
    print("演示 7: 工具函数")
    print("=" * 60)
    
    sandbox = HTTPSandboxService()
    
    # 测试字符串检测函数
    print("字符串类型检测:")
    
    test_strings = [
        ("2023-12-25T10:30:00Z", "日期时间"),
        ("<EMAIL>", "邮箱"),
        ("https://api.github.com", "URL"),
        ("普通字符串", "普通文本")
    ]
    
    for test_str, expected_type in test_strings:
        is_datetime = sandbox._is_datetime_string(test_str)
        is_email = sandbox._is_email_string(test_str)
        is_url = sandbox._is_url_string(test_str)
        
        detected_type = "普通文本"
        if is_datetime:
            detected_type = "日期时间"
        elif is_email:
            detected_type = "邮箱"
        elif is_url:
            detected_type = "URL"
        
        status = "✅" if detected_type == expected_type else "❌"
        print(f"{status} '{test_str}' -> {detected_type} (期望: {expected_type})")
    
    # 测试 IP 地址检测
    print("\nIP 地址检测:")
    
    ip_tests = [
        ("127.0.0.1", True, "本地回环"),
        ("***********", True, "私有网络"),
        ("*******", False, "公共DNS"),
        ("api.github.com", False, "域名")
    ]
    
    for ip, expected_private, description in ip_tests:
        is_private = sandbox._is_private_ip(ip)
        status = "✅" if is_private == expected_private else "❌"
        private_text = "私有" if is_private else "公共"
        print(f"{status} {ip} -> {private_text} ({description})")


async def main():
    """主函数"""
    print("HTTP 工具沙盒服务演示")
    print("这个演示展示了如何使用 HTTP 沙盒服务安全地验证和测试 HTTP 工具")
    
    try:
        await demo_basic_http_validation()
        await demo_post_request_validation()
        await demo_security_checks()
        await demo_schema_inference()
        await demo_code_generation()
        await demo_error_handling()
        await demo_utility_functions()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\nHTTP 沙盒服务的主要功能:")
        print("✓ 安全的 HTTP 请求验证和执行")
        print("✓ 网络安全检查和白名单控制")
        print("✓ 智能响应解析和类型推断")
        print("✓ 自动 JSON Schema 生成")
        print("✓ 多语言代码生成 (cURL, Python, JavaScript)")
        print("✓ 详细的性能指标和错误处理")
        print("✓ 支持各种 HTTP 方法和参数")
        print("✓ 特殊字符串格式检测 (日期、邮箱、URL)")
        
        print("\n安全特性:")
        print("• 私有IP地址和本地网络访问阻止")
        print("• 协议白名单 (仅允许 HTTP/HTTPS)")
        print("• 响应大小限制和超时控制")
        print("• 敏感端口检测和警告")
        print("• 域名白名单支持")
        
        print("\n支持的功能:")
        print("• GET/POST/PUT/PATCH/DELETE 等 HTTP 方法")
        print("• 请求头和查询参数处理")
        print("• JSON 和文本请求体支持")
        print("• 复杂嵌套对象的 Schema 推断")
        print("• 实时性能监控和延迟测量")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())