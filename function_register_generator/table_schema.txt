{
    "settings": {
      "number_of_shards": 1,  
      "number_of_replicas": 1, 
      "analysis": {
        "normalizer": {
          "lc_keyword": { 
            "type": "custom", --- 自定义 normalizer：小写化 keyword
            "filter": ["lowercase"]
          }
        }
      }
    },
    "mappings": {
      "properties": {
        "toolId":      { "type": "keyword", "normalizer": "lc_keyword" }, ## 工具唯一 ID（如 weather.get_forecast）
        "displayName": { "type": "search_as_you_type" }, ## 工具展示名（支持联想搜索）
        "aliases":     { "type": "keyword" }, ## 工具别名/同义词
        "version":     { "type": "keyword" }, ## 语义化版本号
        "versionSeq":  { "type": "integer" }, ## 版本递增序列
        "visibility":  { "type": "keyword" }, ## 可见性（public/internal）
        "category":    { "type": "keyword" }, ## 工具分类（如 weather, utility）
        "capabilities":{ "type": "keyword" }, ## 功能能力（如 http、sync、stream）
        "locales":     { "type": "keyword" }, ## 语言支持（如 zh-CN, en-US）
  
        "owner": { ## 工具所有者信息
          "properties": {
            "org":      { "type": "keyword" }, ## 组织名
            "contact":  { "type": "keyword" }, ## 联系方式
            "license":  { "type": "keyword" }, ## 许可证类型
            "userId":   { "type": "keyword" }, ## 所有者用户 ID
            "apiKeyId": { "type": "keyword" }  ## 关联 API Key ID
          }
        },
  
        "runtime": { ## 运行时配置
          "properties": {
            "transport":     { "type": "keyword" }, ## 运行方式（http、python、本地等）
            "endpoint":      { "type": "keyword" }, ## API 入口 URL（如 http:##xxx）
            "httpMethod":    { "type": "keyword" }, ## HTTP 方法（GET/POST）
            "auth": { ## 鉴权配置
              "properties": {
                "type":     { "type": "keyword" }, ## 鉴权方式（none、api_key、oauth2）
                "env_keys": { "type": "keyword" }  ## 环境变量 key 列表
              }
            },
            "rate_limits": { ## 调用速率限制
              "properties": {
                "rpm":   { "type": "integer" }, ## 每分钟请求数
                "burst": { "type": "integer" }  ## 峰值请求数
              }
            },
            "cost_hints": { ## 成本提示
              "properties": {
                "per_call_usd": { "type": "scaled_float", "scaling_factor": 10000 } ## 每次调用的美元成本
              }
            },
            "latency_hints_ms": { ## 延迟提示（毫秒）
              "properties": {
                "p50": { "type": "integer" }, ## 50 分位延迟
                "p95": { "type": "integer" }  ## 95 分位延迟
              }
            },
            "fault_tolerance": { "type": "keyword" } ## 容错等级（high/low）
          }
        },
  
        "metadata": { ## 额外元信息
          "properties": {
            "has_variables_dependencies": { "type": "boolean" }, ## 是否有变量依赖
            "user_facing":   { "type": "boolean" } ## 是否为用户可见工具
          }
        },
  
        "descriptionDev":  { "type": "text", "fields": { "keyword": { "type": "keyword", "ignore_above": 256 } } }, ## 开发者描述
        "descriptionUser": { "type": "text", "fields": { "keyword": { "type": "keyword", "ignore_above": 256 } } }, ## 面向用户的描述
  
        "inputsDeveloperSchema":      { "type": "flattened" }, ## 入参 JSON Schema（开发者态）
        "inputsDeveloperValidation":  { "type": "flattened" }, ## 入参校验规则
        "inputsDeveloperDependencies":{ "type": "flattened" }, ## 入参依赖
        "outputsSchema":              { "type": "flattened" }, ## 出参 JSON Schema
  
        "examples": { ## 示例
          "type": "nested",
          "properties": {
            "userQuery":      { "type": "text" }, ## 用户查询示例
            "parsedInputs":   { "type": "flattened" }, ## 解析后的入参
            "resolvedInputs": { "type": "flattened" }  ## 最终确认的入参
          }
        },
        "errors": { ## 错误目录
          "type": "nested",
          "properties": {
            "code":     { "type": "keyword" }, ## 错误代码
            "http":     { "type": "integer" }, ## HTTP 状态码
            "message":  { "type": "text" },    ## 错误信息
            "recovery": { "type": "text" }     ## 恢复建议
          }
        },
        "quickstarts": { ## 快速启动脚本
          "type": "nested",
          "properties": {
            "kind":    { "type": "keyword" }, ## 类型（curl/python/其他）
            "title":   { "type": "text" },    ## 脚本标题
            "content": { "type": "text" }     ## 脚本内容
          }
        },
  
        "content":    { "type": "text" }, ## 聚合搜索内容（可拼接名称/别名/描述）
        "contentEmb": { "type": "dense_vector", "dims": 1024, "index": true, "similarity": "cosine" }, ## 聚合向量（语义搜索）
        "descriptionEmb": { "type": "dense_vector", "dims": 1024, "index": true, "similarity": "cosine" }, ## 描述向量
  
        "createdBy":   { "type": "keyword" }, ## 创建人
        "createdTime": { "type": "date", "format": "strict_date_time" }, ## 创建时间
        "updatedBy":   { "type": "keyword" }, ## 更新人
        "updatedTime": { "type": "date", "format": "strict_date_time" }, ## 更新时间
        "deleteFlag":  { "type": "integer" } ## 删除标记（0/1）
      }
    }
  }
  