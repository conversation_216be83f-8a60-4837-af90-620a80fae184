#!/usr/bin/env python3
"""
MCP 连接沙盒演示

这个脚本演示了如何使用 MCP 沙盒服务来安全地连接和验证 MCP 服务器，
支持 stdio 和 http 两种传输方式，包括工具发现、安全检查等功能。
"""

import asyncio
import json
import sys
import os

# 添加 backend 目录到 Python 路径
sys.path.append('backend')

from services.mcp_sandbox_service import (
    MCPSandboxService,
    MCPSandboxRequest,
    MCPSandboxConfig,
    get_mcp_sandbox_service
)


async def demo_basic_mcp_validation():
    """演示基本的 MCP 服务器验证"""
    print("=" * 60)
    print("演示 1: 基本 MCP 服务器验证")
    print("=" * 60)
    
    # 创建 MCP 沙盒服务
    sandbox = MCPSandboxService()
    
    # 测试 stdio 传输方式
    stdio_request = MCPSandboxRequest(
        transport="stdio",
        command="uvx",
        args=["mcp-server-filesystem"],
        timeout=30
    )
    
    print("测试 stdio 传输方式:")
    print(f"传输方式: {stdio_request.transport}")
    print(f"命令: {stdio_request.command}")
    print(f"参数: {stdio_request.args}")
    print(f"超时: {stdio_request.timeout} 秒")
    
    # 注意：这里只是演示请求创建，实际连接需要真实的 MCP 服务器
    print("\n✅ stdio 请求创建成功")
    
    # 测试 http 传输方式
    http_request = MCPSandboxRequest(
        transport="http",
        url="http://localhost:3000/mcp",
        timeout=30
    )
    
    print(f"\n测试 http 传输方式:")
    print(f"传输方式: {http_request.transport}")
    print(f"URL: {http_request.url}")
    print(f"超时: {http_request.timeout} 秒")
    
    print("\n✅ http 请求创建成功")


async def demo_security_checks():
    """演示安全检查功能"""
    print("\n" + "=" * 60)
    print("演示 2: 安全检查功能")
    print("=" * 60)
    
    sandbox = MCPSandboxService()
    
    # 测试不同安全级别的请求
    test_cases = [
        {
            "name": "安全的 stdio 命令",
            "request": MCPSandboxRequest(
                transport="stdio",
                command="uvx",
                args=["mcp-server-example"]
            )
        },
        {
            "name": "安全的 http URL",
            "request": MCPSandboxRequest(
                transport="http",
                url="https://api.example.com/mcp"
            )
        },
        {
            "name": "本地 http 连接",
            "request": MCPSandboxRequest(
                transport="http",
                url="http://localhost:3000/mcp"
            )
        },
        {
            "name": "敏感端口连接",
            "request": MCPSandboxRequest(
                transport="http",
                url="http://example.com:22/mcp"
            )
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        print("-" * 40)
        
        try:
            request = test_case['request']
            warnings = sandbox._security_check(request)
            
            if warnings:
                print("⚠️  安全警告:")
                for warning in warnings:
                    print(f"   - {warning}")
            else:
                print("✅ 安全检查通过")
                
        except Exception as e:
            print(f"❌ 请求创建失败: {e}")


async def demo_dangerous_commands():
    """演示危险命令检测"""
    print("\n" + "=" * 60)
    print("演示 3: 危险命令检测")
    print("=" * 60)
    
    # 测试各种危险命令
    dangerous_commands = [
        ("rm", "删除命令"),
        ("sudo", "提权命令"),
        ("curl", "网络下载命令"),
        ("ssh", "远程连接命令"),
        ("/bin/sh", "Shell 执行")
    ]
    
    print("测试危险命令检测:")
    
    for cmd, description in dangerous_commands:
        print(f"\n测试命令: {cmd} ({description})")
        try:
            MCPSandboxRequest(
                transport="stdio",
                command=cmd,
                args=["test"]
            )
            print(f"❌ 危险命令未被阻止: {cmd}")
        except ValueError as e:
            print(f"✅ 正确阻止危险命令: {e}")
    
    # 测试允许的命令
    print(f"\n测试允许的命令:")
    allowed_commands = [
        ("uvx", "Python 包执行器"),
        ("npx", "Node.js 包执行器"),
        ("python", "Python 解释器"),
        ("/usr/bin/node", "Node.js 解释器")
    ]
    
    for cmd, description in allowed_commands:
        try:
            request = MCPSandboxRequest(
                transport="stdio",
                command=cmd,
                args=["--version"]
            )
            print(f"✅ 允许的命令: {cmd} ({description})")
        except ValueError as e:
            print(f"❌ 意外阻止了安全命令: {cmd} - {e}")


async def demo_mcp_protocol_messages():
    """演示 MCP 协议消息"""
    print("\n" + "=" * 60)
    print("演示 4: MCP 协议消息")
    print("=" * 60)
    
    config = MCPSandboxConfig()
    
    # 初始化消息
    init_message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": config.PROTOCOL_VERSION,
            "capabilities": {},
            "clientInfo": config.CLIENT_INFO
        }
    }
    
    print("MCP 初始化消息:")
    print(json.dumps(init_message, indent=2, ensure_ascii=False))
    
    # 工具列表请求
    tools_message = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/list",
        "params": {}
    }
    
    print(f"\n工具列表请求消息:")
    print(json.dumps(tools_message, indent=2, ensure_ascii=False))
    
    # 工具调用消息
    tool_call_message = {
        "jsonrpc": "2.0",
        "id": 3,
        "method": "tools/call",
        "params": {
            "name": "get_weather",
            "arguments": {
                "location": "北京",
                "units": "metric"
            }
        }
    }
    
    print(f"\n工具调用消息:")
    print(json.dumps(tool_call_message, indent=2, ensure_ascii=False))


async def demo_response_parsing():
    """演示响应解析功能"""
    print("\n" + "=" * 60)
    print("演示 5: 响应解析功能")
    print("=" * 60)
    
    sandbox = MCPSandboxService()
    
    # 模拟服务器信息响应
    server_response = {
        "jsonrpc": "2.0",
        "id": 1,
        "result": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {},
                "resources": {},
                "prompts": {}
            },
            "serverInfo": {
                "name": "filesystem-server",
                "version": "2.1.0",
                "description": "MCP server for filesystem operations"
            }
        }
    }
    
    print("模拟服务器响应:")
    print(json.dumps(server_response, indent=2, ensure_ascii=False))
    
    # 解析服务器信息
    server_info = sandbox._parse_server_info(server_response)
    
    print(f"\n解析的服务器信息:")
    print(f"协议版本: {server_info.protocol_version}")
    print(f"能力: {json.dumps(server_info.capabilities, ensure_ascii=False)}")
    print(f"服务器信息: {json.dumps(server_info.server_info, ensure_ascii=False)}")
    
    # 模拟工具列表响应
    tools_response = {
        "jsonrpc": "2.0",
        "id": 2,
        "result": {
            "tools": [
                {
                    "name": "read_file",
                    "description": "读取文件内容",
                    "inputSchema": {
                        "type": "object",
                        "required": ["path"],
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "要读取的文件路径"
                            }
                        }
                    }
                },
                {
                    "name": "list_directory",
                    "description": "列出目录内容",
                    "inputSchema": {
                        "type": "object",
                        "required": ["path"],
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "要列出的目录路径"
                            },
                            "recursive": {
                                "type": "boolean",
                                "description": "是否递归列出子目录",
                                "default": False
                            }
                        }
                    }
                }
            ]
        }
    }
    
    print(f"\n模拟工具列表响应:")
    print(json.dumps(tools_response, indent=2, ensure_ascii=False))
    
    # 解析工具列表
    tools = sandbox._parse_tools_list(tools_response)
    
    print(f"\n解析的工具列表 ({len(tools)} 个工具):")
    for i, tool in enumerate(tools, 1):
        print(f"{i}. {tool.name}")
        print(f"   描述: {tool.description}")
        print(f"   输入Schema: {json.dumps(tool.input_schema, ensure_ascii=False)}")


async def demo_quickstart_generation():
    """演示快速开始代码生成"""
    print("\n" + "=" * 60)
    print("演示 6: 快速开始代码生成")
    print("=" * 60)
    
    sandbox = MCPSandboxService()
    
    # stdio 快速开始
    stdio_request = MCPSandboxRequest(
        transport="stdio",
        command="uvx",
        args=["mcp-server-filesystem", "--root", "/tmp"]
    )
    
    from services.mcp_sandbox_service import MCPValidationResult
    stdio_result = MCPValidationResult(success=True, transport="stdio")
    
    stdio_quickstart = sandbox._generate_quickstart(stdio_request, stdio_result)
    
    print("stdio 传输方式快速开始:")
    print("-" * 40)
    print(f"命令行调用:")
    print(stdio_quickstart["command"])
    
    print(f"\nMCP 初始化消息:")
    print(stdio_quickstart["mcp_init"])
    
    print(f"\n工具列表请求:")
    print(stdio_quickstart["mcp_tools"])
    
    # http 快速开始
    http_request = MCPSandboxRequest(
        transport="http",
        url="http://localhost:3000/mcp"
    )
    
    http_result = MCPValidationResult(success=True, transport="http")
    http_quickstart = sandbox._generate_quickstart(http_request, http_result)
    
    print(f"\n\nhttp 传输方式快速开始:")
    print("-" * 40)
    print(f"cURL 命令:")
    print(http_quickstart["curl"])
    
    print(f"\nPython 代码:")
    print(http_quickstart["python"])


async def demo_configuration_and_limits():
    """演示配置和限制"""
    print("\n" + "=" * 60)
    print("演示 7: 配置和限制")
    print("=" * 60)
    
    config = MCPSandboxConfig()
    
    print("MCP 沙盒配置:")
    print(f"默认超时: {config.DEFAULT_TIMEOUT} 秒")
    print(f"最大超时: {config.MAX_TIMEOUT} 秒")
    print(f"连接超时: {config.CONNECT_TIMEOUT} 秒")
    print(f"协议版本: {config.PROTOCOL_VERSION}")
    print(f"客户端信息: {json.dumps(config.CLIENT_INFO, ensure_ascii=False)}")
    
    print(f"\n支持的传输方式:")
    for transport in config.SUPPORTED_TRANSPORTS:
        print(f"  - {transport}")
    
    print(f"\n允许的命令前缀 ({len(config.ALLOWED_COMMAND_PREFIXES)} 个):")
    for prefix in config.ALLOWED_COMMAND_PREFIXES:
        print(f"  - {prefix}")
    
    print(f"\n禁止的命令模式 ({len(config.FORBIDDEN_COMMAND_PATTERNS)} 个):")
    for pattern in config.FORBIDDEN_COMMAND_PATTERNS:
        print(f"  - {pattern}")
    
    # 测试请求验证
    print(f"\n请求验证测试:")
    
    # 有效请求
    try:
        valid_request = MCPSandboxRequest(
            transport="stdio",
            command="uvx",
            args=["mcp-server-example"],
            timeout=30
        )
        print("✅ 有效 stdio 请求创建成功")
    except Exception as e:
        print(f"❌ 有效请求创建失败: {e}")
    
    try:
        valid_request = MCPSandboxRequest(
            transport="http",
            url="https://api.example.com/mcp",
            timeout=30
        )
        print("✅ 有效 http 请求创建成功")
    except Exception as e:
        print(f"❌ 有效请求创建失败: {e}")
    
    # 无效请求测试
    invalid_cases = [
        ("无效传输方式", "invalid", None, None),
        ("超时过大", "stdio", "uvx", 200),
        ("空 URL", "http", None, None),
    ]
    
    for case_name, transport, command, timeout in invalid_cases:
        try:
            kwargs = {"transport": transport}
            if command:
                kwargs["command"] = command
            if timeout:
                kwargs["timeout"] = timeout
            if transport == "http" and not command:
                kwargs["url"] = ""
            
            MCPSandboxRequest(**kwargs)
            print(f"❌ {case_name}: 应该失败但成功了")
        except Exception as e:
            print(f"✅ {case_name}: 正确捕获错误")


async def demo_mock_validation():
    """演示模拟验证过程"""
    print("\n" + "=" * 60)
    print("演示 8: 模拟验证过程")
    print("=" * 60)
    
    print("注意: 这是一个模拟演示，实际使用需要真实的 MCP 服务器")
    
    # 模拟成功的验证结果
    from services.mcp_sandbox_service import MCPValidationResult, MCPServerInfo, MCPToolInfo
    
    server_info = MCPServerInfo(
        name="demo-server",
        version="1.0.0",
        protocol_version="2024-11-05",
        capabilities={"tools": {}, "resources": {}},
        server_info={"description": "演示 MCP 服务器"}
    )
    
    tools = [
        MCPToolInfo(
            name="get_weather",
            description="获取天气信息",
            input_schema={
                "type": "object",
                "required": ["location"],
                "properties": {
                    "location": {"type": "string", "description": "城市名称"}
                }
            }
        ),
        MCPToolInfo(
            name="calculate",
            description="执行数学计算",
            input_schema={
                "type": "object",
                "required": ["expression"],
                "properties": {
                    "expression": {"type": "string", "description": "数学表达式"}
                }
            }
        )
    ]
    
    result = MCPValidationResult(
        success=True,
        transport="stdio",
        server_info=server_info,
        available_tools=tools,
        connection_time_ms=1500,
        security_warnings=["连接到本地服务器"],
        quickstart={
            "command": "uvx mcp-server-demo",
            "mcp_init": '{"jsonrpc": "2.0", "method": "initialize"}'
        }
    )
    
    print("模拟验证结果:")
    print(f"成功: {result.success}")
    print(f"传输方式: {result.transport}")
    print(f"连接时间: {result.connection_time_ms}ms")
    
    print(f"\n服务器信息:")
    print(f"名称: {result.server_info.server_info.get('description', 'N/A')}")
    print(f"版本: {result.server_info.version}")
    print(f"协议版本: {result.server_info.protocol_version}")
    
    print(f"\n可用工具 ({len(result.available_tools)} 个):")
    for i, tool in enumerate(result.available_tools, 1):
        print(f"{i}. {tool.name}: {tool.description}")
    
    print(f"\n安全警告:")
    for warning in result.security_warnings:
        print(f"⚠️  {warning}")
    
    print(f"\n快速开始:")
    print(f"命令: {result.quickstart.get('command', 'N/A')}")


async def main():
    """主函数"""
    print("MCP 连接沙盒服务演示")
    print("这个演示展示了如何使用 MCP 沙盒服务安全地连接和验证 MCP 服务器")
    
    try:
        await demo_basic_mcp_validation()
        await demo_security_checks()
        await demo_dangerous_commands()
        await demo_mcp_protocol_messages()
        await demo_response_parsing()
        await demo_quickstart_generation()
        await demo_configuration_and_limits()
        await demo_mock_validation()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\nMCP 连接沙盒服务的主要功能:")
        print("✓ 安全的 MCP 服务器连接和验证")
        print("✓ 支持 stdio 和 http 两种传输方式")
        print("✓ 命令白名单和危险模式检测")
        print("✓ MCP 协议消息处理和解析")
        print("✓ 工具发现和 Schema 提取")
        print("✓ 快速开始代码生成")
        print("✓ 连接超时和错误处理")
        print("✓ 安全警告和风险评估")
        
        print("\n安全特性:")
        print("• 命令白名单控制 (uvx, npx, python 等)")
        print("• 危险命令检测和阻止")
        print("• 本地地址和敏感端口警告")
        print("• 连接超时和资源限制")
        print("• 协议版本验证")
        
        print("\n支持的功能:")
        print("• MCP 协议 2024-11-05 版本")
        print("• 工具列表获取和解析")
        print("• 服务器能力检测")
        print("• JSON-RPC 消息处理")
        print("• 多种 MCP 服务器类型")
        
        print("\n注意事项:")
        print("⚠️  需要真实的 MCP 服务器进行实际连接")
        print("⚠️  stdio 传输需要可执行的命令")
        print("⚠️  http 传输需要网络访问权限")
        print("⚠️  连接时间受超时限制")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())