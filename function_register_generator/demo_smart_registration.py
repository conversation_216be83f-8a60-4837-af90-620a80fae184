#!/usr/bin/env python3
"""
智能工具注册演示脚本
"""
import asyncio
import json
from datetime import datetime

async def demo_smart_registration():
    """演示智能工具注册功能"""
    print("=== 智能工具注册平台演示 ===")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 导入必要的模块
    try:
        from backend.routers.tools import (
            smart_register_tool, modify_tool, suggest_tool_improvements,
            batch_register_tools, SmartRegistrationRequest, 
            ToolModificationRequest, _calculate_tool_quality_score,
            _infer_schema_from_data
        )
        print("✅ 成功导入智能注册模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    print("\n" + "="*60)
    print("1. 智能 cURL 工具注册演示")
    print("="*60)
    
    # 1. 智能 cURL 注册
    smart_curl_request = SmartRegistrationRequest(
        display_name="GitHub 用户信息 API",
        description="获取指定用户的 GitHub 个人资料信息",
        category="api",
        curl_command="curl -H 'Accept: application/vnd.github.v3+json' -H 'User-Agent: MyApp/1.0' https://api.github.com/users/octocat",
        auto_complete=True,
        auto_validate=True,
        generate_examples=True,
        expected_inputs={"username": "octocat"},
        expected_outputs={
            "login": "octocat",
            "id": 583231,
            "name": "The Octocat",
            "company": "GitHub",
            "public_repos": 8
        },
        use_cases=[
            "获取用户基本信息",
            "验证用户是否存在",
            "获取用户仓库统计"
        ]
    )
    
    try:
        result = await smart_register_tool(smart_curl_request)
        print(f"✅ 智能 cURL 注册成功:")
        print(f"   工具ID: {result.tool_id}")
        print(f"   置信度: {result.confidence_score}")
        print(f"   自动生成字段: {', '.join(result.auto_generated_fields)}")
        print(f"   补全建议数量: {len(result.completion_suggestions)}")
        for suggestion in result.completion_suggestions[:3]:
            print(f"   - {suggestion}")
        
        if result.validation_result:
            print(f"   验证状态: {'✅ 通过' if result.validation_result.get('success') else '❌ 失败'}")
            if result.validation_result.get('latency_ms'):
                print(f"   响应延迟: {result.validation_result['latency_ms']}ms")
        
    except Exception as e:
        print(f"❌ 智能 cURL 注册失败: {e}")
    
    print("\n" + "="*60)
    print("2. 自然语言工具注册演示")
    print("="*60)
    
    # 2. 自然语言注册
    natural_request = SmartRegistrationRequest(
        description="创建一个可以生成安全密码的工具，支持指定长度、包含大小写字母、数字和特殊字符",
        category="security",
        auto_complete=True,
        auto_validate=False,  # 这种工具无法直接验证
        generate_examples=True,
        expected_inputs={
            "length": 12,
            "include_uppercase": True,
            "include_lowercase": True,
            "include_numbers": True,
            "include_symbols": True,
            "exclude_ambiguous": False
        },
        expected_outputs={
            "password": "Kx9#mP2$vL8@",
            "strength": "strong",
            "entropy": 78.5
        },
        use_cases=[
            "生成用户账户密码",
            "创建API密钥",
            "生成临时访问令牌"
        ]
    )
    
    try:
        result = await smart_register_tool(natural_request)
        print(f"✅ 自然语言注册成功:")
        print(f"   工具ID: {result.tool_id}")
        print(f"   工具名称: {result.tool_definition.get('displayName', 'N/A')}")
        print(f"   推断的传输方式: {result.tool_definition.get('transport', 'N/A')}")
        print(f"   输入参数数量: {len(result.tool_definition.get('inputsDeveloperSchema', {}).get('properties', {}))}")
        print(f"   改进建议数量: {len(result.improvement_suggestions)}")
        
    except Exception as e:
        print(f"❌ 自然语言注册失败: {e}")
    
    print("\n" + "="*60)
    print("3. 工具修改和优化演示")
    print("="*60)
    
    # 3. 工具修改
    if 'result' in locals() and result.success:
        modification_request = ToolModificationRequest(
            tool_id=result.tool_id,
            modifications={
                "displayName": "高级密码生成器",
                "descriptionUser": "生成符合安全标准的强密码，支持多种字符集和自定义规则",
                "category": "security",
                "aliases": ["password_gen", "pwd_generator", "secure_password"],
                "inputsDeveloperSchema": {
                    "type": "object",
                    "properties": {
                        "length": {
                            "type": "integer",
                            "minimum": 4,
                            "maximum": 128,
                            "default": 12,
                            "description": "密码长度"
                        },
                        "character_sets": {
                            "type": "object",
                            "properties": {
                                "uppercase": {"type": "boolean", "default": True},
                                "lowercase": {"type": "boolean", "default": True},
                                "numbers": {"type": "boolean", "default": True},
                                "symbols": {"type": "boolean", "default": True}
                            },
                            "description": "包含的字符集"
                        },
                        "exclude_ambiguous": {
                            "type": "boolean",
                            "default": False,
                            "description": "排除易混淆字符 (0, O, l, I 等)"
                        }
                    },
                    "required": ["length"]
                }
            },
            regenerate_examples=True,
            revalidate=False
        )
        
        try:
            mod_result = await modify_tool(modification_request)
            print(f"✅ 工具修改成功:")
            print(f"   工具ID: {mod_result.tool_id}")
            print(f"   新名称: {mod_result.tool_definition.get('displayName')}")
            print(f"   别名数量: {len(mod_result.tool_definition.get('aliases', []))}")
            print(f"   示例数量: {len(mod_result.tool_definition.get('examples', []))}")
            
        except Exception as e:
            print(f"❌ 工具修改失败: {e}")
    
    print("\n" + "="*60)
    print("4. 工具质量分析演示")
    print("="*60)
    
    # 4. 质量分析
    test_tools = [
        {
            "name": "高质量工具",
            "tool": {
                "descriptionUser": "这是一个功能完整的API工具，用于获取天气信息。支持多种城市查询方式，返回详细的天气数据包括温度、湿度、风速等信息。",
                "inputsDeveloperSchema": {
                    "type": "object",
                    "properties": {
                        "city": {"type": "string", "description": "城市名称"},
                        "country": {"type": "string", "description": "国家代码"},
                        "units": {"type": "string", "enum": ["metric", "imperial"], "description": "单位制"}
                    },
                    "required": ["city"]
                },
                "examples": [
                    {"name": "查询北京天气", "input": {"city": "Beijing", "country": "CN"}},
                    {"name": "查询纽约天气", "input": {"city": "New York", "country": "US"}}
                ],
                "aliases": ["weather_api", "天气查询", "weather_check"],
                "category": "weather",
                "runtime": {"endpoint": "https://api.openweathermap.org/data/2.5/weather"}
            }
        },
        {
            "name": "低质量工具",
            "tool": {
                "descriptionUser": "工具",
                "inputsDeveloperSchema": {"type": "object", "properties": {}},
                "examples": [],
                "aliases": [],
                "category": "utility",
                "runtime": {}
            }
        }
    ]
    
    for test_case in test_tools:
        score = _calculate_tool_quality_score(test_case["tool"])
        print(f"   {test_case['name']}: {score}分")
        
        # 生成改进建议
        try:
            from backend.routers.tools import _generate_improvement_suggestions
            suggestions = await _generate_improvement_suggestions(test_case["tool"])
            if suggestions:
                print(f"     改进建议: {', '.join(suggestions[:2])}")
        except:
            pass
    
    print("\n" + "="*60)
    print("5. Schema 推断演示")
    print("="*60)
    
    # 5. Schema 推断
    test_data_cases = [
        {
            "name": "用户信息",
            "data": {
                "name": "张三",
                "age": 28,
                "email": "<EMAIL>",
                "is_active": True,
                "salary": 8500.50,
                "skills": ["Python", "JavaScript", "SQL"],
                "address": {
                    "city": "北京",
                    "district": "朝阳区"
                }
            }
        },
        {
            "name": "产品信息",
            "data": {
                "product_id": "P001",
                "name": "智能手机",
                "price": 2999.99,
                "in_stock": True,
                "categories": ["电子产品", "通讯设备"],
                "specifications": {
                    "screen_size": "6.1英寸",
                    "storage": "128GB"
                }
            }
        }
    ]
    
    for case in test_data_cases:
        schema = _infer_schema_from_data(case["data"], "input")
        print(f"   {case['name']} Schema:")
        print(f"     属性数量: {len(schema['properties'])}")
        print(f"     属性类型: {', '.join(set(prop.get('type', 'unknown') for prop in schema['properties'].values()))}")
    
    print("\n" + "="*60)
    print("6. 批量注册演示")
    print("="*60)
    
    # 6. 批量注册
    batch_requests = [
        SmartRegistrationRequest(
            display_name=f"测试API工具{i}",
            description=f"这是第{i}个测试API工具，用于演示批量注册功能",
            category="test",
            curl_command=f"curl https://api.example.com/test{i}",
            auto_complete=False,  # 简化处理
            auto_validate=False
        ) for i in range(1, 4)
    ]
    
    try:
        batch_result = await batch_register_tools(batch_requests)
        print(f"✅ 批量注册完成:")
        print(f"   总数: {batch_result['total']}")
        print(f"   成功: {batch_result['success_count']}")
        print(f"   失败: {batch_result['failure_count']}")
        
        for result in batch_result['results']:
            status = "✅" if result.get('success') else "❌"
            print(f"   {status} 工具{result['index'] + 1}: {result.get('tool_id', result.get('error', 'N/A'))}")
            
    except Exception as e:
        print(f"❌ 批量注册失败: {e}")
    
    print("\n" + "="*60)
    print("演示完成")
    print("="*60)
    print("✅ 智能工具注册功能演示完成")
    print("🎯 主要特性:")
    print("   - 智能 cURL 解析和补全")
    print("   - 自然语言工具生成")
    print("   - 自动验证和质量评估")
    print("   - 工具修改和优化建议")
    print("   - 批量注册支持")
    print("   - Schema 自动推断")
    print("📝 注意: 实际使用时需要配置 LLM 服务和沙盒环境")

async def demo_advanced_features():
    """演示高级功能"""
    print("\n" + "="*60)
    print("高级功能演示")
    print("="*60)
    
    # 演示复杂 cURL 解析
    complex_curl_cases = [
        {
            "name": "POST 请求with JSON",
            "curl": "curl -X POST 'https://api.example.com/users' -H 'Content-Type: application/json' -H 'Authorization: Bearer token123' -d '{\"name\":\"John\",\"email\":\"<EMAIL>\"}'"
        },
        {
            "name": "带查询参数的GET",
            "curl": "curl 'https://api.github.com/search/repositories?q=python&sort=stars&order=desc' -H 'Accept: application/vnd.github.v3+json'"
        },
        {
            "name": "文件上传",
            "curl": "curl -X POST 'https://api.example.com/upload' -F 'file=@document.pdf' -F 'description=Important document'"
        }
    ]
    
    print("复杂 cURL 命令解析:")
    for case in complex_curl_cases:
        print(f"   📋 {case['name']}")
        print(f"      命令长度: {len(case['curl'])} 字符")
        
        # 这里可以调用实际的解析函数
        try:
            from parsers.curl_parser import parse_curl
            parsed = parse_curl(case['curl'])
            print(f"      解析结果: {parsed['method']} {parsed['url'][:50]}...")
        except Exception as e:
            print(f"      解析失败: {e}")
    
    # 演示智能建议生成
    print("\n智能建议生成:")
    suggestion_scenarios = [
        "API 工具缺少错误处理",
        "Python 工具没有类型注解",
        "工具描述过于简单",
        "缺少使用示例",
        "输入参数验证不足"
    ]
    
    for scenario in suggestion_scenarios:
        print(f"   💡 场景: {scenario}")
        print(f"      建议: 根据工具类型和质量分析生成针对性建议")

if __name__ == "__main__":
    print("启动智能工具注册演示...")
    asyncio.run(demo_smart_registration())
    asyncio.run(demo_advanced_features())