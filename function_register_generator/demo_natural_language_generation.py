#!/usr/bin/env python3
"""
自然语言工具生成演示脚本
"""
import asyncio
import json
from datetime import datetime

async def demo_natural_language_generation():
    """演示自然语言工具生成功能"""
    print("=== 自然语言工具生成平台演示 ===")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 导入必要的模块
    try:
        from backend.routers.tools import (
            generate_tool_from_natural_language, preview_generated_tool,
            NaturalLanguageGenerationRequest, ToolPreviewRequest,
            _analyze_user_description, _extract_core_function,
            _infer_transport_type, _infer_input_structure,
            _infer_category, _generate_tool_name, _generate_python_code,
            _generate_schema_documentation, _extract_input_parameters
        )
        print("✅ 成功导入自然语言生成模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    print("\n" + "="*70)
    print("1. 简单工具生成演示")
    print("="*70)
    
    # 1. 简单工具生成
    simple_requests = [
        {
            "name": "密码生成器",
            "request": NaturalLanguageGenerationRequest(
                description="创建一个可以生成安全密码的工具，支持指定长度和字符类型",
                complexity_level="simple",
                preferred_transport="python",
                generate_examples=True
            )
        },
        {
            "name": "URL 短链接",
            "request": NaturalLanguageGenerationRequest(
                description="将长URL转换为短链接的工具，支持自定义域名",
                complexity_level="simple",
                preferred_transport="http",
                generate_examples=True
            )
        },
        {
            "name": "文本统计",
            "request": NaturalLanguageGenerationRequest(
                description="统计文本中的字符数、单词数、行数等信息",
                complexity_level="simple",
                preferred_transport="python",
                generate_examples=True
            )
        }
    ]
    
    for case in simple_requests:
        print(f"\n🔧 生成 {case['name']}:")
        try:
            result = await generate_tool_from_natural_language(case["request"])
            
            if result.success:
                tool_def = result.tool_definition
                print(f"   ✅ 生成成功")
                print(f"   工具ID: {tool_def['toolId']}")
                print(f"   工具名称: {tool_def['displayName']}")
                print(f"   传输方式: {tool_def['transport']}")
                print(f"   分类: {tool_def['category']}")
                print(f"   置信度: {result.confidence_score:.2f}")
                print(f"   生成时间: {result.generation_time_ms}ms")
                
                # 显示输入参数
                input_props = tool_def.get('inputsDeveloperSchema', {}).get('properties', {})
                if input_props:
                    print(f"   输入参数: {', '.join(input_props.keys())}")
                
                # 显示建议
                if result.suggestions:
                    print(f"   建议: {result.suggestions[0]}")
            else:
                print(f"   ❌ 生成失败: {result.message}")
                
        except Exception as e:
            print(f"   ❌ 生成异常: {e}")
    
    print("\n" + "="*70)
    print("2. 复杂工具生成演示")
    print("="*70)
    
    # 2. 复杂工具生成
    complex_request = NaturalLanguageGenerationRequest(
        description="创建一个智能客服聊天机器人工具，能够理解用户问题并提供相关答案，支持多轮对话和上下文记忆",
        use_cases=[
            "客户服务自动化",
            "FAQ 自动回答",
            "用户问题分类和路由",
            "多语言客服支持"
        ],
        target_users=["客服人员", "产品经理", "开发者"],
        domain="customer_service",
        example_inputs={
            "user_message": "我的订单什么时候能到？",
            "conversation_id": "conv_123",
            "user_context": {
                "user_id": "user_456",
                "order_id": "order_789",
                "language": "zh-CN"
            }
        },
        example_outputs={
            "response": "根据您的订单信息，预计3-5个工作日内送达",
            "intent": "order_inquiry",
            "confidence": 0.95,
            "suggested_actions": ["check_order_status", "provide_tracking_info"],
            "conversation_context": {
                "last_topic": "delivery",
                "user_satisfaction": "pending"
            }
        },
        complexity_level="complex",
        preferred_transport="http",
        include_validation=True,
        generate_examples=True,
        tool_name_hint="智能客服助手",
        category_hint="ai_assistant",
        auto_validate=False  # 复杂工具跳过自动验证
    )
    
    try:
        print("🤖 生成智能客服聊天机器人:")
        result = await generate_tool_from_natural_language(complex_request)
        
        if result.success:
            tool_def = result.tool_definition
            print(f"   ✅ 复杂工具生成成功")
            print(f"   工具名称: {tool_def['displayName']}")
            print(f"   分类: {tool_def['category']}")
            print(f"   置信度: {result.confidence_score:.2f}")
            print(f"   生成时间: {result.generation_time_ms}ms")
            
            # 分析结果
            if result.analysis:
                print(f"   核心功能: {result.analysis.get('core_function', 'N/A')}")
                print(f"   推荐传输: {result.analysis.get('recommended_transport', 'N/A')}")
                print(f"   复杂度: {result.analysis.get('complexity', 'N/A')}")
            
            # 输入参数分析
            input_schema = tool_def.get('inputsDeveloperSchema', {})
            if input_schema.get('properties'):
                print(f"   输入参数数量: {len(input_schema['properties'])}")
                for param_name, param_def in list(input_schema['properties'].items())[:3]:
                    print(f"     - {param_name}: {param_def.get('type', 'unknown')} ({param_def.get('description', 'N/A')})")
            
            # 替代方案
            if result.alternatives:
                print(f"   替代方案数量: {len(result.alternatives)}")
                for alt in result.alternatives[:2]:
                    print(f"     - {alt['name']}: {alt['description']}")
            
            # 改进建议
            if result.suggestions:
                print(f"   改进建议:")
                for suggestion in result.suggestions[:3]:
                    print(f"     💡 {suggestion}")
        else:
            print(f"   ❌ 复杂工具生成失败: {result.message}")
            
    except Exception as e:
        print(f"   ❌ 复杂工具生成异常: {e}")
    
    print("\n" + "="*70)
    print("3. 工具预览演示")
    print("="*70)
    
    # 3. 工具预览
    if 'result' in locals() and result.success:
        preview_types = ["basic", "detailed", "interactive"]
        
        for preview_type in preview_types:
            print(f"\n📋 {preview_type.upper()} 预览:")
            try:
                preview_request = ToolPreviewRequest(
                    tool_definition=result.tool_definition,
                    preview_type=preview_type,
                    include_examples=True,
                    include_schema_docs=True
                )
                
                preview_result = await preview_generated_tool(preview_request)
                
                if preview_result.success:
                    preview_data = preview_result.preview_data
                    print(f"   工具名称: {preview_data.get('display_name')}")
                    print(f"   传输方式: {preview_data.get('transport')}")
                    print(f"   参数数量: {len(preview_data.get('input_parameters', []))}")
                    
                    if preview_type == "interactive" and preview_result.interactive_demo:
                        demo = preview_result.interactive_demo
                        print(f"   表单字段: {len(demo.get('form_config', []))}")
                        print(f"   API端点: {demo.get('api_endpoint', 'N/A')}")
                    
                    if preview_result.schema_documentation:
                        doc_lines = preview_result.schema_documentation.split('\n')
                        print(f"   文档行数: {len(doc_lines)}")
                        print(f"   文档预览: {doc_lines[0][:50]}...")
                else:
                    print(f"   ❌ 预览失败: {preview_result.warnings}")
                    
            except Exception as e:
                print(f"   ❌ 预览异常: {e}")
    
    print("\n" + "="*70)
    print("4. 核心功能单元测试演示")
    print("="*70)
    
    # 4. 核心功能测试
    print("\n🧪 核心功能提取测试:")
    core_function_tests = [
        "生成随机密码",
        "查询用户信息", 
        "计算BMI指数",
        "发送邮件通知",
        "处理图片数据",
        "其他未知功能"
    ]
    
    for description in core_function_tests:
        core_function = _extract_core_function(description)
        print(f"   '{description}' -> '{core_function}'")
    
    print("\n🚀 传输方式推断测试:")
    transport_tests = [
        ("调用天气API获取数据", None),
        ("计算数学公式", None),
        ("处理文本内容", None),
        ("网络请求接口", None),
        ("Python脚本执行", None),
        ("任意描述", "stdio")
    ]
    
    for description, preferred in transport_tests:
        request = NaturalLanguageGenerationRequest(
            description=description,
            preferred_transport=preferred
        )
        transport = _infer_transport_type(request)
        preferred_str = f" (首选: {preferred})" if preferred else ""
        print(f"   '{description}'{preferred_str} -> '{transport}'")
    
    print("\n📝 分类推断测试:")
    category_tests = [
        ("调用API接口", None),
        ("处理文件数据", None),
        ("发送邮件通知", None),
        ("加密用户密码", None),
        ("分析销售数据", None),
        ("任意描述", "custom_category")
    ]
    
    for description, hint in category_tests:
        request = NaturalLanguageGenerationRequest(
            description=description,
            category_hint=hint
        )
        category = _infer_category(request)
        hint_str = f" (提示: {hint})" if hint else ""
        print(f"   '{description}'{hint_str} -> '{category}'")
    
    print("\n🏷️ 工具名称生成测试:")
    name_tests = [
        "生成随机密码的工具",
        "查询天气信息",
        "计算用户BMI指数",
        "发送邮件通知系统",
        "的一个可以用于处理",
        ""
    ]
    
    for description in name_tests:
        name = _generate_tool_name(description)
        print(f"   '{description}' -> '{name}'")
    
    print("\n" + "="*70)
    print("5. Schema 和文档生成演示")
    print("="*70)
    
    # 5. Schema 和文档生成
    sample_tool_def = {
        "displayName": "用户管理工具",
        "descriptionUser": "管理用户账户信息，支持创建、查询、更新和删除操作",
        "transport": "http",
        "category": "user_management",
        "inputsDeveloperSchema": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["create", "read", "update", "delete"],
                    "description": "操作类型"
                },
                "user_id": {
                    "type": "string",
                    "description": "用户ID"
                },
                "user_data": {
                    "type": "object",
                    "description": "用户数据",
                    "properties": {
                        "name": {"type": "string", "description": "用户姓名"},
                        "email": {"type": "string", "format": "email", "description": "邮箱地址"},
                        "age": {"type": "integer", "minimum": 0, "description": "年龄"}
                    }
                }
            },
            "required": ["action"]
        },
        "outputsSchema": {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "description": "操作是否成功"},
                "user": {"type": "object", "description": "用户信息"},
                "message": {"type": "string", "description": "响应消息"}
            }
        }
    }
    
    print("\n📊 参数提取演示:")
    parameters = _extract_input_parameters(sample_tool_def)
    for param in parameters:
        required_mark = " (必需)" if param["required"] else " (可选)"
        default_info = f", 默认: {param['default']}" if param.get("default") is not None else ""
        enum_info = f", 选项: {param['enum']}" if param.get("enum") else ""
        print(f"   - {param['name']} ({param['type']}){required_mark}: {param['description']}{default_info}{enum_info}")
    
    print("\n📚 文档生成演示:")
    documentation = _generate_schema_documentation(sample_tool_def)
    doc_lines = documentation.split('\n')
    print("   生成的文档:")
    for line in doc_lines[:10]:  # 显示前10行
        print(f"   {line}")
    if len(doc_lines) > 10:
        print(f"   ... 还有 {len(doc_lines) - 10} 行")
    
    print("\n" + "="*70)
    print("6. Python 代码生成演示")
    print("="*70)
    
    # 6. Python 代码生成
    code_generation_cases = [
        {
            "description": "计算两个数字的和",
            "analysis": {"recommended_transport": "python"}
        },
        {
            "description": "验证邮箱地址格式",
            "analysis": {"recommended_transport": "python"}
        },
        {
            "description": "生成UUID标识符",
            "analysis": {"recommended_transport": "python"}
        }
    ]
    
    for case in code_generation_cases:
        print(f"\n💻 生成代码: {case['description']}")
        request = NaturalLanguageGenerationRequest(description=case["description"])
        code = _generate_python_code(request, case["analysis"])
        
        code_lines = code.split('\n')
        print("   生成的代码:")
        for line in code_lines[:8]:  # 显示前8行
            print(f"   {line}")
        if len(code_lines) > 8:
            print(f"   ... 还有 {len(code_lines) - 8} 行")
    
    print("\n" + "="*70)
    print("7. 输入结构推断演示")
    print("="*70)
    
    # 7. 输入结构推断
    structure_tests = [
        {
            "name": "基于示例数据",
            "request": NaturalLanguageGenerationRequest(
                description="用户信息工具",
                example_inputs={
                    "name": "张三",
                    "age": 25,
                    "active": True,
                    "tags": ["developer", "python"],
                    "profile": {"bio": "软件开发者"}
                }
            )
        },
        {
            "name": "基于描述推断",
            "request": NaturalLanguageGenerationRequest(
                description="查询用户文件信息，支持按时间和数量筛选"
            )
        },
        {
            "name": "搜索功能",
            "request": NaturalLanguageGenerationRequest(
                description="搜索产品信息"
            )
        }
    ]
    
    for test_case in structure_tests:
        print(f"\n🏗️ {test_case['name']}:")
        structure = _infer_input_structure(test_case["request"])
        
        print(f"   Schema 类型: {structure.get('type')}")
        properties = structure.get('properties', {})
        print(f"   参数数量: {len(properties)}")
        
        for prop_name, prop_def in list(properties.items())[:4]:  # 显示前4个参数
            print(f"     - {prop_name}: {prop_def.get('type')} - {prop_def.get('description', 'N/A')}")
        
        if len(properties) > 4:
            print(f"     ... 还有 {len(properties) - 4} 个参数")
        
        required = structure.get('required', [])
        if required:
            print(f"   必需参数: {', '.join(required)}")
    
    print("\n" + "="*70)
    print("演示完成")
    print("="*70)
    print("✅ 自然语言工具生成功能演示完成")
    print("🎯 主要特性:")
    print("   - 智能描述分析和理解")
    print("   - 多种传输方式自动推断")
    print("   - 输入输出 Schema 自动生成")
    print("   - Python 代码模板生成")
    print("   - 工具预览和文档生成")
    print("   - 替代方案和改进建议")
    print("   - 复杂度级别支持")
    print("   - 交互式演示界面")
    print("📝 注意: 实际使用时需要配置 LLM 服务以获得更好的生成效果")

async def demo_advanced_nl_features():
    """演示高级自然语言功能"""
    print("\n" + "="*70)
    print("高级自然语言功能演示")
    print("="*70)
    
    # 多语言支持演示
    print("\n🌍 多语言描述支持:")
    multilingual_descriptions = [
        "Create a weather API tool that returns temperature and humidity",
        "创建一个天气API工具，返回温度和湿度信息",
        "Créer un outil API météo qui retourne la température et l'humidité",
        "天気APIツールを作成して、温度と湿度を返す"
    ]
    
    for desc in multilingual_descriptions:
        lang = "EN" if desc.startswith("Create") else "ZH" if "创建" in desc else "FR" if "Créer" in desc else "JP"
        print(f"   [{lang}] {desc[:50]}...")
    
    # 复杂场景演示
    print("\n🎭 复杂场景支持:")
    complex_scenarios = [
        "微服务架构中的用户认证和授权系统",
        "实时数据流处理和分析平台",
        "多租户SaaS应用的计费和订阅管理",
        "AI驱动的内容推荐引擎",
        "区块链智能合约交互工具"
    ]
    
    for scenario in complex_scenarios:
        print(f"   🔧 {scenario}")
    
    # 领域特定工具
    print("\n🏢 领域特定工具:")
    domain_tools = [
        ("金融", "风险评估、投资组合分析、交易执行"),
        ("医疗", "病历管理、诊断辅助、药物相互作用检查"),
        ("教育", "学习进度跟踪、个性化推荐、考试评估"),
        ("电商", "库存管理、价格优化、客户行为分析"),
        ("物流", "路线优化、货物跟踪、配送调度")
    ]
    
    for domain, tools in domain_tools:
        print(f"   📊 {domain}: {tools}")

if __name__ == "__main__":
    print("启动自然语言工具生成演示...")
    asyncio.run(demo_natural_language_generation())
    asyncio.run(demo_advanced_nl_features())