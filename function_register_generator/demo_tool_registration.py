#!/usr/bin/env python3
"""
工具注册端点演示脚本
"""
import asyncio
import json
from datetime import datetime

async def demo_tool_registration():
    """演示工具注册功能"""
    print("=== 工具注册平台演示 ===")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 导入必要的模块
    try:
        from backend.routers.tools import (
            register_tool, register_curl_tool, register_python_tool,
            register_mcp_tools, register_natural_language_tool,
            list_tools, get_tool, update_tool, delete_tool,
            ToolRegistrationRequest, CurlRegistrationRequest,
            PythonRegistrationRequest, MCPImportRequest,
            NaturalLanguageToolRequest
        )
        print("✅ 成功导入工具注册模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    print("\n" + "="*50)
    print("1. 基础工具注册演示")
    print("="*50)
    
    # 1. 基础工具注册
    basic_request = ToolRegistrationRequest(
        displayName="天气查询API",
        category="weather",
        transport="http",
        endpoint="https://api.openweathermap.org/data/2.5/weather",
        httpMethod="GET",
        description="获取指定城市的天气信息",
        inputSchema={
            "type": "object",
            "properties": {
                "q": {"type": "string", "description": "城市名称"},
                "appid": {"type": "string", "description": "API密钥"}
            },
            "required": ["q", "appid"]
        }
    )
    
    try:
        result = await register_tool(basic_request)
        print(f"✅ 基础工具注册成功:")
        print(f"   工具ID: {result['tool_id']}")
        print(f"   消息: {result['message']}")
        print(f"   传输方式: {result['tool_definition']['transport']}")
    except Exception as e:
        print(f"❌ 基础工具注册失败: {e}")
    
    print("\n" + "="*50)
    print("2. cURL 工具注册演示")
    print("="*50)
    
    # 2. cURL 工具注册
    curl_request = CurlRegistrationRequest(
        displayName="GitHub API 用户信息",
        curl="curl -H 'Accept: application/vnd.github.v3+json' https://api.github.com/users/octocat",
        description="获取 GitHub 用户信息"
    )
    
    try:
        result = await register_curl_tool(curl_request)
        print(f"✅ cURL 工具注册成功:")
        print(f"   工具ID: {result['tool_id']}")
        print(f"   解析的URL: {result['parsed_curl']['url']}")
        print(f"   HTTP方法: {result['parsed_curl']['method']}")
    except Exception as e:
        print(f"❌ cURL 工具注册失败: {e}")
    
    print("\n" + "="*50)
    print("3. Python 工具注册演示")
    print("="*50)
    
    # 3. Python 工具注册
    python_code = '''
def calculate_bmi(weight: float, height: float) -> dict:
    """
    计算身体质量指数(BMI)
    
    Args:
        weight: 体重(公斤)
        height: 身高(米)
    
    Returns:
        包含BMI值和健康状态的字典
    """
    if height <= 0 or weight <= 0:
        raise ValueError("身高和体重必须大于0")
    
    bmi = weight / (height ** 2)
    
    if bmi < 18.5:
        status = "偏瘦"
    elif bmi < 24:
        status = "正常"
    elif bmi < 28:
        status = "偏胖"
    else:
        status = "肥胖"
    
    return {
        "bmi": round(bmi, 2),
        "status": status,
        "weight": weight,
        "height": height
    }
'''
    
    python_request = PythonRegistrationRequest(
        displayName="BMI计算器",
        script_content=python_code,
        entry_function="calculate_bmi",
        description="计算身体质量指数并评估健康状态"
    )
    
    try:
        result = await register_python_tool(python_request)
        print(f"✅ Python 工具注册成功:")
        print(f"   工具ID: {result['tool_id']}")
        print(f"   函数签名: {result['function_info']['signature']}")
        print(f"   参数数量: {len(result['function_info']['parameters'])}")
    except Exception as e:
        print(f"❌ Python 工具注册失败: {e}")
    
    print("\n" + "="*50)
    print("4. MCP 工具注册演示")
    print("="*50)
    
    # 4. MCP 工具注册
    mcp_request = MCPImportRequest(
        transport="stdio",
        command="uvx",
        args=["mcp-server-filesystem"],
        env={"ALLOWED_DIRECTORIES": "/tmp"}
    )
    
    try:
        result = await register_mcp_tools(mcp_request)
        print(f"✅ MCP 工具注册成功:")
        print(f"   注册工具数量: {len(result['registered_tools'])}")
        for tool in result['registered_tools']:
            print(f"   - {tool['toolId']}: {tool['displayName']}")
    except Exception as e:
        print(f"❌ MCP 工具注册失败: {e}")
    
    print("\n" + "="*50)
    print("5. 自然语言工具注册演示")
    print("="*50)
    
    # 5. 自然语言工具注册
    natural_request = NaturalLanguageToolRequest(
        description="创建一个可以生成随机密码的工具，支持指定长度和字符类型",
        test_inputs={"length": 12, "include_symbols": True},
        test_outputs={"password": "Kx9#mP2$vL8@"},
        category="security"
    )
    
    try:
        result = await register_natural_language_tool(natural_request)
        print(f"✅ 自然语言工具注册成功:")
        print(f"   工具ID: {result['tool_id']}")
        print(f"   置信度: {result['confidence']}")
        print(f"   建议数量: {len(result['suggestions'])}")
        for suggestion in result['suggestions']:
            print(f"   - {suggestion}")
    except Exception as e:
        print(f"❌ 自然语言工具注册失败: {e}")
    
    print("\n" + "="*50)
    print("6. 工具管理演示")
    print("="*50)
    
    # 6. 工具列表查询
    try:
        result = await list_tools(limit=5)
        print(f"✅ 工具列表查询成功:")
        print(f"   总数: {result['total']}")
        print(f"   返回: {len(result['tools'])} 个工具")
        for tool in result['tools'][:3]:  # 只显示前3个
            print(f"   - {tool.get('toolId', 'N/A')}: {tool.get('displayName', 'N/A')}")
    except Exception as e:
        print(f"❌ 工具列表查询失败: {e}")
    
    # 7. 工具详情查询
    try:
        # 假设有一个工具ID
        tool_id = "http.天气查询api"
        result = await get_tool(tool_id)
        print(f"✅ 工具详情查询成功:")
        print(f"   工具名称: {result.get('displayName', 'N/A')}")
        print(f"   分类: {result.get('category', 'N/A')}")
        print(f"   传输方式: {result.get('transport', 'N/A')}")
    except Exception as e:
        print(f"❌ 工具详情查询失败: {e}")
    
    print("\n" + "="*50)
    print("7. 统计信息")
    print("="*50)
    
    # 统计信息
    try:
        from backend.routers.tools import get_categories, get_transports
        
        categories_result = await get_categories()
        transports_result = await get_transports()
        
        print(f"✅ 统计信息:")
        print(f"   支持的分类: {len(categories_result['categories'])} 个")
        print(f"   支持的传输方式: {len(transports_result['transports'])} 个")
        
        print(f"\n   分类列表:")
        for category in categories_result['categories']:
            print(f"   - {category}")
        
        print(f"\n   传输方式:")
        for transport in transports_result['transports']:
            print(f"   - {transport['type']}: {transport['name']}")
    except Exception as e:
        print(f"❌ 统计信息获取失败: {e}")
    
    print("\n" + "="*50)
    print("演示完成")
    print("="*50)
    print("✅ 工具注册平台基础功能演示完成")
    print("📝 注意: 实际使用时需要配置数据库和相关服务")

async def demo_error_handling():
    """演示错误处理"""
    print("\n" + "="*50)
    print("错误处理演示")
    print("="*50)
    
    from backend.routers.tools import register_tool, ToolRegistrationRequest
    
    # 测试无效数据
    invalid_requests = [
        {
            "name": "缺少必填字段",
            "data": {"displayName": "测试"}  # 缺少 category 和 transport
        },
        {
            "name": "无效的传输方式",
            "data": {
                "displayName": "测试工具",
                "category": "test",
                "transport": "invalid_transport"
            }
        }
    ]
    
    for test_case in invalid_requests:
        try:
            request = ToolRegistrationRequest(**test_case["data"])
            result = await register_tool(request)
            print(f"❌ {test_case['name']}: 应该失败但成功了")
        except Exception as e:
            print(f"✅ {test_case['name']}: 正确捕获错误 - {type(e).__name__}")

if __name__ == "__main__":
    print("启动工具注册演示...")
    asyncio.run(demo_tool_registration())
    asyncio.run(demo_error_handling())