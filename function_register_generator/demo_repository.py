#!/usr/bin/env python3
"""
数据访问层演示脚本
展示仓库模式和查询构建器的功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from repositories.query_builder import (
    ToolQueryBuilder, create_search_query, create_tool_query
)
import json

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print('='*60)

def print_query(query_dict: dict, title: str = ""):
    """格式化打印查询"""
    if title:
        print(f"\n📋 {title}")
        print("-" * 40)
    
    print(json.dumps(query_dict, ensure_ascii=False, indent=2))

def demo_basic_queries():
    """演示基础查询构建"""
    print_section("基础查询构建演示")
    
    # 1. 简单文本搜索
    builder = ToolQueryBuilder()
    builder.search_text("天气预报")
    query = builder.build()
    print_query(query, "文本搜索查询")
    
    # 2. 分类筛选
    builder = ToolQueryBuilder()
    builder.by_category("weather").sort_by_updated()
    query = builder.build()
    print_query(query, "分类筛选查询")
    
    # 3. 传输方式筛选
    builder = ToolQueryBuilder()
    builder.by_transport("http").public_only()
    query = builder.build()
    print_query(query, "传输方式筛选查询")

def demo_complex_queries():
    """演示复杂查询构建"""
    print_section("复杂查询构建演示")
    
    # 1. 组合搜索
    builder = ToolQueryBuilder()
    builder.search_text("API 工具") \
           .by_category("api") \
           .by_transport("http") \
           .user_facing_only() \
           .pagination(page=2, page_size=10) \
           .sort_by_relevance() \
           .sort_by_updated() \
           .with_highlights()
    
    query = builder.build()
    print_query(query, "组合搜索查询")
    
    # 2. 相似工具查询
    sample_tool = {
        "toolId": "weather.api",
        "category": "weather",
        "capabilities": ["http", "sync"],
        "content": "天气预报 API 工具"
    }
    
    builder = ToolQueryBuilder()
    builder.similar_to(sample_tool).limit(5)
    query = builder.build()
    print_query(query, "相似工具查询")
    
    # 3. 最近工具查询
    builder = ToolQueryBuilder()
    builder.recent_tools(days=7) \
           .with_examples() \
           .sort_by_created()
    
    query = builder.build()
    print_query(query, "最近工具查询")

def demo_aggregation_queries():
    """演示聚合查询"""
    print_section("聚合查询演示")
    
    # 1. 分类统计
    builder = ToolQueryBuilder()
    builder.with_category_agg(size=20)
    query = builder.build()
    print_query(query, "分类统计聚合")
    
    # 2. 传输方式统计
    builder = ToolQueryBuilder()
    builder.with_transport_agg(size=10)
    query = builder.build()
    print_query(query, "传输方式统计聚合")
    
    # 3. 综合统计
    builder = ToolQueryBuilder()
    builder.with_stats_agg()
    query = builder.build()
    print_query(query, "综合统计聚合")

def demo_search_scenarios():
    """演示实际搜索场景"""
    print_section("实际搜索场景演示")
    
    # 场景1：用户搜索天气相关工具
    print("\n🌤️ 场景1：用户搜索天气相关工具")
    query = create_search_query(
        text="天气 weather",
        category="weather",
        user_facing=True,
        page=1,
        page_size=5
    )
    print_query(query, "天气工具搜索")
    
    # 场景2：开发者查找 HTTP API 工具
    print("\n🔗 场景2：开发者查找 HTTP API 工具")
    query = create_search_query(
        text="API",
        transport="http",
        visibility="public",
        page=1,
        page_size=10
    )
    print_query(query, "HTTP API 工具搜索")
    
    # 场景3：管理员查看内部工具
    print("\n👨‍💼 场景3：管理员查看内部工具")
    query = create_search_query(
        text="",
        visibility="internal",
        page=1,
        page_size=20
    )
    print_query(query, "内部工具查看")
    
    # 场景4：查找 Python 脚本工具
    print("\n🐍 场景4：查找 Python 脚本工具")
    query = create_search_query(
        text="python script",
        transport="python",
        page=1,
        page_size=8
    )
    print_query(query, "Python 脚本工具搜索")

def demo_advanced_features():
    """演示高级功能"""
    print_section("高级功能演示")
    
    # 1. 自定义排序
    builder = ToolQueryBuilder()
    from repositories.query_builder import SortOrder
    builder.search_text("工具") \
           .sort("displayName", SortOrder.ASC) \
           .sort("createdTime", SortOrder.DESC)
    
    query = builder.build()
    print_query(query, "自定义排序查询")
    
    # 2. 字段筛选
    builder = ToolQueryBuilder()
    builder.search_text("API") \
           .source(["toolId", "displayName", "category", "descriptionUser"])
    
    query = builder.build()
    print_query(query, "字段筛选查询")
    
    # 3. 最小相关性分数
    builder = ToolQueryBuilder()
    builder.search_text("天气") \
           .minimum_score(0.5)
    
    query = builder.build()
    print_query(query, "最小相关性分数查询")
    
    # 4. 范围查询
    builder = ToolQueryBuilder()
    builder.filter_range("createdTime", gte="2024-01-01")
    
    query = builder.build()
    print_query(query, "时间范围查询")

def demo_query_optimization():
    """演示查询优化技巧"""
    print_section("查询优化技巧演示")
    
    # 1. 使用过滤器而不是查询（性能更好）
    print("\n⚡ 优化技巧1：使用过滤器")
    
    # 不推荐：在查询中使用 term
    builder1 = ToolQueryBuilder()
    builder1.term("category", "weather")
    query1 = builder1.build()
    print_query(query1, "不推荐：查询中使用 term")
    
    # 推荐：使用过滤器
    builder2 = ToolQueryBuilder()
    builder2.filter("category", "weather")
    query2 = builder2.build()
    print_query(query2, "推荐：使用过滤器")
    
    # 2. 合理使用 boost
    print("\n🚀 优化技巧2：合理使用 boost")
    builder = ToolQueryBuilder()
    builder.search_text("API 工具", boost_name=3.0, boost_description=2.0, boost_aliases=2.5)
    query = builder.build()
    print_query(query, "使用 boost 优化相关性")
    
    # 3. 分页优化
    print("\n📄 优化技巧3：分页优化")
    builder = ToolQueryBuilder()
    builder.search_text("工具") \
           .pagination(page=1, page_size=20) \
           .source(["toolId", "displayName", "category"])  # 只返回必要字段
    
    query = builder.build()
    print_query(query, "分页优化查询")

def main():
    """主演示函数"""
    print("🔧 数据访问层功能演示")
    print("=" * 60)
    print("展示仓库模式和查询构建器的强大功能")
    
    try:
        demo_basic_queries()
        demo_complex_queries()
        demo_aggregation_queries()
        demo_search_scenarios()
        demo_advanced_features()
        demo_query_optimization()
        
        print_section("演示完成")
        print("🎉 数据访问层功能演示完成！")
        print("\n💡 核心特性:")
        print("- 🏗️  仓库模式：统一的数据访问接口")
        print("- 🔍 查询构建器：链式调用，灵活组合")
        print("- 📊 聚合查询：统计分析功能")
        print("- 🚀 性能优化：缓存、过滤器、字段筛选")
        print("- 🎯 场景化：针对实际业务需求设计")
        print("- 🔧 可扩展：模块化设计，易于扩展")
        
        print("\n🔗 与其他组件的集成:")
        print("- 📡 API 路由：提供统一的数据访问")
        print("- 🤖 LLM 服务：智能搜索和推荐")
        print("- 🔒 沙盒验证：工具可用性检查")
        print("- 📈 监控统计：性能指标收集")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()