# 🔧 工具注册平台

一个现代化的企业内部工具注册和发现平台，支持多种注册方式、智能补全、沙盒验证和语义搜索。

## ✨ 核心特性

- 🚀 **多种注册方式**: 表单、JSON、cURL、Python 脚本、MCP 导入
- 🤖 **智能补全**: 集成本地 LM Studio (qwen/qwen3-30b-a3b-2507) 自动推断工具元数据
- 🔒 **安全沙盒**: 隔离环境验证工具可用性
- 🔍 **智能搜索**: 关键词 + 语义搜索，快速发现工具
- 💬 **零代码支持**: 自然语言描述生成完整工具定义
- 🎨 **优雅界面**: Flutter Web 响应式设计
- 🔌 **大模型友好**: 完美适配 Function Calling

## 🏗️ 技术架构

```
Frontend (Flutter Web) ←→ Backend (FastAPI) ←→ Elasticsearch + Redis
                                ↓
                        Docker Sandbox + LLM API
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保已安装 Docker 和 Docker Compose
docker --version
docker-compose --version
```

### 2. 启动服务

```bash
# 一键启动所有服务
./start.sh
```

### 3. 访问服务

- 🌐 **API 文档**: http://localhost:8000/docs
- 📊 **后端 API**: http://localhost:8000
- 🎨 **前端界面**: http://localhost:3000 (开发中)
- 🔍 **Elasticsearch**: http://localhost:9200

### 4. 配置 LM Studio (可选)

```bash
# 1. 下载并启动 LM Studio
# 2. 加载模型: qwen/qwen3-30b-a3b-2507
# 3. 启动本地服务器 (默认端口 1234)
```

### 5. 运行演示

```bash
# 安装 Python 依赖
pip install requests

# 运行基础功能演示
python3 demo.py

# 运行 LLM 集成演示
python3 demo_llm.py
```

## 📋 Mock 数据展示

系统内置了丰富的 Mock 数据，包括：

### 🌤️ 天气工具
```json
{
  "toolId": "weather.openmeteo.forecast",
  "displayName": "天气预报查询",
  "category": "weather",
  "runtime": {
    "transport": "http",
    "endpoint": "https://api.open-meteo.com/v1/forecast"
  }
}
```

### ⏰ 时间工具
```json
{
  "toolId": "time.current.get", 
  "displayName": "获取当前时间",
  "category": "utility",
  "runtime": {
    "transport": "python",
    "entry_function": "now"
  }
}
```

### 🌐 翻译工具
```json
{
  "toolId": "translate.text.google",
  "displayName": "文本翻译服务", 
  "category": "nlp",
  "runtime": {
    "transport": "http",
    "endpoint": "https://translation.googleapis.com/language/translate/v2"
  }
}
```

### 💾 数据库工具
```json
{
  "toolId": "database.query.sql",
  "displayName": "数据库查询工具",
  "category": "database", 
  "runtime": {
    "transport": "python",
    "entry_function": "query_database"
  }
}
```

### 📁 MCP 文件系统工具
```json
{
  "toolId": "mcp.filesystem.read",
  "displayName": "文件系统读取 (MCP)",
  "category": "filesystem",
  "runtime": {
    "transport": "stdio",
    "command": "uvx",
    "args": ["mcp-server-filesystem", "--path", "/tmp"]
  }
}
```

## 🔧 API 端点

### 工具管理
- `GET /api/v1/tools/` - 获取工具列表
- `GET /api/v1/tools/{tool_id}` - 获取工具详情
- `POST /api/v1/tools/register` - 基础工具注册
- `POST /api/v1/tools/register-from-curl` - 从 cURL 智能注册
- `POST /api/v1/tools/register-python` - Python 脚本注册
- `POST /api/v1/tools/import-mcp` - MCP 服务器导入
- `POST /api/v1/tools/generate-from-description` - 自然语言生成工具
- `POST /api/v1/tools/smart-complete` - 智能补全元数据

### 搜索发现
- `GET /api/v1/search/?q=关键词` - 关键词搜索
- `POST /api/v1/search/` - 高级搜索
- `GET /api/v1/search/suggestions` - 搜索建议
- `GET /api/v1/search/categories` - 获取分类
- `GET /api/v1/search/stats` - 搜索统计

### 沙盒验证
- `POST /api/v1/sandbox/validate-http` - HTTP 工具验证
- `POST /api/v1/sandbox/validate-python` - Python 脚本验证
- `POST /api/v1/sandbox/validate-mcp` - MCP 服务器验证
- `GET /api/v1/sandbox/status` - 沙盒状态

## 💡 使用示例

### 1. 基础工具注册

```bash
curl -X POST "http://localhost:8000/api/v1/tools/register" \
  -H "Content-Type: application/json" \
  -d '{
    "displayName": "我的 API 工具",
    "category": "api",
    "transport": "http",
    "endpoint": "https://api.example.com/data",
    "description": "获取示例数据的 API"
  }'
```

### 2. 从 cURL 智能注册

```bash
curl -X POST "http://localhost:8000/api/v1/tools/register-from-curl" \
  -H "Content-Type: application/json" \
  -d '{
    "displayName": "GitHub API",
    "curl": "curl -H \"Accept: application/vnd.github.v3+json\" https://api.github.com/users/octocat",
    "description": "获取 GitHub 用户信息"
  }'
```

### 3. 搜索工具

```bash
# 关键词搜索
curl "http://localhost:8000/api/v1/search/?q=天气&limit=5"

# 按分类筛选
curl "http://localhost:8000/api/v1/tools/?category=weather&limit=10"
```

### 4. 自然语言工具生成

```bash
curl -X POST "http://localhost:8000/api/v1/tools/generate-from-description" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "我需要一个计算两个数字和的工具",
    "test_inputs": {"a": 5, "b": 3},
    "test_outputs": {"sum": 8},
    "category": "math"
  }'
```

### 5. 沙盒验证

```bash
curl -X POST "http://localhost:8000/api/v1/sandbox/validate-http" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://httpbin.org/json",
    "method": "GET",
    "timeout": 10
  }'
```

## 🤖 LLM 集成亮点

### 本地大语言模型支持
- 🧠 **智能分析**: 自动解析 cURL 命令，推断 API 结构
- 💬 **自然语言**: 用户描述 → 完整工具定义
- 🔧 **智能补全**: 自动生成描述、分类、示例
- 📝 **Schema 推断**: 从示例数据生成 JSON Schema
- 🎯 **零代码体验**: 非技术用户也能创建工具

### 完整的工具生命周期
- ✅ **注册阶段**: 表单验证、LLM 智能补全、沙盒验证
- ✅ **发现阶段**: 多维度搜索、分类筛选、相关推荐  
- ✅ **使用阶段**: 调用示例、错误处理、性能监控

### 丰富的元数据
- 📊 **运行时信息**: 传输方式、端点、认证、限流
- 📝 **Schema 定义**: 输入输出结构、类型约束、示例
- 🔧 **快速启动**: cURL、Python、MCP 调用示例
- 📈 **性能指标**: 延迟、成本、容错等级

### 真实的业务场景
- 🌤️ **外部 API**: 天气、翻译等第三方服务集成
- 🐍 **Python 脚本**: 时间、数据处理等本地计算
- 💾 **数据库操作**: SQL 查询、数据分析
- 📁 **MCP 协议**: 文件系统、工具链集成

## 🔄 开发工作流

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend

# 重启服务
docker-compose restart backend

# 停止服务
docker-compose down

# 清理数据
docker-compose down -v
```

## 📚 项目结构

```
.
├── backend/                 # FastAPI 后端
│   ├── main.py             # 应用入口
│   ├── config.py           # 配置管理
│   ├── database.py         # 数据库连接
│   ├── mock_data.py        # Mock 数据
│   └── routers/            # API 路由
├── frontend/               # Flutter Web 前端 (开发中)
├── docker-compose.yml      # 服务编排
├── start.sh               # 启动脚本
├── demo.py                # 功能演示
└── README.md              # 项目文档
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 提交 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

🎉 **开始探索工具注册平台的强大功能吧！**